# Sentry Integration Guide

This document provides information on how Sentry is integrated into the fedha_mobileapp application for error tracking and performance monitoring.

## Overview

Sentry is used to:
- Track and report JavaScript errors
- Monitor app performance
- Capture user information for better error context
- Track user sessions

## Configuration

Sentry is initialized in `app/app.tsx` with the following configuration:

```typescript
Sentry.init({
  dsn: "https://<EMAIL>/4509063576748032",
  // Enable auto session tracking to track app crashes
  enableAutoSessionTracking: true,
  // Session closes after app is 10 seconds in the background
  sessionTrackingIntervalMillis: 10000,
  // Don't send events when in development
  enabled: !__DEV__,
  // Adds more context data to events (IP address, cookies, user, etc.)
  sendDefaultPii: true,
  // Set tracesSampleRate to 1.0 to capture 100% of transactions for tracing
  // We recommend adjusting this value in production (0.2 = 20% of transactions)
  tracesSampleRate: __DEV__ ? 1.0 : 0.2,
  // profilesSampleRate is relative to tracesSampleRate
  // Here, we'll capture profiles for 100% of sampled transactions
  profilesSampleRate: 1.0,
  // Enable performance monitoring
  integrations: [
    // Add any integrations you need
  ],
})
```

## User Identification

User information is automatically sent to Sentry when a user logs in. This is handled in the `AuthenticationStore.ts` file:

```typescript
// Set user information in Sentry for error tracking
setSentryUser({
  id: user.id.toString(),
  email: user.email || undefined,
  username: `${user.first_name} ${user.last_name}`,
  phone: user.phone_number,
})
```

When a user logs out, the user information is cleared from Sentry:

```typescript
// Clear Sentry user information
clearSentryUser()
```

## Utility Functions

The following utility functions are available in `app/utils/sentryUtils.ts`:

- `setSentryUser(user)`: Set user information in Sentry
- `clearSentryUser()`: Clear user information from Sentry
- `setSentryContext(context)`: Set additional context information
- `captureException(error, context)`: Manually capture an exception
- `captureMessage(message, level)`: Manually capture a message
- `startTransaction(name, operation)`: Start a performance transaction

## Error Boundaries

The app uses Sentry's TouchEventBoundary to capture unhandled touch events:

```tsx
<ErrorBoundary catchErrors={Config.catchErrors}>
  <Sentry.TouchEventBoundary>
    {/* App components */}
  </Sentry.TouchEventBoundary>
</ErrorBoundary>
```

## Manual Error Reporting

You can manually report errors and messages to Sentry using the utility functions:

```typescript
import { captureException, captureMessage } from '@/utils/sentryUtils';

try {
  // Some code that might throw an error
} catch (error) {
  captureException(error, {
    extra: {
      someContext: 'Additional information'
    }
  });
}

// Or send a message
captureMessage('Something happened', 'info');
```

## Performance Monitoring

You can manually create performance transactions to measure specific operations:

```typescript
import { startTransaction } from '@/utils/sentryUtils';

const transaction = startTransaction('API Call', 'http');
// Perform the operation
// ...
transaction.finish();
```

## Viewing Errors in Sentry Dashboard

You can view all captured errors and performance data in the Sentry dashboard at:
https://fedha.sentry.io/projects/fedha_mobileapp/

## Verifying Sentry Setup

To verify that Sentry is properly set up and receiving events:

1. Run the app in development mode
2. The app will automatically send a test event on initialization
3. In development mode, you can also press the "Send Test Event to Sentry" button on the Dashboard screen
4. Check the Sentry dashboard to confirm that events are being received
5. If you don't see events in the dashboard, check the console logs for any Sentry-related errors

Once you've confirmed that events are being received, you can:

1. Set `enabled: !__DEV__` in the Sentry.init configuration to disable Sentry in development mode
2. Set `debug: false` to disable debug logs
3. Remove the test button from the Dashboard screen

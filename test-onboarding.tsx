import React from "react"
import { View, StyleSheet, TouchableOpacity } from "react-native"
import { Text, Button } from "./app/components"
import { OnboardingScreen } from "./app/screens/public/OnboardingScreen"

// Test component to demonstrate onboarding usage
export const TestOnboarding = () => {
  const [showOnboarding, setShowOnboarding] = React.useState(false)

  // Mock navigation object for testing
  const mockNavigation = {
    replace: (screenName: string) => {
      console.log(`Navigation: Replacing with ${screenName}`)
      setShowOnboarding(false)
    },
    navigate: (screenName: string) => {
      console.log(`Navigation: Navigating to ${screenName}`)
    },
    goBack: () => {
      console.log("Navigation: Going back")
      setShowOnboarding(false)
    },
  }

  if (showOnboarding) {
    return (
      <OnboardingScreen 
        navigation={mockNavigation as any}
        route={{} as any}
      />
    )
  }

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Onboarding Test</Text>
      
      <Text style={styles.description}>
        Click the button below to test the new WhatsApp status-inspired onboarding experience.
      </Text>

      <Button
        text="Start Onboarding"
        onPress={() => setShowOnboarding(true)}
        style={styles.button}
      />

      <View style={styles.features}>
        <Text style={styles.featuresTitle}>Features to Test:</Text>
        <Text style={styles.feature}>• WhatsApp-like progress bars</Text>
        <Text style={styles.feature}>• Auto-advance timer (4s, 3.5s, 3.5s, 3s)</Text>
        <Text style={styles.feature}>• Tap left side to go back</Text>
        <Text style={styles.feature}>• Tap right side to go forward</Text>
        <Text style={styles.feature}>• Skip button (top-right)</Text>
        <Text style={styles.feature}>• Get Started button on final slide</Text>
        <Text style={styles.feature}>• Smooth animations and transitions</Text>
        <Text style={styles.feature}>• Dynamic background colors</Text>
      </View>

      <View style={styles.instructions}>
        <Text style={styles.instructionsTitle}>Test Instructions:</Text>
        <Text style={styles.instruction}>
          1. Tap "Start Onboarding" to begin
        </Text>
        <Text style={styles.instruction}>
          2. Watch the auto-progression or tap to navigate
        </Text>
        <Text style={styles.instruction}>
          3. Try the skip button to exit early
        </Text>
        <Text style={styles.instruction}>
          4. Complete the flow to see the final CTA
        </Text>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: "#f5f5f5",
    justifyContent: "center",
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    textAlign: "center",
    marginBottom: 16,
    color: "#333",
  },
  description: {
    fontSize: 16,
    textAlign: "center",
    marginBottom: 24,
    color: "#666",
    lineHeight: 24,
  },
  button: {
    marginBottom: 32,
  },
  features: {
    backgroundColor: "#fff",
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  featuresTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 12,
    color: "#333",
  },
  feature: {
    fontSize: 14,
    marginBottom: 4,
    color: "#555",
  },
  instructions: {
    backgroundColor: "#e8f4fd",
    padding: 16,
    borderRadius: 8,
  },
  instructionsTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 12,
    color: "#1976d2",
  },
  instruction: {
    fontSize: 14,
    marginBottom: 4,
    color: "#1565c0",
  },
})

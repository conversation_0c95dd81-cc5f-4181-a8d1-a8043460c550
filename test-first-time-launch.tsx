import React from "react"
import { View, StyleSheet, Alert } from "react-native"
import { Text, Button } from "./app/components"
import { 
  isFirstTimeLaunch, 
  markOnboardingCompleted, 
  resetOnboardingState, 
  getOnboardingState 
} from "./app/utils/onboardingUtils"

/**
 * Test component for first-time launch detection
 * Use this to test and debug the onboarding flow
 */
export const TestFirstTimeLaunch = () => {
  const [currentState, setCurrentState] = React.useState(getOnboardingState())
  const [isFirstTime, setIsFirstTime] = React.useState(isFirstTimeLaunch())

  const refreshState = () => {
    setCurrentState(getOnboardingState())
    setIsFirstTime(isFirstTimeLaunch())
  }

  const handleMarkCompleted = () => {
    const success = markOnboardingCompleted()
    if (success) {
      Alert.alert("Success", "Onboarding marked as completed!")
      refreshState()
    } else {
      Alert.alert("Error", "Failed to mark onboarding as completed")
    }
  }

  const handleReset = () => {
    Alert.alert(
      "Reset Onboarding State",
      "This will make the app show onboarding again on next launch. Continue?",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Reset",
          style: "destructive",
          onPress: () => {
            resetOnboardingState()
            Alert.alert("Reset Complete", "Onboarding state has been reset")
            refreshState()
          },
        },
      ]
    )
  }

  const getStatusColor = () => {
    return isFirstTime ? "#ff6b6b" : "#51cf66"
  }

  const getStatusText = () => {
    return isFirstTime ? "Will show onboarding" : "Will skip onboarding"
  }

  return (
    <View style={styles.container}>
      <Text style={styles.title}>First-Time Launch Test</Text>
      
      <View style={styles.statusContainer}>
        <Text style={styles.statusLabel}>Current Status:</Text>
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor() }]}>
          <Text style={styles.statusText}>{currentState.toUpperCase()}</Text>
        </View>
      </View>

      <View style={styles.infoContainer}>
        <Text style={styles.infoTitle}>Debug Information</Text>
        <Text style={styles.infoText}>
          • Is First Time: {isFirstTime ? "YES" : "NO"}
        </Text>
        <Text style={styles.infoText}>
          • Onboarding State: {currentState}
        </Text>
        <Text style={styles.infoText}>
          • Next App Launch: {getStatusText()}
        </Text>
      </View>

      <View style={styles.buttonContainer}>
        <Button
          text="Mark as Completed"
          onPress={handleMarkCompleted}
          style={[styles.button, styles.completeButton]}
          disabled={!isFirstTime}
        />

        <Button
          text="Reset State"
          onPress={handleReset}
          style={[styles.button, styles.resetButton]}
        />

        <Button
          text="Refresh Status"
          onPress={refreshState}
          style={[styles.button, styles.refreshButton]}
        />
      </View>

      <View style={styles.instructionsContainer}>
        <Text style={styles.instructionsTitle}>Testing Instructions:</Text>
        <Text style={styles.instruction}>
          1. Fresh install should show "PENDING" status
        </Text>
        <Text style={styles.instruction}>
          2. Complete onboarding or tap "Mark as Completed"
        </Text>
        <Text style={styles.instruction}>
          3. Status should change to "COMPLETED"
        </Text>
        <Text style={styles.instruction}>
          4. Close and reopen app - should skip onboarding
        </Text>
        <Text style={styles.instruction}>
          5. Use "Reset State" to test fresh install behavior
        </Text>
      </View>

      <View style={styles.warningContainer}>
        <Text style={styles.warningTitle}>⚠️ Important Notes:</Text>
        <Text style={styles.warningText}>
          • This test component is for development only
        </Text>
        <Text style={styles.warningText}>
          • Reset state will affect the actual app behavior
        </Text>
        <Text style={styles.warningText}>
          • Test on real devices for accurate results
        </Text>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: "#f8f9fa",
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    textAlign: "center",
    marginBottom: 24,
    color: "#2c3e50",
  },
  statusContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 24,
  },
  statusLabel: {
    fontSize: 18,
    fontWeight: "600",
    marginRight: 12,
    color: "#34495e",
  },
  statusBadge: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  statusText: {
    color: "white",
    fontWeight: "bold",
    fontSize: 14,
  },
  infoContainer: {
    backgroundColor: "white",
    padding: 16,
    borderRadius: 8,
    marginBottom: 24,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 12,
    color: "#2c3e50",
  },
  infoText: {
    fontSize: 14,
    marginBottom: 4,
    color: "#5a6c7d",
  },
  buttonContainer: {
    marginBottom: 24,
  },
  button: {
    marginBottom: 12,
  },
  completeButton: {
    backgroundColor: "#27ae60",
  },
  resetButton: {
    backgroundColor: "#e74c3c",
  },
  refreshButton: {
    backgroundColor: "#3498db",
  },
  instructionsContainer: {
    backgroundColor: "#e8f4fd",
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  instructionsTitle: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 12,
    color: "#1976d2",
  },
  instruction: {
    fontSize: 14,
    marginBottom: 4,
    color: "#1565c0",
  },
  warningContainer: {
    backgroundColor: "#fff3cd",
    padding: 16,
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: "#ffc107",
  },
  warningTitle: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 8,
    color: "#856404",
  },
  warningText: {
    fontSize: 14,
    marginBottom: 4,
    color: "#856404",
  },
})

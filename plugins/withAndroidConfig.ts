import { ConfigPlugin, withAndroidManifest } from "@expo/config-plugins"

const withAndroidConfig: ConfigPlugin = (config) => {
  return withAndroidManifest(config, async (config) => {
    const androidManifest = config.modResults

    // Ensure we're not applying both library and application plugins
    if (androidManifest.manifest.$) {
      androidManifest.manifest.$["xmlns:android"] = "http://schemas.android.com/apk/res/android"
      androidManifest.manifest.$["xmlns:tools"] = "http://schemas.android.com/tools"
    }

    return config
  })
}

export default withAndroidConfig

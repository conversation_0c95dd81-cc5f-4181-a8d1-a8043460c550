/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable no-restricted-imports */
/* eslint-disable react-native/no-inline-styles */
import { useState } from "react"
import {
  StyleProp,
  TextStyle,
  View,
  ViewStyle,
  Modal,
  TouchableOpacity,
  Image,
  ImageStyle,
  ScrollView,
} from "react-native"
import { Text, Button, Icon, FencyTextInput, ChangeCurency } from "."
import { colors, spacing } from "@/theme"
import { Controller, useForm } from "react-hook-form"
import { useStores } from "@/store"
import React from "react"

export interface BuyAirtimeProps {
  /**
   * An optional style override useful for padding & margin.
   */
  style?: StyleProp<ViewStyle>
}

type NetworkType = {
  id: string
  name: string
  icon: string
}

type PackageType = {
  id: string
  name: string
}

const networks: NetworkType[] = [
  { id: "mtn", name: "MTN", icon: require("../../assets/images/logos/mtn.jpg") },
  { id: "vodacom", name: "VODACOM", icon: require("../../assets/images/logos/vodacom.png") },
  { id: "airtel", name: "AIRTEL", icon: require("../../assets/images/logos/airtel.jpg") },
  { id: "africel", name: "AFRICEL", icon: require("../../assets/images/logos/africel.png") },
  { id: "orange", name: "ORANGE", icon: require("../../assets/images/logos/orange.png") },
]

const packages: PackageType[] = [
  { id: "airtime", name: "Minute" },
  { id: "data", name: "Mega" },
  { id: "sms", name: "SMS" },
]

type FormData = {
  phoneNumber: string
  network: string
  package: string
  amount: string
}

export const BuyAirtime = (props: BuyAirtimeProps) => {
  const { style } = props
  const $styles = [$container, style]

  const {
    appsettings: { currency, getExchangeRate },
    fedhapochi: { currentBalance },
  } = useStores()

  const [showNetworkModal, setShowNetworkModal] = useState(false)
  const [showPackageModal, setShowPackageModal] = useState(false)
  const [showCurrencyModal, setShowCurrencyModal] = useState(false)
  const [selectedNetwork, setSelectedNetwork] = useState<NetworkType | null>(null)
  const [selectedPackage, setSelectedPackage] = useState<PackageType | null>(null)
  const [currentStep, setCurrentStep] = useState<number>(1) // Track the current step

  const {
    control,
    handleSubmit,
    watch,
    formState: { errors, isValid },
  } = useForm<FormData>({
    defaultValues: {
      phoneNumber: "",
      network: "",
      package: "",
      amount: "",
    },
    mode: "onChange",
  })

  // Watch the phone number field to enable/disable network selection
  const phoneNumber = watch("phoneNumber")

  const onSubmit = (data: FormData) => {
    console.log("Form data:", data)
    // Handle form submission here
    alert(
      `Buying ${selectedPackage?.name} for ${data.phoneNumber} on ${selectedNetwork?.name} network for ${data.amount} FC`,
    )
  }

  const handleNetworkSelect = (network: NetworkType) => {
    setSelectedNetwork(network)
    setShowNetworkModal(false)
    setCurrentStep(3) // Move to next step after selecting network
  }

  const handlePackageSelect = (pkg: PackageType) => {
    setSelectedPackage(pkg)
    setShowPackageModal(false)
  }

  // Check if phone number is valid to enable network selection
  const isPhoneNumberValid = () => {
    return /^[0-9]{9,10}$/.test(phoneNumber)
  }

  // Determine if the form is ready for submission
  const canSubmit = isValid && selectedNetwork && selectedPackage

  return (
    <>
      <View style={$styles}>
        {/* <FedhaPochiBar /> */}

        <View style={$contentContainer}>
          <View style={$formContainer}>
            <Controller
              control={control}
              name="phoneNumber"
              rules={{
                required: "Numéro de téléphone requis",
                pattern: {
                  value: /^[0-9]{9,10}$/,
                  message: "Numéro de téléphone invalide",
                },
              }}
              render={({ field: { onChange, value } }) => (
                <View style={$inputContainer}>
                  <FencyTextInput
                    value={value}
                    onChange={onChange}
                    maxLength={9}
                    inputname="Numéro de téléphone"
                    keyboardType="phone-pad"
                    placeholder="Saisir le numéro"
                    style={$input}
                  />
                  {errors.phoneNumber && (
                    <Text style={$errorText}>{errors.phoneNumber.message}</Text>
                  )}
                </View>
              )}
            />

            <TouchableOpacity
              style={[$selectorButton, !isPhoneNumberValid() && $disabledSelector]}
              onPress={() => isPhoneNumberValid() && setShowNetworkModal(true)}
            >
              <Text style={$selectorLabel}>Réseau Mobile</Text>
              <View style={$selectorContent}>
                {selectedNetwork ? (
                  <View style={$selectedOption}>
                    <Image source={selectedNetwork.icon as any} style={$logoImg} />
                    <Text style={$selectedText}>{selectedNetwork.name}</Text>
                  </View>
                ) : (
                  <Text style={[$placeholderText, !isPhoneNumberValid() && $disabledText]}>
                    {isPhoneNumberValid()
                      ? "Sélectionner un réseau"
                      : "Entrez d'abord le numéro de téléphone"}
                  </Text>
                )}
                <Icon icon="caretRight" size={12} color={colors.palette.neutral500} />
              </View>
              {errors.network && <Text style={$errorText}>{errors.network.message}</Text>}
            </TouchableOpacity>

            {currentStep >= 3 && (
              <>
                <TouchableOpacity
                  style={[$selectorButton, !selectedNetwork && $disabledSelector]}
                  onPress={() => selectedNetwork && setShowPackageModal(true)}
                >
                  <Text style={$selectorLabel}>Type de forfait</Text>
                  <View style={$selectorContent}>
                    {selectedPackage ? (
                      <Text style={$selectedText}>{selectedPackage.name}</Text>
                    ) : (
                      <Text style={[$placeholderText, !selectedNetwork && $disabledText]}>
                        {selectedNetwork
                          ? "Sélectionner un forfait"
                          : "Sélectionnez d'abord le réseau"}
                      </Text>
                    )}
                    <Icon icon="caretRight" size={12} color={colors.palette.neutral500} />
                  </View>
                  {errors.package && <Text style={$errorText}>{errors.package.message}</Text>}
                </TouchableOpacity>

                <Controller
                  control={control}
                  name="amount"
                  rules={{
                    required: "Le montant est obligatoire",
                    validate: (value) => {
                      const amount = parseFloat(value)
                      if (isNaN(amount)) return "Le montant doit être un nombre valide"

                      // Get exchange rate and convert amounts using API rates
                      if (currency === "USD") {
                        if (amount < 2) return "Le montant minimum est de 2 USD"
                        // Convert balance to USD using API exchange rate
                        const balanceInUSD = getExchangeRate("FC", "USD") * currentBalance
                        if (amount > balanceInUSD)
                          return `Solde insuffisant. Votre solde est de ${balanceInUSD.toFixed(2)} USD`
                      } else if (currency === "FC") {
                        if (amount < 500) return "Le montant minimum est de 5000 FC"
                        // Convert balance to FC using API exchange rate
                        const balanceInFC = getExchangeRate("USD", "FC") * currentBalance
                        if (amount > balanceInFC)
                          return `Solde insuffisant. Votre solde est de ${balanceInFC.toFixed(2)} FC`
                      }
                      return true
                    },
                  }}
                  render={({ field: { onChange, value } }) => (
                    <View style={$inputContainer}>
                      <FencyTextInput
                        value={value}
                        onChange={onChange}
                        inputname="Montant"
                        keyboardType="numeric"
                        placeholder="Saisir le montant"
                        style={$input}
                        LeftIcon={
                          currency === "FC" || (currency as string) === "Congolese Franc"
                            ? "FC"
                            : "dollar"
                        }
                        leftonPress={() => setShowCurrencyModal(!showCurrencyModal)}
                      />
                      {errors.amount && <Text style={$errorText}>{errors.amount.message}</Text>}
                    </View>
                  )}
                />
              </>
            )}
          </View>
        </View>

        <View style={$buttonContainer}>
          <Button
            preset="reversed"
            text="Acheter"
            style={[$buttonStyle, !canSubmit && { opacity: 0.5 }]}
            disabled={!canSubmit}
            onPress={handleSubmit(onSubmit)}
          />
        </View>

        {/* Network Selection Modal */}
        <Modal
          visible={showNetworkModal}
          transparent={true}
          animationType="slide"
          onRequestClose={() => setShowNetworkModal(false)}
        >
          <View style={$modalOverlay}>
            <View style={$modalContent}>
              <Text style={$modalTitle}>Sélectionner un réseau</Text>
              <ScrollView>
                {networks.map((network) => (
                  <TouchableOpacity
                    key={network.id}
                    style={$modalItem}
                    onPress={() => handleNetworkSelect(network)}
                  >
                    <Image source={network.icon as any} style={$logoImg} />
                    {/* <Icon icon={network.icon as any} size={24} /> */}
                    <Text style={$modalItemText}>{network.name}</Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
              <TouchableOpacity style={$closeButton} onPress={() => setShowNetworkModal(false)}>
                <Text style={$closeButtonText}>Fermer</Text>
              </TouchableOpacity>
            </View>
          </View>
        </Modal>

        {/* Package Selection Modal */}
        <Modal
          visible={showPackageModal}
          transparent={true}
          animationType="slide"
          onRequestClose={() => setShowPackageModal(false)}
        >
          <View style={$modalOverlay}>
            <View style={$modalContent}>
              <Text style={$modalTitle}>Sélectionner un forfait</Text>
              <ScrollView>
                {packages.map((pkg) => (
                  <TouchableOpacity
                    key={pkg.id}
                    style={$modalItem}
                    onPress={() => handlePackageSelect(pkg)}
                  >
                    <Text style={$modalItemText}>{pkg.name}</Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
              <TouchableOpacity style={$closeButton} onPress={() => setShowPackageModal(false)}>
                <Text style={$closeButtonText}>Fermer</Text>
              </TouchableOpacity>
            </View>
          </View>
        </Modal>
      </View>
      <ChangeCurency isVisible={showCurrencyModal} onClose={() => setShowCurrencyModal(false)} />
    </>
  )
}

const $container: ViewStyle = {
  flex: 1,
  width: "100%",
  height: "100%",
  display: "flex",
  flexDirection: "column",
  justifyContent: "space-between",
}

// Remove unused title style
// const $title: TextStyle = {
//   fontSize: 20,
//   fontWeight: "bold",
//   marginBottom: spacing.lg,
//   textAlign: "center",
// }

const $contentContainer: ViewStyle = {
  flex: 1,
  padding: spacing.md,
  paddingBottom: 0,
}

const $formContainer: ViewStyle = {
  gap: spacing.md,
}

const $inputContainer: ViewStyle = {
  marginBottom: spacing.xs,
}

const $input: ViewStyle = {
  borderWidth: 1,
  borderColor: colors.palette.neutral300,
  borderRadius: 12,
}

const $selectorButton: ViewStyle = {
  marginBottom: spacing.xs,
  borderWidth: 0.8,
  borderColor: colors.palette.neutral900,
  marginTop: spacing.md,
  paddingHorizontal: 15,
  borderRadius: 12,
}

const $disabledSelector: ViewStyle = {
  borderColor: colors.palette.neutral400,
  opacity: 0.7,
}

const $selectorLabel: TextStyle = {
  fontSize: 14,
  marginBottom: 4,
}

const $selectorContent: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
  paddingVertical: 9,
  height: 50,
}

const $selectedOption: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  gap: spacing.xs,
}

const $selectedText: TextStyle = {
  fontSize: 16,
}

const $placeholderText: TextStyle = {
  fontSize: 16,
  color: colors.palette.neutral500,
}

const $disabledText: TextStyle = {
  color: colors.palette.neutral400,
  fontSize: 14,
}

const $errorText: TextStyle = {
  color: colors.error,
  fontSize: 12,
  marginTop: 4,
}

const $buttonContainer: ViewStyle = {
  padding: spacing.md,
  paddingBottom: spacing.xl,
  width: "100%",
}

const $buttonStyle: ViewStyle = {
  backgroundColor: colors.palette.neutral900,
  borderRadius: 16,
  elevation: 3,
  paddingVertical: spacing.md,
  shadowColor: colors.palette.neutral900,
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
  shadowRadius: 4,
  width: "100%",
}

const $modalOverlay: ViewStyle = {
  flex: 1,
  backgroundColor: "rgba(0, 0, 0, 0.5)",
  justifyContent: "flex-end",
}

const $modalContent: ViewStyle = {
  backgroundColor: colors.palette.neutral100,
  borderTopLeftRadius: 20,
  borderTopRightRadius: 20,
  padding: spacing.lg,
  maxHeight: "70%",
}
const $logoImg: ImageStyle = {
  width: 34,
  height: 34,
  borderRadius: 5,
}

const $modalTitle: TextStyle = {
  fontSize: 18,
  fontWeight: "bold",
  marginBottom: spacing.md,
  textAlign: "center",
}

const $modalItem: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  padding: spacing.md,
  borderBottomWidth: 1,
  borderBottomColor: colors.palette.neutral200,
  gap: spacing.sm,
}

const $modalItemText: TextStyle = {
  fontSize: 16,
}

const $closeButton: ViewStyle = {
  marginTop: spacing.md,
  padding: spacing.sm,
  backgroundColor: colors.palette.neutral200,
  borderRadius: 8,
  alignItems: "center",
}

const $closeButtonText: TextStyle = {
  fontSize: 16,
  fontWeight: "bold",
}

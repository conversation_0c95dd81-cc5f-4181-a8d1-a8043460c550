/* eslint-disable react-native/no-inline-styles */
import {
  ImageStyle,
  StyleProp,
  TextStyle,
  View,
  ViewStyle,
  Image,
  Linking,
  Alert,
} from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import { colors, spacing, type ThemedStyle } from "@/theme"
import { Icon, Text } from "."
import Share, { Social, ShareSingleOptions } from "react-native-share"
// import RNFetchBlob from "rn-fetch-blob"
import RNBlobUtil from "react-native-blob-util"
import { useStores } from "@/store/rootStore"
import * as FileSystem from "react-native-fs"

export interface FedhaPochiWidgetProps {
  style?: StyleProp<ViewStyle>
}

/**
 * Describe your component here
 */

export const FedhaPochiWidget = (props: FedhaPochiWidgetProps) => {
  const { style } = props
  const $styles = [$container, style]
  const { themed } = useAppTheme()

  const {
    auth: { user },
    fedhapochi: { wallet },
  } = useStores()

  const userName = `${user?.first_name || ""} ${user?.last_name || ""}`.trim()

  const downloadQRImage = async () => {
    const qrImageUri = wallet?.qr_image
    if (!qrImageUri) return null

    try {
      const { dirs } = RNBlobUtil.fs
      const localPath = `${dirs.CacheDir}/walletQR_${Date.now()}.png`

      const response = await RNBlobUtil.config({
        fileCache: true,
        path: localPath,
      }).fetch("GET", qrImageUri)

      return response.path()
    } catch (error) {
      console.error("Error downloading QR image:", error)
      return null
    }
  }

  const saveToGallery = async () => {
    try {
      const qrImageUri = wallet?.qr_image
      if (!qrImageUri) return

      const timestamp = new Date().getTime()
      const fileName = `fedhapochi_qr_${timestamp}.png`
      const destPath = `${FileSystem.PicturesDirectoryPath}/${fileName}`

      await RNBlobUtil.config({ fileCache: true })
        .fetch("GET", qrImageUri)
        .then((resp) => FileSystem.moveFile(resp.path(), destPath))

      Alert.alert(
        "FedhaPochi QR Code",
        "Votre FedhaPochi QR Code a été enregistré dans la galerie avec succès !",
      )
    } catch (error) {
      console.error("Error saving to gallery:", error)
      alert("Failed to save QR Code to gallery")
    }
  }

  const shareViaWhatsApp = async () => {
    try {
      const localQRPath = await downloadQRImage()
      if (!localQRPath) {
        alert("Failed to prepare QR code for sharing")
        return
      }

      const message = `Bonjour,\n\nVoici mon identifiant FedhaPochi :\n\nNom: ${userName}\nID de Portefeuille: ${wallet?.wallet}\n\nVous pouvez utiliser ces informations pour m'envoyer de l'argent.`

      // Create WhatsApp URL with encoded message but without phone number
      const whatsappUrl = `whatsapp://send?text=${encodeURIComponent(message)}`

      // First check if WhatsApp is installed
      const canOpen = await Linking.canOpenURL(whatsappUrl)

      if (canOpen) {
        // If WhatsApp is installed, open it with the share intent
        await Share.shareSingle({
          title: "Share via WhatsApp",
          message: message,
          url: `file://${localQRPath}`,
          type: "image/png",
          social: "whatsapp" as Social,
          whatsAppNumber: undefined,
        } as ShareSingleOptions)
      } else {
        alert("WhatsApp n'est pas installé sur votre appareil")
      }
    } catch (error: any) {
      console.error("Error sharing via WhatsApp:", error)
      if (error.message.includes("User did not share")) {
        return // User cancelled sharing
      }
      alert("WhatsApp n'est pas installé")
    }
  }

  // const shareViaEmail = async () => {
  //   try {
  //     const localQRPath = await downloadQRImage()
  //     if (!localQRPath) {
  //       alert("Failed to prepare QR code for sharing")
  //       return
  //     }

  //     const subject = "Mon Portefeuille FedhaPochi"
  //     const body = `Bonjour,\n\nVoici mon identifiant FedhaPochi :\n\nNom: ${userName}\nID de Portefeuille: ${wallet?.wallet}\n\nVous pouvez utiliser ces informations pour m'envoyer de l'argent.`

  //     const shareOptions = {
  //       title: "Share via Email",
  //       subject: subject,
  //       message: body,
  //       url: `file://${localQRPath}`,
  //       type: "image/png",
  //       social: Share.Social.EMAIL,
  //     }

  //     await Share.shareSingle(shareOptions)
  //   } catch (error) {
  //     console.error("Error sharing via email:", error)
  //     alert("Impossible d'ouvrir l'application mail")
  //   }
  // }

  // const handleGlobalShare = async () => {
  //   try {
  //     const localQRPath = await downloadQRImage()
  //     if (!localQRPath) {
  //       alert("Failed to prepare QR code for sharing")
  //       return
  //     }

  //     const shareOptions = {
  //       title: "Mon Portefeuille FedhaPochi",
  //       message: `Bonjour,\n\nVoici mon identifiant FedhaPochi :\n\nNom: ${userName}\nID de Portefeuille: ${wallet?.wallet}\n\nVous pouvez scanner ce QR Code pour effectuer un paiement rapide.`,
  //       url: `file://${localQRPath}`,
  //       type: "image/png",
  //     }

  //     await Share.open(shareOptions)
  //   } catch (error) {
  //     console.error("Error sharing:", error)
  //     if (error.message.includes("User did not share")) {
  //       return // User cancelled sharing
  //     }
  //     alert("Error sharing QR code")
  //   }
  // }

  return (
    <View style={$styles}>
      <View style={$header}>
        <Text preset="subheading" style={themed($userName)}>
          {userName}
        </Text>
        {/* <Icon
          icon="share"
          color={colors.palette.neutral900}
          size={24}
          onPress={handleGlobalShare}
        /> */}
      </View>

      <View style={$qrContainer}>
        <View style={$walletIdContainer}>
          <Text style={themed($walletLabel)}>FedhaPochi ID:</Text>
          <Text preset="subheading" style={themed($walletId)}>
            {wallet?.wallet}
          </Text>
        </View>

        <Image source={{ uri: wallet?.qr_image }} style={$qrImage} resizeMode="contain" />

        <View style={$shareActions}>
          <Icon
            icon="whatsapp"
            color={"#128c7e"}
            size={28}
            onPress={shareViaWhatsApp}
            containerStyle={$iconButton}
          />
          <Icon
            icon="donwload"
            color={colors.palette.neutral800}
            size={28}
            onPress={saveToGallery}
            containerStyle={$iconButton}
          />
        </View>
      </View>
    </View>
  )
}

const $container: ViewStyle = {
  justifyContent: "center",
  marginTop: spacing.lg,
}

const $header: ViewStyle = {
  flexDirection: "row",
  justifyContent: "center",
  alignItems: "center",
  marginBottom: spacing.sm,
  paddingHorizontal: spacing.sm,
}

const $qrContainer: ViewStyle = {
  backgroundColor: colors.palette.neutral100,
  borderRadius: 16,
  padding: spacing.md,
  alignItems: "center",
  shadowColor: colors.palette.neutral900,
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
  shadowRadius: 4,
  elevation: 3,
}

const $walletIdContainer: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  marginBottom: spacing.sm,
}

const $qrImage: ImageStyle = {
  width: 200,
  height: 200,
  borderRadius: 12,
  marginVertical: spacing.md,
}

const $shareActions: ViewStyle = {
  flexDirection: "row",
  justifyContent: "center",
  gap: spacing.lg,
  marginTop: spacing.sm,
}

const $iconButton: ViewStyle = {
  padding: spacing.xs,
  borderRadius: 8,
  backgroundColor: colors.palette.neutral200,
}

const $userName: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontFamily: typography.primary.medium,
  fontSize: 20,
  color: colors.text,
})

const $walletLabel: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontFamily: typography.primary.normal,
  fontSize: 14,
  color: colors.textDim,
  marginRight: spacing.xs,
})

const $walletId: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontFamily: typography.primary.medium,
  fontSize: 14,
  color: colors.text,
})

import { useState } from "react"
import {
  TextStyle,
  View,
  ViewStyle,
  Modal,
  TouchableOpacity,
  ScrollView,
  Image,
  ImageStyle,
  ActivityIndicator,
  Alert,
} from "react-native"
import { colors, spacing } from "@/theme"
import { Text, Icon, Button } from "@/components"
import { Payinvoice } from "@/services/api"

export interface PayInvoiceProps {
  /**
   * Is the modal visible?
   */
  visible: boolean
  /**
   * Function to close the modal
   */
  onClose: () => void
  /**
   * Invoice data
   */
  invoiceData: {
    banner_photo?: string
    name?: string
    profile_picture?: string
    wallet_id: string
    cart_checkout: string
    cart_items: Array<{
      amount: number
      currency: string
      description: string
      product: string
      product_id: string
      qt: number
    }>
    cart_referance: string
    total_amount: number
    other_currencies?: Array<{
      amount: number
      currency: string
      name: string
    }>
  }
}

export const PayInvoice = (props: PayInvoiceProps) => {
  const { visible, onClose, invoiceData } = props
  const [isProcessing, setIsProcessing] = useState(false)

  // Calculate total items
  const totalItems = invoiceData?.cart_items?.reduce((total, item) => total + item.qt, 0) || 0

  // Handle payment
  const handlePayment = async () => {
    if (!invoiceData?.cart_checkout) {
      Alert.alert("Erreur", "Impossible de traiter cette facture. Identifiant manquant.")
      return
    }

    setIsProcessing(true)
    try {
      const response = await Payinvoice(invoiceData.cart_checkout)

      if (response.success) {
        Alert.alert("Paiement réussi", "Votre paiement a été traité avec succès.", [
          { text: "OK", onPress: onClose },
        ])
      } else {
        throw new Error(response.data || "Échec du paiement")
      }
    } catch (error: any) {
      Alert.alert(
        "Échec du paiement",
        error.message || "Une erreur s'est produite lors du traitement du paiement.",
      )
    } finally {
      setIsProcessing(false)
    }
  }

  return (
    <Modal visible={visible} transparent animationType="slide" onRequestClose={onClose}>
      <View style={$modalOverlay}>
        <View style={$modalContainer}>
          {/* Header with business info */}
          <View style={$header}>
            {invoiceData?.banner_photo && (
              <Image
                source={{ uri: invoiceData.banner_photo }}
                style={$bannerImage}
                resizeMode="cover"
              />
            )}

            <View style={$businessInfoContainer}>
              {invoiceData?.profile_picture ? (
                <Image source={{ uri: invoiceData.profile_picture }} style={$profileImage} />
              ) : (
                <View style={$profilePlaceholder}>
                  <Icon icon="user" size={30} color={colors.palette.neutral100} />
                </View>
              )}

              <View style={$businessTextContainer}>
                <Text style={$businessName}>{invoiceData?.name || "Commerce"}</Text>
                <Text style={$walletId}>ID: {invoiceData?.wallet_id || ""}</Text>
              </View>

              <TouchableOpacity style={$closeButton} onPress={onClose}>
                <Icon icon="x" size={24} color={colors.palette.neutral800} />
              </TouchableOpacity>
            </View>
          </View>

          {/* Invoice details */}
          <ScrollView style={$scrollView} contentContainerStyle={$scrollContent}>
            <View style={$invoiceDetailsContainer}>
              <Text style={$sectionTitle}>Détails de la facture</Text>
              <Text style={$invoiceReference}>Référence: {invoiceData?.cart_referance || ""}</Text>

              {/* Items list */}
              <View style={$itemsContainer}>
                <Text style={$itemsTitle}>Articles ({totalItems})</Text>

                {invoiceData?.cart_items?.map((item, index) => (
                  <View key={index} style={$itemRow}>
                    <View style={$itemInfo}>
                      <Text style={$itemName}>{item.product}</Text>
                      {item.description && <Text style={$itemDescription}>{item.description}</Text>}
                    </View>
                    <View style={$itemPriceContainer}>
                      <Text style={$itemQuantity}>{item.qt} x</Text>
                      <Text style={$itemPrice}>
                        {item.amount} {item.currency}
                      </Text>
                    </View>
                  </View>
                ))}
              </View>

              {/* Other currencies */}
              {invoiceData?.other_currencies && invoiceData.other_currencies.length > 0 && (
                <View style={$otherCurrenciesContainer}>
                  <Text style={$otherCurrenciesTitle}>Autres devises</Text>
                  {invoiceData.other_currencies.map((currency, index) => (
                    <Text key={index} style={$otherCurrencyText}>
                      {currency.amount} {currency.currency} ({currency.name})
                    </Text>
                  ))}
                </View>
              )}
            </View>
          </ScrollView>

          {/* Footer with total and payment button */}
          <View style={$footer}>
            <View style={$totalContainer}>
              <Text style={$totalLabel}>Montant total</Text>
              <Text style={$totalAmount}>
                {invoiceData?.total_amount || 0} {invoiceData?.cart_items?.[0]?.currency || "FC"}
              </Text>
            </View>

            <Button
              text={isProcessing ? "Traitement..." : "Payer maintenant"}
              style={$payButton}
              textStyle={$payButtonText}
              preset="reversed"
              onPress={handlePayment}
              disabled={isProcessing}
            />

            {isProcessing && (
              <ActivityIndicator
                style={$loadingIndicator}
                size="small"
                color={colors.palette.neutral100}
              />
            )}
          </View>
        </View>
      </View>
    </Modal>
  )
}

const $modalOverlay: ViewStyle = {
  flex: 1,
  backgroundColor: "rgba(0, 0, 0, 0.5)",
  justifyContent: "center",
  alignItems: "center",
}

const $modalContainer: ViewStyle = {
  width: "90%",
  maxHeight: "80%",
  backgroundColor: colors.palette.neutral100,
  borderRadius: 20,
  overflow: "hidden",
  elevation: 5,
  shadowColor: "#000",
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.25,
  shadowRadius: 3.84,
}

const $header: ViewStyle = {
  width: "100%",
}

const $bannerImage: ImageStyle = {
  width: "100%",
  height: 100,
}

const $businessInfoContainer: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  padding: spacing.md,
  borderBottomWidth: 1,
  borderBottomColor: colors.palette.neutral200,
}

const $profileImage: ImageStyle = {
  width: 50,
  height: 50,
  borderRadius: 25,
  marginRight: spacing.sm,
}

const $profilePlaceholder: ViewStyle = {
  width: 50,
  height: 50,
  borderRadius: 25,
  backgroundColor: colors.palette.neutral800,
  justifyContent: "center",
  alignItems: "center",
  marginRight: spacing.sm,
}

const $businessTextContainer: ViewStyle = {
  flex: 1,
}

const $businessName: TextStyle = {
  fontSize: 18,
  fontWeight: "bold",
  color: colors.palette.neutral800,
}

const $walletId: TextStyle = {
  fontSize: 14,
  color: colors.palette.neutral600,
}

const $closeButton: ViewStyle = {
  padding: spacing.xs,
}

const $scrollView: ViewStyle = {
  maxHeight: 400,
}

const $scrollContent: ViewStyle = {
  padding: spacing.md,
}

const $invoiceDetailsContainer: ViewStyle = {
  marginBottom: spacing.lg,
}

const $sectionTitle: TextStyle = {
  fontSize: 18,
  fontWeight: "bold",
  marginBottom: spacing.sm,
  color: colors.palette.neutral800,
}

const $invoiceReference: TextStyle = {
  fontSize: 14,
  color: colors.palette.neutral600,
  marginBottom: spacing.md,
}

const $itemsContainer: ViewStyle = {
  marginBottom: spacing.md,
}

const $itemsTitle: TextStyle = {
  fontSize: 16,
  fontWeight: "600",
  marginBottom: spacing.sm,
  color: colors.palette.neutral800,
}

const $itemRow: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
  paddingVertical: spacing.xs,
  borderBottomWidth: 1,
  borderBottomColor: colors.palette.neutral200,
}

const $itemInfo: ViewStyle = {
  flex: 1,
  marginRight: spacing.sm,
}

const $itemName: TextStyle = {
  fontSize: 15,
  fontWeight: "500",
  color: colors.palette.neutral800,
}

const $itemDescription: TextStyle = {
  fontSize: 13,
  color: colors.palette.neutral600,
  marginTop: 2,
}

const $itemPriceContainer: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
}

const $itemQuantity: TextStyle = {
  fontSize: 14,
  color: colors.palette.neutral600,
  marginRight: spacing.xs,
}

const $itemPrice: TextStyle = {
  fontSize: 15,
  fontWeight: "500",
  color: colors.palette.neutral800,
}

const $otherCurrenciesContainer: ViewStyle = {
  marginTop: spacing.sm,
}

const $otherCurrenciesTitle: TextStyle = {
  fontSize: 16,
  fontWeight: "600",
  marginBottom: spacing.xs,
  color: colors.palette.neutral800,
}

const $otherCurrencyText: TextStyle = {
  fontSize: 14,
  color: colors.palette.neutral600,
  marginBottom: 2,
}

const $footer: ViewStyle = {
  padding: spacing.md,
  borderTopWidth: 1,
  borderTopColor: colors.palette.neutral200,
}

const $totalContainer: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
  marginBottom: spacing.md,
}

const $totalLabel: TextStyle = {
  fontSize: 16,
  fontWeight: "600",
  color: colors.palette.neutral800,
}

const $totalAmount: TextStyle = {
  fontSize: 20,
  fontWeight: "bold",
  color: colors.palette.neutral900,
}

const $payButton: ViewStyle = {
  backgroundColor: colors.palette.neutral900,
  borderRadius: 10,
  height: 50,
}

const $payButtonText: TextStyle = {
  fontSize: 16,
  fontWeight: "600",
}

const $loadingIndicator: ViewStyle = {
  position: "absolute",
  right: spacing.lg,
  top: "50%",
}

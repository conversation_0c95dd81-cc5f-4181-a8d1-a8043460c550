/* eslint-disable react/jsx-no-undef */
/* eslint-disable no-restricted-imports */
/* eslint-disable import/no-unresolved */
/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useState, useEffect } from "react"
import { StyleProp, View, ViewStyle, TextStyle } from "react-native"
import { Icon, Text, FencyTextInput, TextField } from "."
import { colors, spacing } from "@/theme"
import { useStores } from "@/store/rootStore"
import { Controller, Control, FieldValues, Path } from "react-hook-form"

export interface AmountInputProps<T extends FieldValues = FieldValues> {
  // Basic props
  value?: string | number // Allow both string and number types
  onChange?: (value: string) => void
  style?: StyleProp<ViewStyle>
  error?: string
  disabled?: boolean
  placeholder?: string
  inputname?: string
  editable?: boolean
  // Currency related props
  currency: string
  onCurrencyPress?: () => void

  // Balance related props
  currentBalance?: number
  minAmount?: { USD: number; FC: number }

  // React Hook Form props (optional)
  control?: Control<T>
  name?: Path<T>
  required?: boolean

  // Customization props
  customValidation?: (value: string, currency: string, balance: number) => string | true
}

export function AmountInput<T extends FieldValues = FieldValues>({
  value: externalValue,
  onChange: externalOnChange,
  style,
  error: externalError,
  disabled,
  placeholder = "Saisir le montant",
  inputname,
  editable,
  currency,
  onCurrencyPress,
  currentBalance: externalBalance,
  minAmount = { USD: 2, FC: 5000 },
  control,
  name,
  required = true,
  customValidation,
}: AmountInputProps<T>) {
  const {
    appsettings: { getExchangeRate, getStandardizedCurrency },
    fedhapochi: { currentBalance: storeBalance, otherCurrencies },
  } = useStores()

  const [localError, setLocalError] = useState<string>("")
  const currentBalance = externalBalance ?? storeBalance ?? 0

  // Get the actual balance in the current currency
  const getBalanceInCurrency = (targetCurrency: string): number => {
    const standardizedCurrency = getStandardizedCurrency(targetCurrency)

    if (standardizedCurrency === "FC") {
      return currentBalance // Balance is already in FC
    } else if (standardizedCurrency === "USD") {
      // Check if we have the USD balance in otherCurrencies
      const usdBalance = otherCurrencies?.find(
        (curr) => getStandardizedCurrency(curr.currency) === "USD",
      )
      if (usdBalance) {
        return usdBalance.amount
      }
      // If not found in otherCurrencies, convert FC to USD
      return getExchangeRate("FC", "USD") * currentBalance
    }
    return 0
  }

  // Enhanced validation function
  const validateAmount = (value: string): string | true => {
    setLocalError("") // Clear previous error

    if (!value && required) return "Le montant est obligatoire"

    const amount = parseFloat(value)
    if (isNaN(amount)) return "Le montant doit être un nombre valide"

    const standardizedCurrency = getStandardizedCurrency(currency)
    const availableBalance = getBalanceInCurrency(standardizedCurrency)

    // Currency specific validation with proper balance checking
    if (standardizedCurrency === "USD") {
      if (amount < minAmount.USD) {
        return `Le montant minimum est de ${minAmount.USD} USD`
      }
      // Convert balance to USD using API exchange rate
      const balanceInUSD = getExchangeRate("FC", "USD") * currentBalance
      if (amount > balanceInUSD) {
        return `Solde insuffisant. Votre solde est de ${balanceInUSD.toFixed(2)} USD`
      }
    } else if (standardizedCurrency === "FC") {
      if (amount < minAmount.FC) {
        return `Le montant minimum est de ${minAmount.FC.toLocaleString()} FC`
      }
      // Use currentBalance directly for FC
      if (amount > currentBalance) {
        return `Solde insuffisant. Votre solde est de ${currentBalance.toLocaleString()} FC`
      }
    }

    // Custom validation if provided
    if (customValidation) {
      const customResult = customValidation(value, currency, availableBalance)
      if (customResult !== true) return customResult
    }

    return true
  }

  // Format balance for display
  const formatBalance = (amount: number, curr: string): string => {
    const standardizedCurrency = getStandardizedCurrency(curr)
    return standardizedCurrency === "FC" ? amount.toLocaleString("fr-CD") : amount.toFixed(2)
  }

  // Enhanced handleChange with immediate validation
  const handleChange = (value: string) => {
    const sanitizedValue = sanitizeAmount(value)
    const validationResult = validateAmount(sanitizedValue)

    if (typeof validationResult === "string") {
      setLocalError(validationResult)
    }

    externalOnChange?.(sanitizedValue)
  }

  // Sanitize amount input
  const sanitizeAmount = (value: string): string => {
    // Remove non-numeric characters except decimal point
    let sanitized = value.replace(/[^0-9.]/g, "")

    // Ensure only one decimal point
    const parts = sanitized.split(".")
    if (parts.length > 2) {
      sanitized = parts[0] + "." + parts.slice(1).join("")
    }

    // Limit to 2 decimal places
    if (parts[1]?.length > 2) {
      sanitized = parts[0] + "." + parts[1].slice(0, 2)
    }

    return sanitized
  }

  // Render input with balance display and immediate error feedback
  const renderInput = (field?: { onChange: (value: string) => void; value: string }) => (
    <View>
      <TextField
        value={field?.value} // Convert to string and provide default empty string
        onChangeText={(value: string) => {
          const sanitizedValue = sanitizeAmount(value)
          if (field?.onChange) {
            field.onChange(sanitizedValue)
          } else {
            handleChange(sanitizedValue)
          }
        }}
        style={$input}
        label={inputname ?? `montant en ${currency}`}
        keyboardType="numeric"
        placeholder={placeholder}
        // RightAccessory={getStandardizedCurrency(currency) === "FC" ? "FC" : "dollar"}
        // leftonPress={onCurrencyPress}
        helper={externalError || localError}
        status={externalError || localError ? "error" : undefined}
        editable={editable}
      />

      {/* Show error message immediately */}
      {(externalError || localError) && (
        <Text style={$errorText}>{externalError || localError}</Text>
      )}

      {/* Show balance */}
      <Text style={$balanceText}>
        Solde disponible: {formatBalance(getBalanceInCurrency(currency), currency)} {currency}
      </Text>
    </View>
  )

  return (
    <View style={[$container, style]}>
      {control && name ? (
        <Controller
          control={control}
          name={name}
          rules={{
            required: required ? "Le montant est obligatoire" : false,
            validate: validateAmount,
          }}
          render={({ field }) => renderInput(field)}
        />
      ) : (
        renderInput()
      )}
    </View>
  )
}

const $container: ViewStyle = {
  // paddingHorizontal: spacing.md,
}

const $input: TextStyle = {
  fontSize: 16,
}

const $errorText: TextStyle = {
  color: colors.palette.angry500, // or your error color
  fontSize: 12,
  // marginTop: spacing.xs,
  // marginBottom: spacing.xs,
}

const $balanceText: TextStyle = {
  fontSize: 12,
  color: colors.palette.neutral600,
  marginTop: spacing.xs,
  textAlign: "right",
}

/* eslint-disable import/no-unresolved */
/* eslint-disable react-native/no-inline-styles */
import { StyleProp, TextStyle, TouchableOpacity, ViewStyle } from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import { colors, type ThemedStyle } from "@/theme"
import { Icon, Text } from "../"
import { Badge } from "react-native-paper"

export interface ActionIconProps {
  icon?: string | any
  title: string
  onPress: () => void
  iconStyle?: any
  hascolor?: string
  needAttention?: any
  labelStyle?: any
  style?: StyleProp<ViewStyle>
}

/**
 * Describe your component here
 */

export const ActionIcon = (props: ActionIconProps) => {
  const { style, title, icon, onPress, hascolor, needAttention } = props
  const $styles = [$container, style]
  const { themed } = useAppTheme()

  return (
    <TouchableOpacity style={$styles} onPress={onPress}>
      {needAttention && (
        <Badge size={12} style={{ position: "absolute", top: 2, right: 5, zIndex: 1 }} />
      )}
      <Icon
        icon={icon || "noIcon"} // Adjust based on your icon names
        color={hascolor || colors.palette.primary600}
        size={38}
        containerStyle={$Icon}
        // style={{
        //   borderRadius: 25,
        // }}
      />
      <Text style={themed($text)} numberOfLines={2}>
        {title}
      </Text>
    </TouchableOpacity>
  )
}
const $container: ViewStyle = {
  alignItems: "center",
  justifyContent: "center",
  alignContent: "center",
  margin: 6,
}

const $text: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontFamily: typography.primary.normal,
  color: colors.palette.neutral800,
  marginTop: 9,
  fontSize: 13,
  width: 80,
  flexWrap: "wrap",
  textAlign: "center",
  lineHeight: 18,
})

const $Icon: ViewStyle = {
  width: 70,
  height: 70,
  borderColor: colors.palette.accent300,
  borderWidth: 2,
  borderRadius: 20,
  // backgroundColor: colors.palette.neutral300, // Light background
  alignItems: "center",
  justifyContent: "center",
  // shadowColor: "#000",
  // shadowOffset: { width: 0, height: 4 },
  // shadowOpacity: 0.1,
  // shadowRadius: 6,
  // elevation: 3,
}

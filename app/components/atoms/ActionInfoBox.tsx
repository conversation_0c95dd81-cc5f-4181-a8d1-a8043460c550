import { StyleProp, TouchableOpacity, View, ViewStyle, Platform } from "react-native"

export interface ActionInfoBoxProps {
  children: React.ReactNode
  onPress?: () => void
  style?: StyleProp<ViewStyle>
}

export const ActionInfoBox = (props: ActionInfoBoxProps) => {
  const { style, children, onPress } = props

  const ContainerComponent = onPress ? TouchableOpacity : View

  const $styles = [$container, style]

  return (
    <ContainerComponent style={$styles} onPress={onPress} activeOpacity={onPress ? 0.7 : 1}>
      {children}
    </ContainerComponent>
  )
}

const $container: ViewStyle = {
  backgroundColor: "#ffffff", // bg-white
  borderRadius: 8,
  padding: 16,
  borderWidth: 1, // border
  borderColor: "#e5e7eb", // border-gray-200
  // Add shadows/elevation for the 'shadow-sm' effect
  ...Platform.select({
    ios: {
      // iOS shadow properties
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.2, // Similar to sm shadow
      shadowRadius: 1.41, // Similar to sm shadow
    },
    android: {
      // Android elevation
      elevation: 2, // Elevation approximating shadow-sm
    },
  }),
}

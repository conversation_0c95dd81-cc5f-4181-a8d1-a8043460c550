import { StyleProp, ViewStyle } from "react-native"
import LottieView from "lottie-react-native"

export interface FedhaLoaderProps {
  size?: number
  style?: StyleProp<ViewStyle>
}

/**
 * Describe your component here
 */

export const FedhaLoader = (props: FedhaLoaderProps) => {
  const { size = 150 } = props

  return (
    <LottieView
      source={require("../../../assets/annimations/fedhalogoA.json")} // Path to your Lottie file
      autoPlay
      loop
      style={{ width: size, height: size }}
    />
  )
}

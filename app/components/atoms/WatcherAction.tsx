import { StyleP<PERSON>, ViewStyle } from "react-native"
import { colors } from "@/theme"
import { Icon } from ".."
import { useMemo } from "react"

export interface WatcherActionProps {
  style?: StyleProp<ViewStyle>
  iconSize?: number
  iconColor?: string
  isHidden: boolean
  onToggle: () => void
}
export const WatcherAction = (props: WatcherActionProps) => {
  const { style, onToggle, isHidden, iconSize, iconColor } = props

  const PasswordRightAccessory = useMemo(() => {
    return function PasswordRightAccessory() {
      return (
        <Icon
          icon={isHidden ? "view" : "hidden"}
          color={iconColor || colors.palette.neutral400}
          containerStyle={style}
          size={iconSize}
          onPress={onToggle} // External handler
        />
      )
    }
  }, [isHidden, iconColor, iconSize, style, onToggle])

  return <PasswordRightAccessory />
}

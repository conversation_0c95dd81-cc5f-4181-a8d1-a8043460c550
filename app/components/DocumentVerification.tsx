/* eslint-disable no-restricted-imports */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable react-native/no-inline-styles */
import {
  StyleProp,
  TextStyle,
  View,
  ViewStyle,
  Image,
  Alert,
  Modal,
  TouchableOpacity,
} from "react-native"
import RNFS from "react-native-fs"
import { colors, spacing } from "@/theme"
import { Button, FedhaLoader, FencyTextInput, Icon, Text } from "."
import { Camera, useCameraDevice, useCameraPermission } from "react-native-vision-camera"
import { useEffect, useRef, useState } from "react"
import { useNavigation } from "@react-navigation/native"
import { uploadDocument } from "@/services/api"
import DatePicker from "react-native-date-picker"
import { useStores } from "@/store/rootStore"
import { compressImage } from "@/utils/compresor"
import { launchImageLibrary, ImageLibraryOptions, PhotoQuality } from "react-native-image-picker"
import React from "react"
// import { <PERSON>haLoader } from "./FedhaLoader"

export interface DocumentVerificationProps {
  onCompleted: () => void
  style?: StyleProp<ViewStyle>
}

export const DocumentVerification = (props: DocumentVerificationProps) => {
  const {
    auth: { user, fetchUserData },
  } = useStores()
  const { style, onCompleted } = props
  const navigation = useNavigation()
  const device = useCameraDevice("back")
  const cameraRef = useRef<Camera>(null)
  const $styles = [$container, style]
  const [documentType, setDocumentType] = useState<
    "passport" | "id_card" | "driving-license" | "autre"
  >()
  const [photoUri, setPhotoUri] = useState<string | null>(null)
  const { hasPermission, requestPermission } = useCameraPermission()
  const [step, setStep] = useState(1)
  const [openIssue, setOpenIssue] = useState(false)
  const [isloading, setIsLoading] = useState(false)
  const [openExpiration, setOpenExpiration] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [apiResponse, setApiResponse] = useState<{ success: boolean; message: string } | null>(null)

  const handleNext = () => {
    // For step 2, validate all fields are filled before proceeding
    if (step === 2) {
      if (
        !formData.date_issue ||
        !formData.date_expiration ||
        !formData.lieu_de_delivrance ||
        !formData.numero_de_piece
      ) {
        Alert.alert("Formulaire incomplet", "Veuillez remplir tous les champs avant de continuer.")
        return
      }

      // Validate document number format
      if (documentType && !validateNumero()) {
        Alert.alert(
          "Numéro invalide",
          documentType === "passport"
            ? "Le numéro de passeport doit contenir 9 caractères"
            : documentType === "id_card"
              ? "Le numéro de carte d'identité doit commencer par 'NN' suivi de 11 chiffres"
              : "Le format du numéro de pièce est invalide",
        )
        return
      }
    }

    setStep((prev) => prev + 1)
  }
  const handleBack = () => setStep((prev) => prev - 1)

  const [formData, setFormData] = useState({
    date_expiration: "",
    date_issue: "",
    lieu_de_delivrance: "",
    numero_de_piece: "",
  })

  useEffect(() => {
    if (user?.IdentityDocument) {
      setFormData({
        date_expiration: user.IdentityDocument.date_expiration || "",
        date_issue: user.IdentityDocument.date_issue || "",
        lieu_de_delivrance: user.IdentityDocument.lieu_de_delivrance || "",
        numero_de_piece: user.IdentityDocument.numero_de_piece || "",
      })

      // setDocumentType(user.IdentityDocument.type_piecer === "passport" ? "passport" : "id_card")
    }
  }, [user])
  // console.log("usr:", formData)
  // Handler for updating date values
  const handleDateChange = (key: "date_issue" | "date_expiration", date: Date) => {
    setFormData((prev) => ({ ...prev, [key]: date }))
  }
  // const { trigger: mutateProfile } = useSWRMutation("user/profile")

  if (!device) return <Text>No camera available</Text>
  if (!hasPermission) {
    return (
      <View style={$permissionContainer}>
        <View style={$permissionContent}>
          <Icon
            icon={"nocamera"}
            color={colors.palette.primary600}
            size={80}
            containerStyle={$permissionIconContainer}
          />
          <Text style={$permissionTitle}>Accès à la caméra requis</Text>
          <Text style={$permissionText}>
            Vous devrez autoriser Fedha à accéder à la caméra de votre téléphone pour prendre une
            photo de votre document afin de vérifier votre compte.
          </Text>
        </View>
        <Button
          preset="reversed"
          style={$permissionButton}
          onPress={requestPermission}
          text="Autoriser la caméra"
        />
      </View>
    )
  }

  const MAX_FILE_SIZE = 5 * 1024 * 1024 // 5MB in bytes

  const processImage = async (uri: string): Promise<string | null> => {
    try {
      // First attempt with moderate compression
      let compressedUri = await compressImage(uri, {
        maxWidth: 1024,
        quality: 0.6,
      })

      // Check file size
      const fileInfo = await RNFS.stat(compressedUri)

      if (fileInfo.size > MAX_FILE_SIZE) {
        // Try again with more aggressive compression
        compressedUri = await compressImage(uri, {
          maxWidth: 800,
          quality: 0.4,
        })

        const finalFileInfo = await RNFS.stat(compressedUri)
        if (finalFileInfo.size > MAX_FILE_SIZE) {
          Alert.alert(
            "Fichier trop volumineux",
            "Le fichier dépasse 5 MB même après compression. Veuillez choisir une image plus petite.",
          )
          return null
        }
      }

      return compressedUri
    } catch (error) {
      console.error("Error processing image:", error)
      Alert.alert("Erreur", "Une erreur s'est produite lors du traitement de l'image")
      return null
    }
  }

  const pickImageFromGallery = async () => {
    try {
      const result = await launchImageLibrary({
        mediaType: "photo",
        quality: 1 as PhotoQuality,
        selectionLimit: 1,
      })
      if (result.assets && result.assets[0]?.uri) {
        const processedUri = await processImage(result.assets[0].uri)
        if (processedUri) {
          setPhotoUri(processedUri)
        }
      }
    } catch (error) {
      console.error("Image picker error:", error)
      Alert.alert("Erreur", "Une erreur s'est produite lors de la sélection de l'image")
    }
  }

  const takePicture = async () => {
    if (!cameraRef.current) return

    try {
      const photo = await cameraRef.current.takePhoto({ flash: "auto" })
      const uri = `file://${photo.path}`
      const processedUri = await processImage(uri)
      if (processedUri) {
        setPhotoUri(processedUri)
      }
    } catch (error) {
      Alert.alert("Erreur", "Échec de la prise de photo")
    }
  }

  // Update the type definition to include validation rules
  type DocumentTypeConfig = {
    maxLength: number
    pattern?: RegExp
    placeholder: string
    formatValue?: (value: string) => string
  }

  const DOCUMENT_CONFIGS: Record<
    "passport" | "id_card" | "driving-license" | "autre",
    DocumentTypeConfig
  > = {
    "passport": {
      maxLength: 9,
      pattern: /^[A-Z0-9]{9}$/, // Allow both uppercase letters and numbers
      placeholder: "Numéro de passeport",
    },
    "id_card": {
      maxLength: 13, // 11 numbers + "NN"
      pattern: /^NN\d{11}$/,
      placeholder: "Numéro de carte d'électeur ",
      formatValue: (value: string) => {
        // Remove any existing "NN" prefix
        const cleanValue = value.replace(/^NN/, "").replace(/\D/g, "")
        return cleanValue ? `NN${cleanValue}` : ""
      },
    },
    "autre": {
      maxLength: 50, // or any reasonable limit
      placeholder: "Numéro du document",
    },
    "driving-license": {
      maxLength: 0,
      pattern: undefined,
      placeholder: "",
      formatValue: undefined,
    },
  }

  // Add a handler for numero_de_piece changes
  const handleNumeroChange = (text: string) => {
    if (!documentType) return

    const config = DOCUMENT_CONFIGS[documentType]
    let formattedValue = text

    if (documentType === "passport") {
      // Allow only uppercase letters and numbers, remove other characters
      formattedValue = text.replace(/[^A-Z0-9]/g, "")
    }

    // Apply special formatting for ID card
    if (documentType === "id_card") {
      formattedValue = config.formatValue?.(text) ?? text
    }

    // Update the form data
    setFormData((prev) => ({
      ...prev,
      numero_de_piece: formattedValue,
    }))
  }

  // Add validation before submission
  const validateNumero = () => {
    if (!documentType || !formData.numero_de_piece) return false

    const config = DOCUMENT_CONFIGS[documentType]

    if (documentType === "autre") return true // No specific validation

    return config.pattern?.test(formData.numero_de_piece) ?? true
  }

  // Check if the form is complete and valid
  const isFormValid = () => {
    if (
      !formData.date_issue ||
      !formData.date_expiration ||
      !formData.lieu_de_delivrance ||
      !formData.numero_de_piece
    ) {
      return false
    }

    // Also check document number format
    if (documentType && !validateNumero()) {
      return false
    }

    return true
  }

  const handleSubmit = async () => {
    setIsSubmitting(true)
    setIsLoading(true)
    setApiResponse(null)

    try {
      // Validate required fields
      if (!photoUri) {
        throw new Error("Veuillez prendre une photo avant de soumettre.")
      }

      if (!documentType) {
        throw new Error("Veuillez sélectionner un type de document.")
      }

      if (!formData.numero_de_piece || !formData.date_expiration || !formData.date_issue) {
        throw new Error("Veuillez remplir tous les champs obligatoires.")
      }

      if (!validateNumero()) {
        throw new Error(
          documentType === "passport"
            ? "Le numéro de passeport doit contenir 9 caractères"
            : documentType === "id_card"
              ? "Le numéro de carte d'identité doit commencer par 'NN' suivi de 11 chiffres"
              : "Numéro de pièce invalide",
        )
      }

      // Debug: Check file size before compression
      const originalFileInfo = await RNFS.stat(photoUri)
      console.log("Original file size:", (originalFileInfo.size / (1024 * 1024)).toFixed(2), "MB")

      const formDataToSend = new FormData()

      // Append form data
      formDataToSend.append(
        "date_expiration",
        formData.date_expiration
          ? new Date(formData.date_expiration).toISOString().split("T")[0]
          : "",
      )
      formDataToSend.append(
        "date_issue",
        formData.date_issue ? new Date(formData.date_issue).toISOString().split("T")[0] : "",
      )
      formDataToSend.append("lieu_de_delivrance", formData.lieu_de_delivrance || "")
      formDataToSend.append("numero_de_piece", formData.numero_de_piece || "")
      formDataToSend.append("type_piece", documentType)

      // Compress and check size
      const compressedUri = await compressImage(photoUri, {
        maxWidth: 1024,
        quality: 0.6,
      })

      // Debug: Check file size after compression
      const compressedFileInfo = await RNFS.stat(compressedUri)
      console.log(
        "Compressed file size:",
        (compressedFileInfo.size / (1024 * 1024)).toFixed(2),
        "MB",
      )

      // Create the file object with compressed image
      const file = {
        uri: compressedUri,
        name: "document.jpg",
        type: "image/jpeg",
      }

      formDataToSend.append("piece_identite", file as any)

      // Debug: Log the full FormData structure
      console.log(
        "FormData structure:",
        JSON.stringify(
          {
            date_expiration: formDataToSend.get("date_expiration"),
            date_issue: formDataToSend.get("date_issue"),
            lieu_de_delivrance: formDataToSend.get("lieu_de_delivrance"),
            numero_de_piece: formDataToSend.get("numero_de_piece"),
            type_piece: formDataToSend.get("type_piece"),
            piece_identite_size: compressedFileInfo.size,
          },
          null,
          2,
        ),
      )

      const response = await uploadDocument(formDataToSend)

      if (response.success) {
        await fetchUserData()
        setApiResponse({
          success: true,
          message: "Document soumis avec succès!",
        })
        setTimeout(() => {
          navigation.navigate("FedhaLoader" as never)
          onCompleted()
        }, 1500)
      } else {
        throw new Error(response.message || "Une erreur est survenue lors de l'envoi du document.")
      }
    } catch (error: any) {
      console.error("Document submission error:", error)
      setApiResponse({
        success: false,
        message:
          error.message || "Une erreur inattendue s'est produite. Veuillez réessayer plus tard.",
      })
    } finally {
      setIsSubmitting(false)
      setIsLoading(false)
    }
  }
  const LoadingModal = () => (
    <Modal visible={isSubmitting || !!apiResponse} transparent={true} animationType="fade">
      <View style={$modalOverlay}>
        <View style={$modalContent}>
          {isSubmitting ? (
            <View style={$centerContent}>
              <FedhaLoader size={100} />
              <Text style={$processingText}>Traitement en cours...</Text>
            </View>
          ) : apiResponse ? (
            <View style={$centerContent}>
              <Text
                style={[
                  $responseText,
                  {
                    color: apiResponse.success ? colors.palette.accent400 : colors.palette.angry500,
                  },
                ]}
              >
                {apiResponse.message}
              </Text>
              {!apiResponse.success && (
                <Button
                  preset="default"
                  text="Fermer"
                  style={$modalButton}
                  onPress={() => setApiResponse(null)}
                />
              )}
            </View>
          ) : null}
        </View>
      </View>
    </Modal>
  )

  return (
    <View style={$styles}>
      {step === 1 && (
        <View style={$documentSelectionContainer}>
          <Text style={$sectionTitle}>Choisissez la pièce d&apos;identité</Text>
          <Text style={$sectionSubtitle}>
            Sélectionnez le type de document que vous souhaitez utiliser
          </Text>

          <View style={$documentGrid}>
            <TouchableOpacity
              style={$documentCard}
              onPress={() => {
                setDocumentType("id_card")
                handleNext()
              }}
            >
              <View style={$documentIconContainer}>
                <Icon icon={"idcard"} color={colors.palette.primary600} size={50} />
              </View>
              <Text style={$documentCardTitle}>Carte d&apos;électeur</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={$documentCard}
              onPress={() => {
                setDocumentType("passport")
                handleNext()
              }}
            >
              <View style={$documentIconContainer}>
                <Icon icon={"passport"} color={colors.palette.primary600} size={50} />
              </View>
              <Text style={$documentCardTitle}>Passeport</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={$documentCard}
              onPress={() => {
                setDocumentType("driving-license")
                handleNext()
              }}
            >
              <View style={$documentIconContainer}>
                <Icon icon={"idcard"} color={colors.palette.primary600} size={50} />
              </View>
              <Text style={$documentCardTitle}>Permis de conduire</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={$documentCard}
              onPress={() => {
                setDocumentType("autre")
                handleNext()
              }}
            >
              <View style={$documentIconContainer}>
                <Icon icon={"identification"} color={colors.palette.primary600} size={50} />
              </View>
              <Text style={$documentCardTitle}>Autre document</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}

      {step === 2 && (
        <View style={$formContainer}>
          <Text style={$sectionTitle}>Détails du document</Text>
          <Text style={$sectionSubtitle}>
            Veuillez remplir les informations de votre{" "}
            {documentType === "passport"
              ? "passeport"
              : documentType === "id_card"
                ? "carte d'électeur"
                : documentType === "driving-license"
                  ? "permis de conduire"
                  : "document"}
          </Text>

          <View style={$formFieldsContainer}>
            <View style={[$formField, !formData.date_expiration && $invalidField]}>
              <Text style={$formLabel}>
                Date d&lsquo;émission <Text style={$requiredField}>*</Text>
              </Text>
              <TouchableOpacity style={$datePickerButton} onPress={() => setOpenIssue(true)}>
                <Text style={[$datePickerText, !formData.date_issue && $placeholderText]}>
                  {formData.date_issue
                    ? new Date(formData.date_issue).toISOString().split("T")[0]
                    : "Sélectionner une date"}
                </Text>
                <Icon icon="calendar" size={20} color={colors.palette.neutral600} />
              </TouchableOpacity>
            </View>

            <DatePicker
              modal
              open={openIssue}
              date={formData.date_issue ? new Date(formData.date_issue) : new Date()}
              mode="date"
              onConfirm={(date) => {
                handleDateChange("date_issue", date)
                setOpenIssue(false)
              }}
              onCancel={() => setOpenIssue(false)}
            />

            <View style={[$formField, !formData.date_expiration && $invalidField]}>
              <Text style={$formLabel}>
                Date d&#39;expiration <Text style={$requiredField}>*</Text>
              </Text>
              <TouchableOpacity style={$datePickerButton} onPress={() => setOpenExpiration(true)}>
                <Text style={[$datePickerText, !formData.date_expiration && $placeholderText]}>
                  {formData.date_expiration
                    ? new Date(formData.date_expiration).toISOString().split("T")[0]
                    : "Sélectionner une date"}
                </Text>
                <Icon icon="calendar" size={20} color={colors.palette.neutral600} />
              </TouchableOpacity>
            </View>

            <DatePicker
              modal
              open={openExpiration}
              date={formData.date_expiration ? new Date(formData.date_expiration) : new Date()}
              mode="date"
              onConfirm={(date) => {
                handleDateChange("date_expiration", date)
                setOpenExpiration(false)
              }}
              onCancel={() => setOpenExpiration(false)}
            />

            <FencyTextInput
              value={formData.lieu_de_delivrance}
              onChange={(text: any) => setFormData({ ...formData, lieu_de_delivrance: text })}
              style={$customInput}
              status={!formData.lieu_de_delivrance ? "error" : "disabled"}
              inputname={"Lieu de délivrance *"}
              placeholder={"Entrez le lieu de délivrance"}
            />

            <FencyTextInput
              value={formData.numero_de_piece}
              onChange={handleNumeroChange}
              style={$customInput}
              status={!formData.numero_de_piece ? "error" : "disabled"}
              maxLength={documentType ? DOCUMENT_CONFIGS[documentType].maxLength : undefined}
              inputname={"Numéro de pièce *"}
              placeholder={
                documentType
                  ? DOCUMENT_CONFIGS[documentType].placeholder
                  : "Entrez le numéro de pièce"
              }
            />
            {documentType && formData.numero_de_piece && !validateNumero() && (
              <Text style={$errorText}>
                {documentType === "passport"
                  ? "Le numéro de passeport doit contenir 9 caractères"
                  : documentType === "id_card"
                    ? "Le numéro doit commencer par 'NN' suivi de 11 chiffres"
                    : "Format invalide"}
              </Text>
            )}
          </View>

          <View style={$formActions}>
            <Button preset="default" text={"Retour"} style={$backButton} onPress={handleBack} />
            <Button
              testID="next-screen-button"
              preset="reversed"
              text={"Suivant"}
              style={[$nextButton, !isFormValid() && $disabledButton]}
              disabled={!isFormValid()}
              onPress={handleNext}
            />
          </View>
        </View>
      )}
      {step === 3 && (
        <View style={$cameraContainer}>
          {/* <Text style={$sectionTitle}>Photo du document</Text> */}
          <Text style={$sectionSubtitle}>
            Prenez une photo claire de votre{" "}
            {documentType === "passport"
              ? "passeport"
              : documentType === "id_card"
                ? "carte d'électeur"
                : documentType === "driving-license"
                  ? "permis de conduire"
                  : "document"}
          </Text>

          {photoUri ? (
            <View style={$photoPreviewContainer}>
              <Image source={{ uri: photoUri }} style={$photoPreview} />
              <View style={$photoActions}>
                {!isloading && (
                  <Button
                    testID="retake-photo-button"
                    preset="default"
                    text={"Reprendre la photo"}
                    style={$retakeButton}
                    onPress={() => setPhotoUri(null)}
                  />
                )}

                <Button
                  testID="submit-button"
                  preset="reversed"
                  text={"Soumettre le document"}
                  style={$submitButton}
                  disabled={isloading || !photoUri}
                  onPress={handleSubmit}
                />
              </View>
            </View>
          ) : (
            <View style={$cameraWrapper}>
              <Camera
                ref={cameraRef}
                style={$camera}
                device={device}
                isActive={true}
                photo={true}
              />

              <View style={$cameraControls}>
                <Button
                  testID="take-photo-button"
                  preset="reversed"
                  text={"Prendre une photo"}
                  style={$cameraButton}
                  onPress={takePicture}
                />
                <Button
                  testID="gallery-button"
                  preset="default"
                  text={"Choisir depuis la galerie"}
                  style={$galleryButton}
                  onPress={pickImageFromGallery}
                />
              </View>

              <View style={$cameraInstructions}>
                <Text style={$cameraInstructionsText}>
                  Assurez-vous que le document est bien éclairé et que tous les détails sont
                  lisibles
                </Text>
              </View>
            </View>
          )}
        </View>
      )}
      <LoadingModal />
    </View>
  )
}

const $container: ViewStyle = {
  // flex: 1,
  justifyContent: "center",
  alignItems: "center",
  // padding: 16,
  backgroundColor: "#F7F9FC",
}

// Styles removed to clean up unused declarations

const $modalOverlay: ViewStyle = {
  flex: 1,
  backgroundColor: "rgba(0, 0, 0, 0.5)",
  justifyContent: "center",
  alignItems: "center",
}

const $modalContent: ViewStyle = {
  backgroundColor: colors.palette.neutral100,
  padding: 20,
  borderRadius: 10,
  width: "80%",
  alignItems: "center",
}

const $centerContent: ViewStyle = {
  alignItems: "center",
  justifyContent: "center",
}

const $processingText: TextStyle = {
  marginTop: 20,
  fontSize: 16,
  textAlign: "center",
  color: colors.text,
}

const $responseText: TextStyle = {
  fontSize: 16,
  textAlign: "center",
  marginBottom: 20,
}

const $modalButton: ViewStyle = {
  minWidth: 120,
}

// New styles for document selection screen
const $documentSelectionContainer: ViewStyle = {
  width: "100%",
  padding: spacing.md,
  alignItems: "center",
}

const $sectionTitle: TextStyle = {
  fontSize: 22,
  fontWeight: "bold",
  color: colors.palette.neutral800,
  marginBottom: spacing.xs,
  textAlign: "center",
}

const $sectionSubtitle: TextStyle = {
  fontSize: 14,
  top: 20,
  color: colors.palette.neutral600,
  marginBottom: spacing.xl,
  textAlign: "center",
}

const $documentGrid: ViewStyle = {
  flexDirection: "row",
  flexWrap: "wrap",
  justifyContent: "space-between",
  width: "100%",
  marginTop: spacing.md,
}

const $documentCard: ViewStyle = {
  width: "48%",
  backgroundColor: colors.palette.neutral100,
  borderRadius: 12,
  padding: spacing.md,
  marginBottom: spacing.md,
  alignItems: "center",
  shadowColor: colors.palette.neutral900,
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
  shadowRadius: 4,
  elevation: 2,
  borderWidth: 1,
  borderColor: colors.palette.neutral300,
}

const $documentIconContainer: ViewStyle = {
  width: 80,
  height: 80,
  borderRadius: 40,
  backgroundColor: colors.palette.primary100,
  justifyContent: "center",
  alignItems: "center",
  marginBottom: spacing.sm,
}

const $documentCardTitle: TextStyle = {
  fontSize: 14,
  fontWeight: "500",
  color: colors.palette.neutral800,
  textAlign: "center",
}

// Form styles
const $formContainer: ViewStyle = {
  width: "100%",
  padding: spacing.md,
}

const $formFieldsContainer: ViewStyle = {
  marginTop: spacing.md,
  marginBottom: spacing.xl,
}

const $formField: ViewStyle = {
  marginBottom: spacing.md,
  borderRadius: 15,
  marginTop: spacing.md,
  paddingHorizontal: 15,
  borderColor: "#7E958D",
  borderWidth: 1.5,
}

const $formLabel: TextStyle = {
  fontSize: 14,
  fontWeight: "500",
  color: colors.palette.neutral700,
  marginBottom: spacing.xs / 2,
}

const $datePickerButton: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
  // backgroundColor: colors.palette.neutral100,
  // borderWidth: 1,
  // borderColor: colors.palette.neutral300,
  borderRadius: 8,
  padding: spacing.sm,
  height: 50,
}

const $datePickerText: TextStyle = {
  fontSize: 14,
  color: colors.palette.neutral800,
}

const $customInput: ViewStyle = {
  marginBottom: spacing.md,
}

const $formActions: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
  marginTop: spacing.md,
}

const $backButton: ViewStyle = {
  flex: 1,
  marginRight: spacing.xs,
  backgroundColor: colors.palette.neutral200,
  borderWidth: 1,
  borderColor: colors.palette.neutral300,
}

const $nextButton: ViewStyle = {
  flex: 2,
  marginLeft: spacing.xs,
  backgroundColor: colors.palette.primary600,
}

const $disabledButton: ViewStyle = {
  backgroundColor: colors.palette.neutral400,
  opacity: 0.7,
}

const $requiredField: TextStyle = {
  color: colors.palette.angry500,
  fontWeight: "bold",
}

const $invalidField: ViewStyle = {
  borderColor: colors.palette.angry500,
  borderWidth: 1,
}

const $placeholderText: TextStyle = {
  color: colors.palette.neutral500,
  fontStyle: "italic",
}

const $invalidInput: ViewStyle = {
  borderColor: colors.palette.angry500,
}

const $errorText: TextStyle = {
  color: colors.palette.angry500,
  fontSize: 12,
  marginTop: 4,
  marginBottom: 8,
}

// Camera screen styles
const $cameraContainer: ViewStyle = {
  width: "100%",
  paddingTop: spacing.md,
}

const $photoPreviewContainer: ViewStyle = {
  width: "100%",
  alignItems: "center",
  marginTop: spacing.md,
}

const $photoPreview = {
  width: "100%",
  height: 400,
  borderRadius: 12,
  marginBottom: spacing.lg,
} as any // Using 'any' to avoid type issues with Image style

const $photoActions: ViewStyle = {
  width: "100%",
}

const $retakeButton: ViewStyle = {
  marginBottom: spacing.md,
  backgroundColor: colors.palette.neutral200,
  borderWidth: 1,
  borderColor: colors.palette.neutral300,
}

const $submitButton: ViewStyle = {
  backgroundColor: colors.palette.primary600,
}

const $cameraWrapper: ViewStyle = {
  width: "100%",
  position: "relative",
  marginTop: spacing.md,
}

const $camera: ViewStyle = {
  width: "100%",
  height: 400,
  borderRadius: 12,
  marginBottom: spacing.md,
  position: "relative",
  overflow: "hidden",
}

// const $cameraGuide: ViewStyle = {
//   position: 'absolute',
//   top: 0,
//   left: 0,
//   right: 0,
//   bottom: 0,
//   zIndex: 10,
//   borderWidth: 2,
//   borderColor: 'transparent',
//   borderRadius: 12,
// }

// const $cameraGuideCorner: ViewStyle = {
//   position: 'absolute',
//   width: 20,
//   height: 20,
//   borderColor: colors.palette.primary500,
//   borderWidth: 3,
// }

const $cameraControls: ViewStyle = {
  flexDirection: "column",
  width: "100%",
  marginBottom: spacing.md,
}

const $cameraButton: ViewStyle = {
  backgroundColor: colors.palette.primary600,
  marginBottom: spacing.sm,
}

const $galleryButton: ViewStyle = {
  backgroundColor: colors.palette.neutral200,
  borderWidth: 1,
  borderColor: colors.palette.neutral300,
}

const $cameraInstructions: ViewStyle = {
  padding: spacing.sm,
  backgroundColor: colors.palette.neutral200,
  borderRadius: 8,
  marginBottom: spacing.lg,
}

const $cameraInstructionsText: TextStyle = {
  fontSize: 12,
  color: colors.palette.neutral700,
  textAlign: "center",
}

// Permission screen styles
const $permissionContainer: ViewStyle = {
  flex: 1,
  padding: spacing.lg,
  backgroundColor: colors.palette.neutral100,
  justifyContent: "space-between",
}

const $permissionContent: ViewStyle = {
  flex: 1,
  alignItems: "center",
  justifyContent: "center",
  marginBottom: spacing.xl,
}

const $permissionIconContainer: ViewStyle = {
  width: 120,
  height: 120,
  borderRadius: 60,
  backgroundColor: colors.palette.primary100,
  justifyContent: "center",
  alignItems: "center",
  marginBottom: spacing.lg,
}

const $permissionTitle: TextStyle = {
  fontSize: 20,
  fontWeight: "bold",
  color: colors.palette.neutral800,
  marginBottom: spacing.md,
  textAlign: "center",
}

const $permissionText: TextStyle = {
  fontSize: 16,
  color: colors.palette.neutral600,
  textAlign: "center",
  lineHeight: 24,
}

const $permissionButton: ViewStyle = {
  backgroundColor: colors.palette.primary600,
  borderRadius: 12,
  paddingVertical: spacing.md,
}

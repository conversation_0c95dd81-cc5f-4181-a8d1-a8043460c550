/* eslint-disable no-restricted-imports */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable react-native/no-inline-styles */
/* eslint-disable import/no-unresolved */
import { useState } from "react"
import {
  StyleProp,
  View,
  ViewStyle,
  ScrollView,
  ActivityIndicator,
  Modal,
  TextStyle,
} from "react-native"
import {
  Button,
  ChangeCurency,
  FedhaPochiBar,
  FencyTextInput,
  Text,
  FedhaLoader,
  // Scanner,
  FedhaPochiInput,
  Icon,
} from "."
import { colors, spacing } from "@/theme"
import { useStores } from "@/store/rootStore"
import { useForm, Controller } from "react-hook-form"
import { TransferCash } from "@/services/api"
import React from "react"

export interface TransferFedhaPochiProps {
  style?: StyleProp<ViewStyle>
}

interface TransferFormData {
  amount: string
  receiver: string
  note?: string
}

export const TransferFedhaPochi = (props: TransferFedhaPochiProps) => {
  const {
    appsettings: { currency },
    fedhapochi: { fetchWallet }, // Add this to refresh wallet after transfer
  } = useStores()

  const { style } = props
  const $styles = [$container, style]

  const [showCurrencyModal, setShowCurrencyModal] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [showManualEntry, setShowManualEntry] = useState(false)
  const [manualId, setManualId] = useState("")

  const [apiResponse, setApiResponse] = useState<{
    success?: boolean
    message?: string
  } | null>(null)
  // const [scannerVisible, setScannerVisible] = useState(false)

  const {
    control,
    handleSubmit,
    setValue,
    formState: { errors },
    reset,
  } = useForm<TransferFormData>({
    defaultValues: {
      amount: "",
      receiver: "",
      note: "",
    },
  })

  const onSubmit = async (data: TransferFormData) => {
    try {
      setIsLoading(true)
      setApiResponse(null)

      const response = await TransferCash({
        receiver: data.receiver,
        amount: Number(data.amount),
        currency,
        note: data.note || "",
      })

      if (response.success) {
        setApiResponse({
          success: true,
          message: "Transfert réussi!",
        })
        // Reset form and refresh wallet
        reset()
        await fetchWallet()
      } else {
        throw new Error(response.data || "Le transfert a échoué")
      }
    } catch (error: any) {
      setApiResponse({
        success: false,
        message: error.message || "Une erreur s'est produite. Veuillez réessayer.",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <>
      <ScrollView style={$styles}>
        <View style={$content}>
          <Text text="À partir de" />
          <FedhaPochiBar />
          <Text text="Vers" />

          <Controller
            control={control}
            name="receiver"
            rules={{
              required: "L'ID FedhaPochi est obligatoire",
              pattern: {
                value: /^\d{9}$/,
                message: "L'ID FedhaPochi doit contenir 9 chiffres",
              },
            }}
            render={({ field: { onChange, value } }) => (
              <View style={$inputContainer}>
                <FedhaPochiInput onChange={onChange} value={value} />
                {errors.receiver && <Text style={$errorText}>{errors.receiver.message}</Text>}
              </View>
            )}
          />

          <Controller
            control={control}
            name="amount"
            rules={{
              required: "Le montant est obligatoire",
              validate: (value) => {
                const amount = parseFloat(value)
                if (isNaN(amount)) return "Le montant doit être un nombre valide"
                if (amount <= 0) return "Le montant doit être supérieur à 0"
                if (currency === "USD" && amount < 1) return "Le montant minimum est de 1 USD"
                if (currency === "FC" && amount < 50) return "Le montant minimum est de 50 FC"
                return true
              },
            }}
            render={({ field: { onChange, value } }) => (
              <View style={$inputContainer}>
                <FencyTextInput
                  value={value}
                  onChange={onChange}
                  style={$input}
                  inputname={`Montant en ${currency}`}
                  keyboardType="numeric"
                  placeholder={"Saisir le montant"}
                  LeftIcon={
                    currency === "FC" || (currency as string) === "Congolese Franc"
                      ? "FC"
                      : "dollar"
                  }
                  leftonPress={() => setShowCurrencyModal(!showCurrencyModal)}
                />
                {errors.amount && <Text style={$errorText}>{errors.amount.message}</Text>}
              </View>
            )}
          />

          <Controller
            control={control}
            name="note"
            render={({ field: { onChange, value } }) => (
              <View style={$inputContainer}>
                <FencyTextInput
                  value={value}
                  onChange={onChange}
                  multiline
                  numberOfLines={3}
                  inputname="Message (optionnel)"
                  placeholder="Ajouter un message"
                  style={$input}
                />
                <Text style={$helperText}>
                  Vous pouvez envoyer de l&lsquo;argent avec ou sans message
                </Text>
              </View>
            )}
          />
        </View>

        <View style={$footer}>
          <Button
            preset="reversed"
            text="Transférer"
            style={[$transferButton, isLoading && { opacity: 0.5 }]}
            onPress={handleSubmit(onSubmit)}
            disabled={isLoading}
          >
            {isLoading ? (
              <ActivityIndicator size="small" color={colors.palette.neutral100} />
            ) : null}
          </Button>
        </View>
      </ScrollView>

      <ChangeCurency isVisible={showCurrencyModal} onClose={() => setShowCurrencyModal(false)} />

      <Modal visible={isLoading || !!apiResponse} transparent animationType="fade">
        <View style={$modalOverlay}>
          <View style={$modalContent}>
            {isLoading ? (
              <View style={$centerContent}>
                <View style={$loaderIconContainer}>
                  <FedhaLoader />
                </View>
                <Text style={$processingText}>Traitement en cours...</Text>
                <Text style={$processingSubText}>
                  Veuillez patienter pendant que nous traitons votre demande
                </Text>
              </View>
            ) : apiResponse ? (
              <View style={$centerContent}>
                <View
                  style={[
                    $statusIconContainer,
                    {
                      backgroundColor: apiResponse.success
                        ? colors.palette.accent100
                        : colors.palette.angry100,
                    },
                  ]}
                >
                  <Icon
                    icon={apiResponse.success ? "Rcheck" : "cancelround"}
                    size={35}
                    color={
                      apiResponse.success ? colors.palette.neutral300 : colors.palette.angry500
                    }
                  />
                </View>
                <Text style={$statusTitle}>
                  {apiResponse.success ? "Transfert réussi!" : "Échec du transfert"}
                </Text>
                <Text
                  style={[
                    $responseText,
                    {
                      color: apiResponse.success
                        ? colors.palette.neutral700
                        : colors.palette.angry500,
                    },
                  ]}
                >
                  {apiResponse.message}
                </Text>
                <Button
                  preset={apiResponse.success ? "reversed" : "default"}
                  text={apiResponse.success ? "Terminé" : "Fermer"}
                  style={[
                    $modalButton,
                    apiResponse.success && { backgroundColor: colors.palette.neutral300 },
                  ]}
                  onPress={() => setApiResponse(null)}
                />
              </View>
            ) : null}
          </View>
        </View>
      </Modal>

      {/* <Scanner
        visible={scannerVisible}
        onClose={() => setScannerVisible(false)}
        onscan={(scannedValue) => {
          setValue("receiver", scannedValue)
          setScannerVisible(false)
        }}
      /> */}
      <Modal
        visible={showManualEntry}
        transparent
        animationType="slide"
        onRequestClose={() => setShowManualEntry(false)}
      >
        <View style={$manualEntryOverlay}>
          <View style={$manualEntryContent}>
            <FencyTextInput
              value={manualId}
              onChange={setManualId}
              inputname="FedhaPochi ID"
              placeholder="Entrez l'ID FedhaPochi"
              keyboardType="numeric"
              maxLength={9}
              onSubmitEditing={() => {
                if (manualId.length === 9) {
                  setValue("receiver", manualId)
                  setManualId("")
                  setShowManualEntry(false)
                }
              }}
            />
          </View>
        </View>
      </Modal>
    </>
  )
}

const $container: ViewStyle = {
  backgroundColor: colors.palette.neutral100,
  height: "100%",
}

const $content: ViewStyle = {
  paddingHorizontal: spacing.lg,
  paddingTop: spacing.xl,
  paddingBottom: 100, // Space for footer
}

const $inputContainer: ViewStyle = {
  marginBottom: spacing.sm,
  width: "100%",
}

const $input: ViewStyle = {
  width: "100%",
}

const $manualEntryOverlay: ViewStyle = {
  flex: 1,
  backgroundColor: "rgba(0, 0, 0, 0.5)",
  justifyContent: "flex-end",
}

const $manualEntryContent: ViewStyle = {
  backgroundColor: colors.palette.neutral100,
  padding: spacing.lg,
  borderTopLeftRadius: 20,
  borderTopRightRadius: 20,
  minHeight: 200,
}

const $errorText: TextStyle = {
  color: colors.palette.angry500,
  fontSize: 12,
  marginTop: 4,
}

const $helperText: TextStyle = {
  color: colors.palette.neutral600,
  fontSize: 12,
  marginTop: 2,
  fontStyle: "italic",
}

const $footer: ViewStyle = {
  position: "absolute",
  bottom: 0,
  left: 0,
  right: 0,
  paddingHorizontal: spacing.lg,
  paddingVertical: spacing.lg,
  borderTopWidth: 1,
  borderTopColor: colors.palette.neutral200,
  backgroundColor: colors.palette.neutral100,
}

const $transferButton: ViewStyle = {
  backgroundColor: colors.palette.neutral900,
  height: 48,
  borderRadius: 20,
}

const $modalOverlay: ViewStyle = {
  flex: 1,
  backgroundColor: "rgba(0, 0, 0, 0.5)",
  justifyContent: "center",
  alignItems: "center",
}

const $modalContent: ViewStyle = {
  backgroundColor: colors.palette.neutral100,
  padding: spacing.xl,
  borderRadius: 20,
  width: "85%",
  alignItems: "center",
  shadowColor: "#000",
  shadowOffset: { width: 0, height: 4 },
  shadowOpacity: 0.2,
  shadowRadius: 8,
  elevation: 5,
}

const $centerContent: ViewStyle = {
  alignItems: "center",
  justifyContent: "center",
}

const $processingText: TextStyle = {
  marginTop: spacing.md,
  fontSize: 16,
  color: colors.palette.neutral900,
}

const $responseText: TextStyle = {
  fontSize: 15,
  textAlign: "center",
  marginBottom: spacing.lg,
  paddingHorizontal: spacing.md,
  lineHeight: 22,
}

const $modalButton: ViewStyle = {
  minWidth: 120,
  borderRadius: 20,
  paddingHorizontal: spacing.lg,
}

const $loaderIconContainer: ViewStyle = {
  width: 80,
  height: 80,
  borderRadius: 40,
  backgroundColor: colors.palette.primary100,
  justifyContent: "center",
  alignItems: "center",
  marginBottom: spacing.md,
}

const $statusIconContainer: ViewStyle = {
  width: 70,
  height: 70,
  borderRadius: 35,
  justifyContent: "center",
  alignItems: "center",
  marginBottom: spacing.md,
}

const $statusTitle: TextStyle = {
  fontSize: 20,
  fontWeight: "bold",
  color: colors.palette.neutral800,
  marginBottom: spacing.xs,
}

const $processingSubText: TextStyle = {
  fontSize: 14,
  color: colors.palette.neutral600,
  textAlign: "center",
  marginTop: spacing.xs,
}

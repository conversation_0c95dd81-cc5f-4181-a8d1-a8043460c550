/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable react-native/no-color-literals */
/* eslint-disable react-native/no-inline-styles */
/* eslint-disable import/no-unresolved */
import {
  ActivityIndicator,
  StyleProp,
  TextStyle,
  TouchableOpacity,
  View,
  ViewStyle,
  Modal,
} from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import { useForm, Controller } from "react-hook-form"
import { $Gstyles, colors, spacing, UserVerificationStyles } from "@/theme"
import { AmountInput, Button, ChangeCurency, FedhaLoader, FencyTextInput, Icon, Text } from "."
import { useState, useEffect } from "react"
import { useStores } from "@/store/rootStore"
import { SnelPowerTopUp } from "@/services/api"
import React from "react"
// import { showElectricityPurchaseNotification } from "@/utils/notificationHelpers"

export interface BuyElectricityProps {
  navigation?: any
  style?: StyleProp<ViewStyle>
}

export const BuyElectricity = (props: BuyElectricityProps) => {
  const {
    appsettings: { currency, getServerCurrencyCode, getExchangeRate, currencies },
    fedhapochi: { currentBalance },
    services: { refreshSNELHistory },
  } = useStores()

  // Currencies are now loaded at app startup
  // No need to fetch currencies here

  const { style, navigation } = props
  const $styles = [$container, style]
  const { themed } = useAppTheme()
  const { input } = UserVerificationStyles
  const [isLoading, setIsLoading] = useState(false)
  const [showCurrencyModal, setShowCurrencyModal] = useState(false)
  const [showConfirmModal, setShowConfirmModal] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)
  const [apiResponse, setApiResponse] = useState<{
    success?: boolean
    message?: string
  } | null>(null)

  const {
    control,
    handleSubmit,
    reset,
    watch,
    setValue,
    formState: { errors, isValid },
  } = useForm({
    defaultValues: {
      meter_no: "",
      amount: "",
      currency: currency,
      note: "",
    },
    mode: "onChange", // Validate on change instead of on submit
  })

  useEffect(() => {
    setValue("currency", currency)
  }, [currency, setValue])

  const handlePurchase = async (formData: any) => {
    try {
      setIsProcessing(true)

      // Format the data according to the required structure
      const requestData = {
        meter_no: formData.meter_no,
        amount: Number(formData.amount), // Convert amount to number
        currency: formData.currency.toLowerCase(), // Ensure currency is lowercase
        note: formData.note || "", // Ensure note is never undefined
      }

      console.log("Purchase Data:", requestData)

      const response = await SnelPowerTopUp(requestData)

      if (response.success) {
        // await showElectricityPurchaseNotification(
        //   `${formData.amount} ${formData.currency}`,
        //   formData.meter_no,
        //   "success",
        // )

        setApiResponse({
          success: true,
          message: "Achat réussi!",
        })

        setTimeout(async () => {
          reset({
            meter_no: "",
            amount: "",
            currency: currency,
            note: "",
          })
          await refreshSNELHistory()
          setShowConfirmModal(false)
          setApiResponse(null)
        }, 2000)
      } else {
        throw new Error(response.message || "La transaction a échoué")
      }
    } catch (error: any) {
      if (__DEV__) {
        console.error("Purchase Error:", error.message || error)
      }

      // await showElectricityPurchaseNotification(
      //   `${formData.amount} ${formData.currency}`,
      //   formData.meter_no,
      //   "failed",
      // )

      setApiResponse({
        success: false,
        message:
          error.message || "Une erreur s'est produite lors de la transaction. Veuillez réessayer.",
      })
    } finally {
      setIsProcessing(false)
    }
  }

  const onSubmit = (data: any) => {
    setShowConfirmModal(true)
    console.log("Submitting with currency:", data.currency)
    setApiResponse(null)
  }

  const meterNumber = watch("meter_no") // Watch the meter number input
  // console.log("Current currencies:", useStores().appsettings.currencies)
  // console.log("From currency code:", getServerCurrencyCode("FC"))
  // console.log("To currency code:", getServerCurrencyCode("USD"))
  // console.log("Exchange rate:", getExchangeRate("FC", "USD"))
  // console.log("900 FC to USD:", getExchangeRate("FC", "USD") * 5000) // Should be ~0.32 USD
  // console.log("1 USD to FC:", getExchangeRate("USD", "FC") * 1) // Should be 2850 FC
  const meterNumberError =
    meterNumber && !/^\d{11}$/.test(meterNumber) // Check if it’s exactly 11 digits
      ? "Le numéro du compteur doit avoir exactement 11 chiffres"
      : null

  return (
    <>
      <View style={$styles}>
        <View style={themed($header)}>
          <Text style={themed($headerText)}>Acheter de l&apos;électricité</Text>
          {/* <Icon
            icon="question"
            size={24}
            onPress={() =>
              navigation.navigate("ElimuHub", { serviceName: "L'achat du SNEL CASHPOWER" })
            }
          /> */}
        </View>

        <Controller
          control={control}
          name="meter_no"
          rules={{
            required: "Le numéro du compteur est obligatoire",
            pattern: {
              value: /^[0-9]{11}$/,
              message: "Le numéro du compteur doit avoir exactement 11 chiffres",
            },
          }}
          render={({ field: { onChange, value }, fieldState: { error } }) => (
            <View>
              <FencyTextInput
                value={value}
                status={error ? "error" : "disabled"}
                onChange={onChange}
                maxLength={11}
                style={[themed(input), error && { borderColor: "red", borderWidth: 1 }]}
                inputname={"Numéro du compteur"}
                keyboardType="numeric"
                placeholder={"Saisir le numéro du compteur"}
              />
              {meterNumberError && (
                <Text style={{ color: "red", marginTop: 5 }}>{meterNumberError}</Text>
              )}
            </View>
          )}
        />

        <View>
          <Controller
            control={control}
            name="amount"
            rules={{
              required: "Le montant est obligatoire",
              validate: (value) => {
                const amount = parseFloat(value)
                if (isNaN(amount)) return "Le montant doit être un nombre valide"

                // Get exchange rate and convert amounts using API rates
                if (currency === "USD") {
                  if (amount < 2) return "Le montant minimum est de 2 USD"
                  // Convert balance to USD using API exchange rate
                  const balanceInUSD = getExchangeRate("FC", "USD") * currentBalance
                  if (amount > balanceInUSD)
                    return `Solde insuffisant. Votre solde est de ${balanceInUSD.toFixed(2)} USD`
                } else if (currency === "FC") {
                  if (amount < 5000) return "Le montant minimum est de 5000 FC"
                  // Convert balance to FC using API exchange rate
                  const balanceInFC = getExchangeRate("USD", "FC") * currentBalance
                  if (amount > balanceInFC)
                    return `Solde insuffisant. Votre solde est de ${balanceInFC.toFixed(2)} FC`
                }
                return true
              },
            }}
            render={({ field: { onChange, value } }) => (
              <FencyTextInput
                value={value}
                onChange={onChange}
                style={themed(input)}
                inputname={`montant en ${currency}`}
                keyboardType="numeric"
                placeholder={"Saisir le montant"}
                LeftIcon={
                  currency === "FC" || (currency as string) === "Congolese Franc" ? "FC" : "dollar"
                }
                leftonPress={() => setShowCurrencyModal(!showCurrencyModal)}
              />
            )}
          />
        </View>
        {errors.amount && <Text style={$errorText}>{errors.amount.message}</Text>}

        <Controller
          control={control}
          name="note"
          render={({ field: { onChange, value }, fieldState: { error } }) => (
            <View>
              <FencyTextInput
                value={value}
                onChange={onChange}
                multiline={true}
                numberOfLines={2}
                style={[themed(input), error && { borderColor: "red", borderWidth: 1 }]}
                inputname={"Message"}
                placeholder={"Optional note"}
              />
            </View>
          )}
        />

        <View>
          <Button
            testID="confirm-button"
            preset="reversed"
            text={isLoading ? "Chargement..." : "Acheter"}
            style={[$Gstyles.buttonStyle, (isLoading || !isValid) && { opacity: 0.5 }]}
            disabled={isLoading || !isValid}
            onPress={handleSubmit(onSubmit)}
          />
        </View>
      </View>
      <ChangeCurency isVisible={showCurrencyModal} onClose={() => setShowCurrencyModal(false)} />
      <Modal
        visible={showConfirmModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowConfirmModal(false)}
      >
        <View style={$modalOverlay}>
          <View style={$modalContent}>
            {isProcessing ? (
              <View style={$centerContent}>
                <FedhaLoader />
                <Text style={$processingText}>Traitement en cours...</Text>
              </View>
            ) : apiResponse ? (
              <View style={$centerContent}>
                <Icon
                  icon={apiResponse.success ? "check" : "trylater"} // Adjust based on your icon names
                  color={colors.palette.neutral600}
                  size={100}
                />
                <Text
                  style={[
                    $responseText,
                    {
                      color: apiResponse.success ? colors.palette.accent400 : colors.palette.Black,
                    },
                  ]}
                >
                  {apiResponse.message}
                </Text>
                {!apiResponse.success && (
                  <View style={$buttonRow}>
                    <Button
                      text="Réessayer"
                      preset="reversed"
                      style={$modalButton}
                      onPress={() => {
                        setApiResponse(null)

                        const formData = watch()
                        console.log("handlePurchase", formData)
                        handlePurchase(formData)
                      }}
                    />
                    <Button
                      text="Annuler"
                      preset="default"
                      style={$modalButton}
                      onPress={() => {
                        setShowConfirmModal(false)
                        setApiResponse(null)
                      }}
                    />
                  </View>
                )}
              </View>
            ) : (
              <>
                <Text style={$confirmTitle}>Confirmer l&lsquo;achat</Text>
                <View style={$detailsContainer}>
                  <Text style={$Info}>
                    Le traitement de votre demande peut prendre jusqu&lsquo;à une heure ou plus.
                    Nous vous enverrons une notification une fois que votre demande aura été
                    traitée.
                  </Text>
                  <Text style={$detailRow}>
                    <Text style={$detailLabel}>Compteur: </Text>
                    {watch("meter_no")}
                  </Text>
                  <Text style={$detailRow}>
                    <Text style={$detailLabel}>Montant: </Text>
                    {watch("amount")} {currency}
                  </Text>
                  {watch("note") && (
                    <Text style={$detailRow}>
                      <Text style={$detailLabel}>Note: </Text>
                      {watch("note")}
                    </Text>
                  )}
                </View>
                <View style={$buttonRow}>
                  <Button
                    text="Confirmer"
                    preset="reversed"
                    style={$modalButton}
                    onPress={() => handlePurchase(watch())}
                  />
                  <Button
                    text="Annuler"
                    preset="default"
                    style={$modalButton}
                    onPress={() => setShowConfirmModal(false)}
                  />
                </View>
              </>
            )}
          </View>
        </View>
      </Modal>
    </>
  )
}

const $container: ViewStyle = {
  justifyContent: "center",
  top: 20,
  paddingBottom: spacing.xl,
  // flex: 1,
}

const $errorText: TextStyle = {
  color: "red",
  fontSize: 12,
  marginBottom: 5,
}

const $header: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
  // marginBottom: 20,
}

const $headerText: TextStyle = {
  fontSize: 18,
  fontWeight: "bold",
}

const $modalOverlay: ViewStyle = {
  flex: 1,
  backgroundColor: "rgba(0, 0, 0, 0.5)",
  justifyContent: "center",
  alignItems: "center",
  padding: spacing.md,
}

const $modalContent: ViewStyle = {
  backgroundColor: "white",
  borderRadius: 8,
  padding: spacing.lg,
  width: "100%",
  maxWidth: 400,
}

const $centerContent: ViewStyle = {
  alignItems: "center",
  padding: spacing.lg,
}

const $confirmTitle: TextStyle = {
  fontSize: 18,
  fontWeight: "bold",
  marginBottom: spacing.md,
  textAlign: "center",
}

const $Info: TextStyle = {
  fontSize: 15,
  marginBottom: spacing.md,
  textAlign: "center",
  color: colors.palette.accent400,
}

const $detailsContainer: ViewStyle = {
  marginVertical: spacing.md,
}

const $detailRow: TextStyle = {
  marginVertical: spacing.xs,
}

const $detailLabel: TextStyle = {
  fontWeight: "bold",
}

const $buttonRow: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
  marginTop: spacing.md,
  gap: spacing.sm,
}

const $modalButton: ViewStyle = {
  flex: 1,
}

const $processingText: TextStyle = {
  marginTop: spacing.md,
  fontSize: 16,
}

const $responseText: TextStyle = {
  fontSize: 16,
  textAlign: "center",
  marginBottom: spacing.md,
}

const $balanceText: TextStyle = {
  fontSize: 14,
  color: colors.palette.neutral600,
  marginBottom: spacing.xs,
}

/* eslint-disable react-native/sort-styles */
/* eslint-disable no-restricted-imports */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable react-native/no-color-literals */
import React, { useState, useEffect } from "react"
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  FlatList,
  TextInput,
  ActivityIndicator,
} from "react-native"
import { colors, spacing, typography } from "@/theme"
import { Icon } from "@/components"
import { useStores } from "@/store/rootStore"

interface Category {
  id: number
  name: string
  description: string
}

interface CategoryDropdownProps {
  value: string
  onValueChange: (value: string) => void
  inputname: string
  placeholder?: string
}

export const CategoryDropdown = ({
  value,
  onValueChange,
  inputname,
  placeholder = "Sélectionner une catégorie",
}: CategoryDropdownProps) => {
  const [modalVisible, setModalVisible] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const { business } = useStores()
  const { categories, loading, fetchBusinessCategories } = business

  useEffect(() => {
    // Safely check if categories exist and fetch if needed
    if (!categories || !Array.isArray(categories) || categories.length === 0) {
      fetchBusinessCategories()
    }
  }, [categories, fetchBusinessCategories])

  // Memoize filtered categories to improve performance with large lists
  const getFilteredCategories = () => {
    // Check if categories is undefined or not an array
    if (!categories || !Array.isArray(categories)) {
      return []
    }

    if (!searchQuery.trim()) {
      // Return first 50 categories when no search query to improve performance
      return categories.slice(0, 50)
    }

    // When searching, filter through all categories
    return categories
      .filter((category: Category) => {
        // Add null checks to prevent errors
        return (
          category &&
          category.name &&
          category.name.toLowerCase().includes(searchQuery.toLowerCase())
        )
      })
      .slice(0, 100) // Limit to 100 results for performance
  }

  const filteredCategories = getFilteredCategories()

  const handleSelectCategory = (categoryName: string) => {
    onValueChange(categoryName)
    setModalVisible(false)
  }

  const renderCategoryItem = ({ item }: { item: Category }) => {
    // Add null checks to prevent errors with undefined items
    if (!item || !item.name) {
      return null
    }

    return (
      <TouchableOpacity style={styles.categoryItem} onPress={() => handleSelectCategory(item.name)}>
        <View style={styles.categoryContent}>
          <Text style={styles.categoryName}>{item.name}</Text>
          {item.description && (
            <Text style={styles.categoryDescription} numberOfLines={2}>
              {item.description}
            </Text>
          )}
        </View>
        {value === item.name && <Icon icon="check" size={20} color={colors.palette.primary500} />}
      </TouchableOpacity>
    )
  }

  return (
    <View style={styles.container}>
      <Text style={styles.label}>{inputname}</Text>
      <TouchableOpacity style={styles.selectorButton} onPress={() => setModalVisible(true)}>
        <Text style={[styles.selectorText, !value && styles.placeholderText]}>
          {value || placeholder}
        </Text>
        <Icon icon="downarrow" size={20} color={colors.palette.neutral600} />
      </TouchableOpacity>

      <Modal
        visible={modalVisible}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Sélectionner une catégorie</Text>
              <TouchableOpacity style={styles.closeButton} onPress={() => setModalVisible(false)}>
                <Icon icon="x" size={24} color={colors.palette.neutral800} />
              </TouchableOpacity>
            </View>

            <View style={styles.searchContainer}>
              <Icon
                icon="search"
                size={20}
                color={colors.palette.neutral500}
                style={styles.searchIcon}
              />
              <TextInput
                style={styles.searchInput}
                placeholder="Rechercher une catégorie..."
                value={searchQuery}
                onChangeText={setSearchQuery}
                autoCapitalize="none"
                autoCorrect={false}
              />
              {searchQuery.length > 0 && (
                <TouchableOpacity onPress={() => setSearchQuery("")}>
                  <Icon icon="x" size={16} color={colors.palette.neutral500} />
                </TouchableOpacity>
              )}
            </View>

            {loading ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={colors.palette.primary500} />
                <Text style={styles.loadingText}>Chargement des catégories...</Text>
              </View>
            ) : (
              <FlatList
                data={filteredCategories}
                renderItem={renderCategoryItem}
                keyExtractor={(item) =>
                  item && item.id ? item.id.toString() : Math.random().toString()
                }
                contentContainerStyle={styles.listContent}
                initialNumToRender={10}
                maxToRenderPerBatch={20}
                windowSize={10}
                removeClippedSubviews={true}
                ListEmptyComponent={
                  <View style={styles.emptyContainer}>
                    <Text style={styles.emptyText}>
                      {searchQuery.length > 0
                        ? `Aucune catégorie trouvée pour "${searchQuery}"`
                        : "Aucune catégorie disponible"}
                    </Text>
                  </View>
                }
              />
            )}
          </View>
        </View>
      </Modal>
    </View>
  )
}

const styles = StyleSheet.create({
  categoryContent: {
    flex: 1,
    marginRight: 8,
  },
  categoryDescription: {
    color: colors.textDim,
    fontSize: 14,
  },
  categoryItem: {
    alignItems: "center",
    borderBottomColor: colors.palette.neutral200,
    borderBottomWidth: 1,
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  categoryName: {
    color: colors.text,
    fontSize: 16,
    fontWeight: "500",
    marginBottom: 4,
  },
  closeButton: {
    padding: spacing.xs,
  },
  container: {
    // marginBottom: spacing.md,
    justifyContent: "center",
    borderRadius: 15,
    marginTop: spacing.md,
    paddingHorizontal: 15,
    paddingVertical: 9,
    borderWidth: 1.5,
  },
  emptyContainer: {
    alignItems: "center",
    padding: 20,
  },
  emptyText: {
    color: colors.textDim,
    fontSize: 16,
    textAlign: "center",
  },
  label: {
    fontFamily: typography.primary.normal,
    fontSize: 16,
    color: "#333",
    fontWeight: "600",
  },
  listContent: {
    paddingBottom: spacing.lg,
  },
  loadingContainer: {
    alignItems: "center",
    padding: 20,
  },
  loadingText: {
    color: colors.textDim,
    marginTop: 10,
  },
  modalContent: {
    backgroundColor: colors.background,
    borderRadius: 12,
    elevation: 5,
    maxHeight: "80%",
    padding: spacing.md,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    width: "100%",
  },
  modalHeader: {
    alignItems: "center",
    borderBottomColor: colors.palette.neutral200,
    borderBottomWidth: 1,
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: spacing.md,
    paddingBottom: spacing.sm,
  },
  modalOverlay: {
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    flex: 1,
    justifyContent: "center",
    padding: spacing.lg,
  },
  modalTitle: {
    color: colors.text,
    fontSize: 18,
    fontWeight: "600",
  },
  placeholderText: {
    color: colors.palette.neutral500,
  },
  searchContainer: {
    alignItems: "center",
    backgroundColor: colors.palette.neutral100,
    borderColor: colors.palette.neutral300,
    borderRadius: 8,
    borderWidth: 1,
    flexDirection: "row",
    marginBottom: spacing.md,
    paddingHorizontal: 12,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    color: colors.text,
    flex: 1,
    fontSize: 16,
    height: 40,
  },
  selectorButton: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "space-between",
    paddingVertical: 12,
  },
  selectorText: {
    color: colors.text,
    fontSize: 16,
  },
})

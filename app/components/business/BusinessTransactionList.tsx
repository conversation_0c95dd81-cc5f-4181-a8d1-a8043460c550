/* eslint-disable no-restricted-imports */
/* eslint-disable react-native/sort-styles */
/* eslint-disable react-native/no-unused-styles */
/* eslint-disable react-native/no-color-literals */
import React, { useState } from "react"
import {
  View,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  Modal,
  ActivityIndicator,
} from "react-native"
import { Text, Icon } from "@/components"
import { colors, spacing } from "@/theme"
import { BusinessTransaction } from "@/store/BusinessTransactionStore"
import { formatDate, formatDateTime } from "@/utils/dateUtils"

interface BusinessTransactionListProps {
  transactions: BusinessTransaction[]
  isLoading: boolean
  onRefresh?: () => void
  isRefreshing?: boolean
}

export const BusinessTransactionList: React.FC<BusinessTransactionListProps> = ({
  transactions,
  isLoading,
  onRefresh,
  isRefreshing = false,
}) => {
  const [selectedTransaction, setSelectedTransaction] = useState<BusinessTransaction | null>(null)
  const [modalVisible, setModalVisible] = useState(false)

  const handleTransactionPress = (transaction: BusinessTransaction) => {
    setSelectedTransaction(transaction)
    setModalVisible(true)
  }

  const closeModal = () => {
    setModalVisible(false)
    setSelectedTransaction(null)
  }

  const renderTransactionItem = ({ item }: { item: BusinessTransaction }) => {
    const isCredit = item.transaction_action === "deposit"
    const amount = parseFloat(item.amount)
    const formattedAmount = amount.toLocaleString("fr-CD", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })
    const currency = typeof item.currency === "number" ? "FC" : item.currency || "FC"
    const date = new Date(item.created_at)
    const formattedDate = formatDate(date.toISOString())

    return (
      <TouchableOpacity style={styles.transactionItem} onPress={() => handleTransactionPress(item)}>
        <View style={styles.transactionLeft}>
          <View
            style={[
              styles.iconContainer,
              {
                backgroundColor: isCredit ? colors.palette.primary500 : colors.palette.angry100,
              },
            ]}
          >
            <Icon
              icon={isCredit ? "increase" : "decrease"}
              size={20}
              color={isCredit ? colors.palette.neutral900 : colors.palette.angry500}
            />
          </View>
          <View style={styles.transactionInfo}>
            <Text style={styles.transactionType}>
              {isCredit ? "Entrée" : "Sortie"}
              {item.transaction_type === "service" && ` - ${item.service_name || "Service"}`}
            </Text>
            <Text style={styles.transactionDate}>{formattedDate}</Text>
          </View>
        </View>
        <View style={styles.transactionRight}>
          <Text
            style={[
              styles.transactionAmount,
              {
                color: isCredit ? colors.palette.neutral900 : colors.palette.angry500,
              },
            ]}
          >
            {isCredit ? "+" : "-"} {formattedAmount}
          </Text>
          <Text style={styles.currencyCode}>{currency}</Text>
        </View>
      </TouchableOpacity>
    )
  }

  if (isLoading && !isRefreshing && (!transactions || transactions.length === 0)) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.palette.neutral900} />
        <Text style={styles.loadingText}>Chargement des transactions...</Text>
      </View>
    )
  }

  if (!transactions || transactions.length === 0) {
    return (
      <View style={styles.emptyContainer}>
        <Icon icon="nocontent" size={64} color={colors.palette.neutral400} />
        <Text style={styles.emptyText}>Aucune transaction disponible</Text>
      </View>
    )
  }

  return (
    <View style={styles.container}>
      {/* <Text style={styles.sectionTitle}></Text> */}
      <FlatList
        data={transactions}
        renderItem={renderTransactionItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContent}
        onRefresh={onRefresh}
        refreshing={isRefreshing}
      />

      {/* Transaction Details Modal */}
      <Modal visible={modalVisible} transparent animationType="slide" onRequestClose={closeModal}>
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            {/* Header */}
            <View style={styles.modalHeader}>
              <View style={styles.modalHeaderContent}>
                <Icon
                  icon={
                    selectedTransaction?.transaction_action === "deposit" ? "increase" : "decrease"
                  }
                  size={32}
                  color={
                    selectedTransaction?.transaction_action === "deposit"
                      ? colors.palette.neutral900
                      : colors.palette.angry500
                  }
                />
                <View>
                  <Text style={styles.modalTitle}>Détails de la transaction</Text>
                  <Text style={styles.modalSubtitle}>
                    {selectedTransaction?.transaction_action === "deposit" ? "Entrée" : "Sortie"}
                  </Text>
                </View>
              </View>
              <TouchableOpacity onPress={closeModal} style={styles.closeButton}>
                <Icon icon="x" size={24} color={colors.palette.neutral800} />
              </TouchableOpacity>
            </View>

            {/* Transaction Details */}
            {selectedTransaction && (
              <View style={styles.detailsContainer}>
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>ID Transaction</Text>
                  <Text style={styles.detailValue}>{selectedTransaction.id}</Text>
                </View>

                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Date</Text>
                  <Text style={styles.detailValue}>
                    {formatDateTime(new Date(selectedTransaction.created_at).toISOString())}
                  </Text>
                </View>

                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Montant</Text>
                  <Text
                    style={[
                      styles.detailValue,
                      {
                        color:
                          selectedTransaction.transaction_action === "deposit"
                            ? colors.palette.neutral900
                            : colors.palette.angry500,
                      },
                    ]}
                  >
                    {selectedTransaction.transaction_action === "deposit" ? "+" : "-"}{" "}
                    {parseFloat(selectedTransaction.amount).toLocaleString("fr-CD", {
                      minimumFractionDigits: 2,
                      maximumFractionDigits: 2,
                    })}{" "}
                    {typeof selectedTransaction.currency === "number"
                      ? "FC"
                      : selectedTransaction.currency || "FC"}
                  </Text>
                </View>

                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Type</Text>
                  <Text style={styles.detailValue}>
                    {selectedTransaction.transaction_type === "transfer"
                      ? "Transfert"
                      : selectedTransaction.transaction_type === "withdraw"
                        ? "Retrait"
                        : selectedTransaction.transaction_type === "deposit"
                          ? "Dépôt"
                          : selectedTransaction.transaction_type === "service"
                            ? "Service"
                            : selectedTransaction.transaction_type}
                  </Text>
                </View>

                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Statut</Text>
                  <View
                    style={[
                      styles.statusBadge,
                      {
                        backgroundColor:
                          selectedTransaction.status === "completed"
                            ? colors.palette.primary500
                            : selectedTransaction.status === "pending" ||
                                selectedTransaction.status === "in_progress"
                              ? colors.palette.angry100
                              : colors.palette.angry100,
                      },
                    ]}
                  >
                    <Text
                      style={[
                        styles.statusText,
                        {
                          color:
                            selectedTransaction.status === "completed"
                              ? colors.palette.neutral900
                              : selectedTransaction.status === "pending" ||
                                  selectedTransaction.status === "in_progress"
                                ? colors.palette.secondary100
                                : colors.palette.angry500,
                        },
                      ]}
                    >
                      {selectedTransaction.status === "completed"
                        ? "Complété"
                        : selectedTransaction.status === "pending"
                          ? "En attente"
                          : selectedTransaction.status === "in_progress"
                            ? "En cours"
                            : selectedTransaction.status === "failed"
                              ? "Échoué"
                              : selectedTransaction.status}
                    </Text>
                  </View>
                </View>

                {selectedTransaction.note && (
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>Note</Text>
                    <Text style={styles.detailValue}>{selectedTransaction.note}</Text>
                  </View>
                )}

                {selectedTransaction.receiver && (
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>Destinataire</Text>
                    <Text style={styles.detailValue}>{selectedTransaction.receiver}</Text>
                  </View>
                )}

                {selectedTransaction.sender && (
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>Expéditeur</Text>
                    <Text style={styles.detailValue}>{selectedTransaction.sender}</Text>
                  </View>
                )}
              </View>
            )}
          </View>
        </View>
      </Modal>
    </View>
  )
}

const styles = StyleSheet.create({
  closeButton: {
    padding: spacing.xs,
  },
  container: {
    flex: 1,
  },
  currencyCode: {
    color: colors.palette.neutral600,
    fontSize: 14,
    marginTop: 2,
  },
  detailLabel: {
    color: colors.palette.neutral600,
    fontSize: 16,
  },
  detailRow: {
    alignItems: "center",
    borderBottomColor: colors.palette.neutral300,
    borderBottomWidth: 1,
    flexDirection: "row",
    justifyContent: "space-between",
    paddingVertical: spacing.sm,
  },
  detailValue: {
    color: colors.palette.neutral800,
    fontSize: 16,
    fontWeight: "500",
    maxWidth: "60%",
    textAlign: "right",
  },
  detailsContainer: {
    padding: spacing.md,
  },
  emptyContainer: {
    alignItems: "center",
    flex: 1,
    justifyContent: "center",
    padding: spacing.xl,
  },
  emptyText: {
    color: colors.palette.neutral600,
    marginTop: spacing.md,
    textAlign: "center",
  },
  iconContainer: {
    alignItems: "center",
    borderRadius: 20,
    height: 40,
    justifyContent: "center",
    marginRight: spacing.sm,
    width: 40,
  },
  listContent: {
    paddingBottom: spacing.xl,
    paddingHorizontal: spacing.md,
  },
  loadingContainer: {
    alignItems: "center",
    flex: 1,
    justifyContent: "center",
    padding: spacing.xl,
  },
  loadingText: {
    color: colors.palette.neutral600,
    marginTop: spacing.md,
  },
  modalContent: {
    backgroundColor: colors.palette.neutral100,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: 30, // Extra padding for bottom safe area
    maxHeight: "80%",
  },
  modalHeader: {
    alignItems: "center",
    borderBottomColor: colors.palette.neutral300,
    borderBottomWidth: 1,
    flexDirection: "row",
    justifyContent: "space-between",
    padding: spacing.md,
  },
  modalHeaderContent: {
    alignItems: "center",
    flexDirection: "row",
  },
  modalOverlay: {
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    flex: 1,
    justifyContent: "flex-end",
  },
  modalSubtitle: {
    color: colors.palette.neutral600,
    fontSize: 14,
  },
  modalTitle: {
    color: colors.palette.neutral800,
    fontSize: 18,
    fontWeight: "bold",
  },
  sectionTitle: {
    color: colors.palette.neutral800,
    fontSize: 18,
    fontWeight: "bold",
    marginHorizontal: spacing.md,
    marginVertical: spacing.sm,
  },
  statusBadge: {
    borderRadius: 12,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
  },
  statusText: {
    fontSize: 14,
    fontWeight: "500",
  },
  transactionAmount: {
    fontSize: 16,
    fontWeight: "bold",
  },
  transactionDate: {
    color: colors.palette.neutral600,
    fontSize: 14,
    marginTop: 2,
  },
  transactionInfo: {
    flex: 1,
  },
  transactionItem: {
    alignItems: "center",
    borderBottomColor: colors.palette.neutral300,
    borderBottomWidth: 1,
    flexDirection: "row",
    justifyContent: "space-between",
    paddingVertical: spacing.sm,
  },
  transactionLeft: {
    alignItems: "center",
    flexDirection: "row",
    flex: 1,
  },
  transactionRight: {
    alignItems: "flex-end",
  },
  transactionType: {
    color: colors.palette.neutral800,
    fontSize: 16,
    fontWeight: "500",
  },
})

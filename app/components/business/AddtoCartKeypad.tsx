/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable no-restricted-imports */
/* eslint-disable react-native/no-color-literals */
/* eslint-disable react-native/sort-styles */
import React, { useState, useEffect } from "react"
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  TextInput,
  Modal,
  Alert,
  Animated,
  Platform,
  ViewStyle,
  TextStyle,
} from "react-native"
import { colors, spacing } from "@/theme"
import { Icon } from "../atoms/Icon"

interface AddToCartKeypadProps {
  disabled?: boolean
  onAddItem: (item: { price: number; name: string; description: string; currency: string }) => void
}

const defaultCurrency = "FC"
const animationDuration = 300
const maxPriceLength = 10 // Adjust as needed

export const AddToCartKeypad: React.FC<AddToCartKeypadProps> = ({ onAddItem }) => {
  const [priceInput, setPriceInput] = useState("0.00")
  const [showItemDetailsModal, setShowItemDetailsModal] = useState(false)
  const [itemName, setItemName] = useState("Article") // Default French name
  const [itemDescription, setItemDescription] = useState("")
  const [currentDescription, setCurrentDescription] = useState("") // For the text input
  const [hasItemDetails, setHasItemDetails] = useState(false) // New state to track if item details have been added
  const [itemAddedAnimated, setItemAddedAnimated] = useState(new Animated.Value(0))

  useEffect(() => {
    // Update hasItemDetails whenever itemName or itemDescription changes
    if (itemName !== "Article" || itemDescription) {
      setHasItemDetails(true)
    } else {
      setHasItemDetails(false)
    }
  }, [itemName, itemDescription])

  const handleNumberInput = (number: string) => {
    if (priceInput.length < maxPriceLength) {
      if (priceInput === "0.00") {
        setPriceInput(number)
      } else {
        setPriceInput((prev) => prev + number)
      }
    }
  }

  const handleClearInput = () => {
    setPriceInput("0.00")
    setCurrentDescription("")
    setHasItemDetails(false) // Reset item details flag on clear
    setItemName("Article")
    setItemDescription("")
  }

  const handleOpenItemDetailsModal = () => {
    if (parseFloat(priceInput) > 0) {
      setItemDescription(currentDescription)
      setShowItemDetailsModal(true)
    } else {
      Alert.alert("Erreur", "Veuillez entrer un prix supérieur à zéro avant d'ajouter les détails.")
    }
  }

  const handleFinalAddItem = () => {
    if (itemDescription) {
      setCurrentDescription(itemDescription)
    } else if (itemName !== "Article") {
      setCurrentDescription(itemName)
    } else {
      setCurrentDescription("Article")
    }
    setShowItemDetailsModal(false)
    // hasItemDetails is already updated in the useEffect hook
  }

  const handleAddToCart = () => {
    const price = parseFloat(priceInput)
    if (isNaN(price) || price <= 0) {
      Alert.alert(
        "Erreur",
        "Veuillez entrer un prix valide supérieur à zéro avant d'ajouter au panier.",
      )
      return
    }

    onAddItem({
      price,
      name: itemName,
      description: itemDescription,
      currency: defaultCurrency,
    })
    setPriceInput("0.00")
    setItemName("Article")
    setItemDescription("")
    setCurrentDescription("")
    setHasItemDetails(false) // Reset item details flag after adding to cart
    triggerItemAddedAnimation()
  }

  const triggerItemAddedAnimation = () => {
    Animated.sequence([
      Animated.timing(itemAddedAnimated, {
        toValue: 1,
        duration: animationDuration,
        useNativeDriver: true,
      }),
      Animated.delay(500),
      Animated.timing(itemAddedAnimated, {
        toValue: 0,
        duration: animationDuration,
        useNativeDriver: true,
      }),
    ]).start()
  }

  const handleCloseModal = () => {
    setShowItemDetailsModal(false)
  }

  const itemAddedOpacity = itemAddedAnimated.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 1],
  })

  const isactiv = parseFloat(priceInput) > 0

  // console.log('isA', isactiv)

  return (
    <View style={styles.container}>
      <View style={styles.priceContainer}>
        <Text style={styles.currency}>{defaultCurrency}</Text>
        <TextInput
          style={styles.priceInput}
          value={priceInput}
          keyboardType="numeric"
          editable={false}
          textAlign="center" // Align price in the center
        />
      </View>

      <View style={styles.descriptionRow}>
        <TextInput
          style={styles.descriptionInput}
          placeholder="Ajouter une description (Optionnel)"
          value={currentDescription}
          onChangeText={setCurrentDescription}
          editable={false}
        />
        <TouchableOpacity
          style={[
            styles.addDescriptionButton,
            {
              backgroundColor: isactiv ? colors.palette.neutral900 : colors.palette.neutral400,
            },
          ]}
          onPress={handleOpenItemDetailsModal}
        >
          <Text style={styles.addDescriptionButtonText}>
            {hasItemDetails ? "Modifier" : "Ajouter"}
          </Text>
        </TouchableOpacity>
      </View>

      {/* Keypad remains the same */}
      <View style={styles.keypadRow}>
        {[1, 2, 3].map((num) => (
          <TouchableOpacity
            key={num}
            style={styles.keypadButton}
            onPress={() => handleNumberInput(String(num))}
          >
            <Text style={styles.keypadButtonText}>{num}</Text>
          </TouchableOpacity>
        ))}
      </View>
      <View style={styles.keypadRow}>
        {[4, 5, 6].map((num) => (
          <TouchableOpacity
            key={num}
            style={styles.keypadButton}
            onPress={() => handleNumberInput(String(num))}
          >
            <Text style={styles.keypadButtonText}>{num}</Text>
          </TouchableOpacity>
        ))}
      </View>
      <View style={styles.keypadRow}>
        {[7, 8, 9].map((num) => (
          <TouchableOpacity
            key={num}
            style={styles.keypadButton}
            onPress={() => handleNumberInput(String(num))}
          >
            <Text style={styles.keypadButtonText}>{num}</Text>
          </TouchableOpacity>
        ))}
      </View>
      <View style={styles.keypadRow}>
        <TouchableOpacity style={styles.keypadButtonDouble} onPress={() => handleNumberInput("00")}>
          <Text style={styles.keypadButtonText}>00</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.keypadButton} onPress={() => handleNumberInput("0")}>
          <Text style={styles.keypadButtonText}>0</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.keypadButtonClear} onPress={handleClearInput}>
          <Icon icon={"delete"} size={38} color={colors.palette.neutral900} />
        </TouchableOpacity>
      </View>

      <Animated.View
        style={[
          styles.itemAddedAnimation,
          { opacity: itemAddedOpacity },
          {
            transform: [
              {
                translateY: itemAddedAnimated.interpolate({
                  inputRange: [0, 1],
                  outputRange: [20, 0],
                }),
              },
            ],
          },
        ]}
      >
        <Icon icon={"addedincart"} size={28} color={colors.palette.neutral900} />
        <Text style={styles.itemAddedText}>Article ajouté!</Text>
      </Animated.View>

      <TouchableOpacity style={styles.addItemButton} onPress={handleAddToCart}>
        <Text style={styles.addItemButtonText}>{"Ajouter au panier"}</Text>
      </TouchableOpacity>

      <Modal visible={showItemDetailsModal} animationType="slide" transparent>
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <Text style={styles.modalTitle}>Détails de l&lsquo;article</Text>
            <TextInput
              style={styles.modalInput}
              placeholder="Nom de l'article (Optionnel)"
              value={itemName}
              onChangeText={setItemName}
            />
            <TextInput
              style={[styles.modalInput, styles.modalDescriptionInput]}
              placeholder="Description de l'article (Optionnel)"
              value={itemDescription}
              onChangeText={setItemDescription}
              multiline
            />
            <View style={styles.modalButtons}>
              <TouchableOpacity style={$modalButton} onPress={handleCloseModal} activeOpacity={0.7}>
                <Text style={[$modalButtonText, $secondaryButtonText]}>Annuler</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[$modalButton, $secondaryButton]}
                onPress={handleFinalAddItem}
                activeOpacity={0.7}
              >
                <Text style={[$modalButtonText, $secondaryButtonText]}>Enregistrer</Text>
              </TouchableOpacity>
              {/* <Button
                title="Annuler"
                onPress={handleCloseModal}
                color={colors.palette.neutral400}
                style={styles.mbuttons}
              />
              <Button
                title="Enregistrer"
                onPress={handleFinalAddItem}
                color={colors.palette.primary500}
              /> */}
            </View>
          </View>
        </View>
      </Modal>
    </View>
  )
}

const $modalButtonsContainer: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
  width: "100%",
  marginTop: spacing.sm,
}

const $modalButton: ViewStyle = {
  backgroundColor: colors.palette.accent500,
  paddingVertical: spacing.sm,
  paddingHorizontal: spacing.md,
  borderRadius: 25,
  flex: 1,
  marginHorizontal: spacing.xs,
  alignItems: "center",
}

const $secondaryButton: ViewStyle = {
  backgroundColor: colors.palette.neutral900,
}

const $modalButtonText: TextStyle = {
  color: colors.palette.neutral100,

  fontSize: 16,
  fontWeight: "600",
  textAlign: "center",
}

const $secondaryButtonText: TextStyle = {
  color: colors.palette.neutral100,
}

const buttonShadow =
  Platform.OS === "ios"
    ? {
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 3,
      }
    : {
        elevation: 3,
      }

const styles = StyleSheet.create({
  container: {
    padding: 16,
    // backgroundColor: "#f7f7f7",
    borderRadius: 8,
  },
  priceContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
    paddingVertical: 15,
    paddingHorizontal: 12,
    justifyContent: "center",
    alignContent: "center",
  },
  currency: {
    fontSize: 32, // Make currency more visible
    fontWeight: "bold",
    marginRight: 8,
    color: colors.palette.neutral900,
  },
  priceInput: {
    flex: 1,
    fontSize: 48, // Make price really visible
    color: colors.palette.neutral900,
  },
  descriptionRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
    borderRadius: 10,
    // borderRadius: 10,
    // borderWidth: 1,
    backgroundColor: colors.palette.neutral300,
    padding: spacing.sm,
    // borderColor: "#ddd",
  },
  descriptionInput: {
    flex: 1,
    paddingVertical: 10,
    paddingHorizontal: 12,
    backgroundColor: colors.palette.neutral300,
    color: colors.palette.neutral700,
    marginRight: 8,
  },
  addDescriptionButton: {
    backgroundColor: colors.palette.neutral400,
    borderRadius: 9,
    paddingVertical: 10,
    paddingHorizontal: 11,
    ...buttonShadow, // Add shadow
  },
  addDescriptionButtonText: {
    fontSize: 16,
    color: "#FFF",
  },
  keypadRow: {
    flexDirection: "row",
    justifyContent: "space-around",
    marginBottom: 10,
  },
  keypadButton: {
    backgroundColor: "white",
    borderRadius: 12, // More rounded buttons
    paddingVertical: 18, // Larger padding
    alignItems: "center",
    justifyContent: "center",
    flex: 1,
    marginHorizontal: 5,
    // ...buttonShadow, // Add shadow
  },
  keypadButtonDouble: {
    backgroundColor: "white",
    borderRadius: 12,
    paddingVertical: 18,
    alignItems: "center",
    justifyContent: "center",
    flex: 2,
    marginHorizontal: 5,
    // ...buttonShadow, // Add shadow
  },
  keypadButtonClear: {
    paddingVertical: 15,
    alignItems: "center",
    justifyContent: "center",
    flex: 1,
    marginHorizontal: 5,
    borderRadius: 12,
    backgroundColor: colors.palette.neutral200,
    ...buttonShadow,
  },
  keypadButtonText: {
    fontSize: 24, // Larger text
    color: colors.palette.neutral900,
  },
  addItemButton: {
    backgroundColor: colors.palette.neutral900,
    borderRadius: 12,
    paddingVertical: 20,
    alignItems: "center",
    justifyContent: "center",
    marginTop: 20,
    ...buttonShadow, // Add shadow
  },
  addItemButtonText: {
    fontSize: 22,
    fontWeight: "bold",
    color: "#fff",
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  modalContainer: {
    backgroundColor: "white",
    padding: 20,
    borderRadius: 10,
    width: "80%",
    ...buttonShadow, // Add shadow to modal
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 15,
    color: colors.palette.neutral900,
    textAlign: "center",
  },
  modalInput: {
    borderWidth: 1,
    borderColor: "#ccc",
    borderRadius: 8,
    padding: 12,
    marginBottom: 10,
    color: colors.palette.neutral700,
  },
  modalDescriptionInput: {
    minHeight: 80,
    textAlignVertical: "top",
  },
  modalButtons: {
    flexDirection: "row",
    justifyContent: "space-around",
    marginTop: 20,
  },
  itemAddedAnimation: {
    position: "absolute",
    left: "80%",
    transform: [{ translateX: -55 }],
    alignItems: "center",
  },
  itemAddedText: {
    marginTop: 15,
    fontSize: 16,
    color: colors.palette.primary500,
  },
})

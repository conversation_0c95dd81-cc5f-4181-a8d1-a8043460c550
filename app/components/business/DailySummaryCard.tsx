import React from "react"
import { View, ViewStyle, TextStyle, StyleSheet } from "react-native"
import { Text, Card, Icon } from "@/components"
import { colors, spacing } from "@/theme"
import { DailySummary } from "@/store/BusinessTransactionStore"

interface DailySummaryCardProps {
  summary: DailySummary | null
  isLoading: boolean
}

export const DailySummaryCard: React.FC<DailySummaryCardProps> = ({ summary, isLoading }) => {
  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Chargement du résumé...</Text>
      </View>
    )
  }

  if (!summary) {
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyText}>Aucune donnée disponible</Text>
      </View>
    )
  }

  const formatAmount = (amount: number) => {
    return amount.toLocaleString("fr-CD", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })
  }

  const formattedDate = new Date(summary.date).toLocaleDateString("fr-CD", {
    weekday: "long",
    year: "numeric",
    month: "long",
    day: "numeric",
  })

  return (
    <Card style={styles.card}>
      <View style={styles.header}>
        <Text style={styles.title}>Résumé du jour</Text>
        <Text style={styles.date}>{formattedDate}</Text>
      </View>

      <View style={styles.summaryContainer}>
        <View style={styles.summaryItem}>
          <View style={styles.iconContainer}>
            <Icon icon="increase" size={24} color={colors.palette.success500} />
          </View>
          <View style={styles.summaryContent}>
            <Text style={styles.summaryLabel}>Entrées</Text>
            <Text style={styles.summaryValue}>
              FC {formatAmount(summary.totalIn)}
            </Text>
          </View>
        </View>

        <View style={styles.summaryItem}>
          <View style={styles.iconContainer}>
            <Icon icon="decrease" size={24} color={colors.palette.angry500} />
          </View>
          <View style={styles.summaryContent}>
            <Text style={styles.summaryLabel}>Sorties</Text>
            <Text style={styles.summaryValue}>
              FC {formatAmount(summary.totalOut)}
            </Text>
          </View>
        </View>

        <View style={styles.divider} />

        <View style={styles.summaryItem}>
          <View style={styles.iconContainer}>
            <Icon icon="wallet" size={24} color={colors.palette.primary500} />
          </View>
          <View style={styles.summaryContent}>
            <Text style={styles.summaryLabel}>Dernier encaissement</Text>
            <Text style={styles.summaryValue}>
              FC {formatAmount(summary.lastCashupAmount)}
            </Text>
          </View>
        </View>
      </View>
    </Card>
  )
}

const styles = StyleSheet.create({
  card: {
    marginHorizontal: spacing.md,
    marginVertical: spacing.sm,
    padding: spacing.md,
    borderRadius: 12,
    backgroundColor: colors.palette.neutral100,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  header: {
    marginBottom: spacing.md,
  },
  title: {
    fontSize: 18,
    fontWeight: "bold",
    color: colors.palette.neutral800,
  },
  date: {
    fontSize: 14,
    color: colors.palette.neutral600,
    marginTop: spacing.xs,
  },
  summaryContainer: {
    gap: spacing.sm,
  },
  summaryItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: spacing.xs,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.palette.neutral200,
    justifyContent: "center",
    alignItems: "center",
    marginRight: spacing.sm,
  },
  summaryContent: {
    flex: 1,
  },
  summaryLabel: {
    fontSize: 14,
    color: colors.palette.neutral600,
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: "bold",
    color: colors.palette.neutral800,
  },
  divider: {
    height: 1,
    backgroundColor: colors.palette.neutral300,
    marginVertical: spacing.sm,
  },
  loadingContainer: {
    padding: spacing.lg,
    alignItems: "center",
    justifyContent: "center",
  },
  loadingText: {
    color: colors.palette.neutral600,
  },
  emptyContainer: {
    padding: spacing.lg,
    alignItems: "center",
    justifyContent: "center",
  },
  emptyText: {
    color: colors.palette.neutral600,
  },
})

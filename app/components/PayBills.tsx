/* eslint-disable import/no-unresolved */
import { StyleProp, View, ViewStyle } from "react-native"
// import { useAppTheme } from "@/utils/useAppTheme"
// import type { ThemedStyle } from "@/theme"
import { BuyElectricity } from "."
import { spacing } from "@/theme"

export interface PayBillsProps {
  navigation: any
  style?: StyleProp<ViewStyle>
}

/**
 * Describe your component here
 * 
 */

export const PayBills = (props: PayBillsProps) => {
  const { style, navigation } = props
  const $styles = [$container, style]
  // const { themed } = useAppTheme()

  return (
    <View style={$styles}>
      <BuyElectricity navigation={navigation} />
    </View>
  )
}

const $container: ViewStyle = {
  // flex: 1,
  paddingBottom: spacing.xl,
  paddingHorizontal: spacing.lg,
}

/* eslint-disable react-native/no-inline-styles */
/* eslint-disable import/no-unresolved */
import { StyleProp, TouchableOpacity, View, ViewStyle } from "react-native"
import { colors, spacing } from "@/theme"
import { Icon, Text } from "."

export interface QuickActionsProps {
  navigation: any
  style?: StyleProp<ViewStyle>
}

/**
 * Describe your component here
 */

export const QuickActions = (props: QuickActionsProps) => {
  const { style, navigation } = props
  const $styles = [$container, style]
  // const { themed } = useAppTheme()
  return (
    <View style={$styles}>
      <TouchableOpacity style={$ActinBtn} onPress={() => navigation.navigate("Transfer")}>
        <Icon
          icon={"transfer"} // Adjust based on your icon names
          color={colors.palette.neutral900}
          size={30}
          containerStyle={$ActionIcon}
        />
        <Text
          preset="subheading"
          style={{ fontSize: 14, color: colors.palette.neutral900, fontWeight: "600" }}
        >
          Transfert{" "}
        </Text>
      </TouchableOpacity>
      <TouchableOpacity style={$ActinBtn} onPress={() => navigation.navigate("Pay")}>
        <Icon
          icon={"qrcodewinc"} // Adjust based on your icon names
          color={colors.palette.neutral900}
          size={30}
          containerStyle={$ActionIcon}
        />
        <Text
          preset="subheading"
          style={{ fontSize: 14, color: colors.palette.neutral900, fontWeight: "600" }}
        >
          Payer
        </Text>
      </TouchableOpacity>
      <TouchableOpacity style={$ActinBtn} onPress={() => navigation.navigate("Withdraw")}>
        <Icon
          icon={"withdrawal"} // Adjust based on your icon names
          color={colors.palette.neutral900}
          size={30}
          containerStyle={$ActionIcon}
        />
        <Text
          preset="subheading"
          style={{ fontSize: 14, color: colors.palette.neutral900, fontWeight: "600" }}
        >
          Retirer{" "}
        </Text>
      </TouchableOpacity>
    </View>
  )
}

const $container: ViewStyle = {
  flexDirection: "row",
  paddingVertical: spacing.xs,
  // marginTop: 20,
  // bottom: spacing.lg,
  position: "relative",
  // marginHorizontal: spacing.md,
  // bottom: 0,
  borderWidth: 1,
  top: spacing.sm,
  borderRadius: 20,
  borderColor: colors.palette.neutral800,
  alignContent: "center",
  // borderRadius: 20,
  //  top: spacing.lg,
  justifyContent: "space-between",
}

const $ActinBtn: ViewStyle = {
  alignItems: "center",
  flex: 1,
  padding: 5,
  paddingHorizontal: spacing.md,
}

const $ActionIcon: ViewStyle = {
  // marginHorizontal: spacing.md
  // paddingHorizontal: spacing.lg,
}

/* eslint-disable react-native/no-inline-styles */
import React from "react"
import {
  Modal,
  View,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  Image,
  ViewStyle,
  TextStyle,
  ImageStyle,
} from "react-native"
import { Text, Icon, Button } from "."
import { colors, spacing } from "@/theme"

// Try to import LottieView, but handle the case where it might not be available
let LottieView: any
try {
  LottieView = require("lottie-react-native").default
} catch (error) {
  console.log("Lottie not available:", error)
  LottieView = null
}

const { width } = Dimensions.get("window")

interface ComingSoonModalProps {
  visible: boolean
  onClose: () => void
  serviceName: string
  serviceIcon?: string
  description?: string
  useLottie?: boolean
}

export const ComingSoonModal = ({
  visible,
  onClose,
  serviceName,
  serviceIcon,
  description,
  useLottie = true,
}: ComingSoonModalProps) => {
  return (
    <Modal visible={visible} transparent animationType="fade">
      <View style={styles.overlay}>
        <View style={styles.modalContainer}>
          {/* Close button */}
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <Icon icon="x" size={24} color={colors.palette.neutral600} />
          </TouchableOpacity>

          {/* Service icon */}
          <View style={styles.iconContainer}>
            {serviceIcon ? (
              <Icon icon={serviceIcon as any} size={40} color={colors.palette.primary500} />
            ) : (
              <Icon icon="info" size={40} color={colors.palette.primary500} />
            )}
          </View>

          {/* Title */}
          <Text preset="heading" style={styles.title}>
            {serviceName}
          </Text>

          {/* Coming soon text */}
          <Text preset="subheading" style={styles.comingSoonText}>
            Bientôt disponible
          </Text>

          {/* Description */}
          <Text style={styles.description}>
            {description ||
              `Nous travaillons actuellement sur cette fonctionnalité pour vous offrir une expérience exceptionnelle. Restez à l'écoute!`}
          </Text>

          {/* Animation */}
          <View style={styles.animationContainer}>
            {useLottie && LottieView ? (
              <LottieView
                source={require("../../assets/animations/coming-soon.json")}
                autoPlay
                loop
                style={styles.lottieAnimation}
              />
            ) : (
              <View style={styles.gifPlaceholder}>
                <Icon icon="loading" size={50} color={colors.palette.primary500} />
                <Text style={styles.placeholderText}>En préparation...</Text>
              </View>
            )}
          </View>

          {/* Button */}
          <Button
            text="J'ai compris"
            preset="reversed"
            onPress={onClose}
            style={styles.button}
          />
        </View>
      </View>
    </Modal>
  )
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  modalContainer: {
    width: width * 0.85,
    backgroundColor: colors.palette.neutral100,
    borderRadius: 20,
    padding: spacing.lg,
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  closeButton: {
    position: "absolute",
    top: spacing.sm,
    right: spacing.sm,
    padding: spacing.xs,
    zIndex: 1,
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: colors.palette.primary100,
    justifyContent: "center",
    alignItems: "center",
    marginTop: spacing.lg,
    marginBottom: spacing.md,
  },
  title: {
    fontSize: 22,
    fontWeight: "bold",
    color: colors.palette.neutral800,
    textAlign: "center",
    marginBottom: spacing.xs,
  },
  comingSoonText: {
    fontSize: 18,
    color: colors.palette.primary500,
    fontWeight: "600",
    textAlign: "center",
    marginBottom: spacing.md,
  },
  description: {
    fontSize: 16,
    color: colors.palette.neutral600,
    textAlign: "center",
    marginBottom: spacing.lg,
    paddingHorizontal: spacing.sm,
  },
  animationContainer: {
    height: 150,
    width: "100%",
    marginBottom: spacing.lg,
    justifyContent: "center",
    alignItems: "center",
  },
  lottieAnimation: {
    width: 200,
    height: 150,
  },
  animation: {
    width: 200,
    height: 150,
  },
  gifPlaceholder: {
    width: 200,
    height: 150,
    backgroundColor: colors.palette.primary100,
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
  },
  placeholderText: {
    marginTop: spacing.sm,
    color: colors.palette.primary500,
    fontWeight: "600",
  },
  button: {
    minWidth: 200,
    marginTop: spacing.sm,
  },
})

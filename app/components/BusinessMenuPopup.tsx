/* eslint-disable react-native/sort-styles */
/* eslint-disable no-restricted-imports */
/* eslint-disable react-native/no-inline-styles */
/* eslint-disable react-native/no-color-literals */
import React from "react"
import {
  Modal,
  View,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  Animated,
  Linking,
  Alert,
} from "react-native"
import { Text, Icon } from "."
import { colors, spacing } from "@/theme"
import { useStores } from "@/store"

const { height } = Dimensions.get("window")

interface BusinessMenuPopupProps {
  visible: boolean
  onClose: () => void
}

interface MenuItemProps {
  icon: string
  title: string
  subtitle: string
  onPress: () => void
  iconColor?: string
}

const MenuItem: React.FC<MenuItemProps> = ({ icon, title, subtitle, onPress, iconColor }) => (
  <TouchableOpacity style={styles.menuItem} onPress={onPress} activeOpacity={0.7}>
    <View style={styles.menuItemLeft}>
      <View style={styles.iconContainer}>
        <Icon icon={icon as any} size={24} color={iconColor || colors.palette.primary500} />
      </View>
      <View style={styles.menuItemTextContainer}>
        <Text style={styles.menuItemTitle}>{title}</Text>
        <Text style={styles.menuItemSubtitle}>{subtitle}</Text>
      </View>
    </View>
    <Icon icon="caretRight" size={20} color={colors.palette.neutral400} />
  </TouchableOpacity>
)

export const BusinessMenuPopup: React.FC<BusinessMenuPopupProps> = ({ visible, onClose }) => {
  const {
    auth: { logout },
  } = useStores()

  const slideAnim = React.useRef(new Animated.Value(height)).current

  React.useEffect(() => {
    if (visible) {
      Animated.spring(slideAnim, {
        toValue: 0,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }).start()
    } else {
      Animated.spring(slideAnim, {
        toValue: height,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }).start()
    }
  }, [visible, slideAnim])

  const handleWhatsAppHelp = () => {
    onClose()
    const message = `Bonjour, j'ai besoin d'aide avec la création de mon entreprise sur Fedha.`
    const phoneNumber = "+243836803506"
    const url = `https://wa.me/${phoneNumber.replace("+", "")}?text=${encodeURIComponent(message)}`

    Linking.openURL(url)
      .then((supported) => {
        if (!supported) {
          Alert.alert("Erreur", "WhatsApp n'est pas installé sur votre appareil")
        }
      })
      .catch((err) => console.error("Erreur WhatsApp:", err))
  }

  const handleContactUs = () => {
    onClose()
    Alert.alert("Nous contacter", "Choisissez votre méthode de contact préférée:", [
      {
        text: "Email",
        onPress: () => {
          const email = "<EMAIL>"
          const subject = "Aide - Création d'entreprise"
          const body = "Bonjour, j'ai besoin d'aide avec la création de mon entreprise sur Fedha."
          const url = `mailto:${email}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`
          Linking.openURL(url).catch((err) => console.error("Erreur email:", err))
        },
      },
      {
        text: "Téléphone",
        onPress: () => {
          const phoneNumber = "+243836803506"
          const url = `tel:${phoneNumber}`
          Linking.openURL(url).catch((err) => console.error("Erreur téléphone:", err))
        },
      },
      {
        text: "SMS",
        onPress: () => {
          const phoneNumber = "+243836803506"
          const message =
            "Bonjour, j'ai besoin d'aide avec la création de mon entreprise sur Fedha."
          const url = `sms:${phoneNumber}?body=${encodeURIComponent(message)}`
          Linking.openURL(url).catch((err) => console.error("Erreur SMS:", err))
        },
      },
      { text: "Annuler", style: "cancel" },
    ])
  }

  const handleLogout = () => {
    onClose()
    Alert.alert("Déconnexion", "Êtes-vous sûr de vouloir vous déconnecter ?", [
      { text: "Annuler", style: "cancel" },
      {
        text: "Déconnexion",
        onPress: () => logout(),
        style: "destructive",
      },
    ])
  }

  const handleBackdropPress = () => {
    onClose()
  }

  return (
    <Modal visible={visible} transparent animationType="none" onRequestClose={onClose}>
      <TouchableOpacity style={styles.overlay} activeOpacity={1} onPress={handleBackdropPress}>
        <Animated.View
          style={[
            styles.popupContainer,
            {
              transform: [{ translateY: slideAnim }],
            },
          ]}
        >
          <TouchableOpacity activeOpacity={1} onPress={(e) => e.stopPropagation()}>
            {/* Header */}
            <View style={styles.header}>
              <View style={styles.dragIndicator} />
              <Text style={styles.headerTitle}>Options</Text>
              <TouchableOpacity style={styles.closeButton} onPress={onClose}>
                <Icon icon="x" size={24} color={colors.palette.neutral600} />
              </TouchableOpacity>
            </View>

            {/* Menu Items */}
            <View style={styles.menuContainer}>
              <MenuItem
                icon="whatsapp"
                title="Aide WhatsApp"
                subtitle="Obtenez de l'aide via WhatsApp"
                onPress={handleWhatsAppHelp}
                iconColor={colors.palette.primary600}
              />

              <MenuItem
                icon="contactus"
                title="Nous contacter"
                subtitle="Email, téléphone ou SMS"
                onPress={handleContactUs}
                iconColor={colors.palette.primary500}
              />

              <MenuItem
                icon="logout"
                title="Déconnexion"
                subtitle="Se déconnecter de l'application"
                onPress={handleLogout}
                iconColor={colors.palette.angry500}
              />
            </View>
          </TouchableOpacity>
        </Animated.View>
      </TouchableOpacity>
    </Modal>
  )
}

const styles = StyleSheet.create({
  closeButton: {
    padding: spacing.xs,
  },
  dragIndicator: {
    backgroundColor: colors.palette.neutral300,
    borderRadius: 2,
    height: 4,
    left: "50%",
    marginLeft: -20,
    position: "absolute",
    top: spacing.xs,
    width: 40,
  },
  header: {
    alignItems: "center",
    borderBottomColor: colors.palette.neutral200,
    borderBottomWidth: 1,
    flexDirection: "row",
    justifyContent: "space-between",
    paddingBottom: spacing.sm,
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.md,
  },
  headerTitle: {
    color: colors.palette.neutral900,
    flex: 1,
    fontSize: 18,
    fontWeight: "600",
    textAlign: "center",
  },
  iconContainer: {
    alignItems: "center",
    backgroundColor: colors.palette.neutral100,
    borderRadius: 24,
    height: 48,
    justifyContent: "center",
    marginRight: spacing.md,
    width: 48,
  },
  menuContainer: {
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.md,
  },
  menuItem: {
    alignItems: "center",
    backgroundColor: colors.palette.neutral200,
    borderColor: colors.palette.neutral200,
    borderRadius: 12,
    borderWidth: 1,
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: spacing.xs,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.md,
  },
  menuItemLeft: {
    alignItems: "center",
    flexDirection: "row",
    flex: 1,
  },
  menuItemSubtitle: {
    color: colors.palette.neutral600,
    fontSize: 14,
    lineHeight: 18,
  },
  menuItemTextContainer: {
    flex: 1,
  },
  menuItemTitle: {
    color: colors.palette.neutral900,
    fontSize: 16,
    fontWeight: "600",
    marginBottom: spacing.xs,
  },
  overlay: {
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    flex: 1,
    justifyContent: "flex-end",
  },
  popupContainer: {
    backgroundColor: colors.palette.neutral100,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: 34, // Safe area padding
    shadowColor: "#000",
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.25,
    shadowRadius: 10,
    elevation: 10,
  },
})

/* eslint-disable react-native/no-inline-styles */
import {
  ImageStyle,
  StyleProp,
  TextStyle,
  View,
  ViewStyle,
  ImageSourcePropType,
  ImageBackground,
} from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import { colors, spacing, type ThemedStyle } from "@/theme"
import { Button, Text } from "."

export interface ServiceItemProps {
  imageSource: ImageSourcePropType
  title?: string
  event: "active" | "comingSoon" | "inactive" | any
  description: string
  onBuyPress: () => void
  buttonText?: string
  style?: StyleProp<ViewStyle>
  imageStyle?: StyleProp<ImageStyle>
  descriptionStyle?: StyleProp<TextStyle>
  buttonStyle?: StyleProp<ViewStyle>
  buttonTextStyle?: StyleProp<TextStyle>
}

export const ServiceItem = (props: ServiceItemProps) => {
  const {
    imageSource,
    description,
    onBuyPress,
    buttonText = "Acheter maintenant", // Default button text
    style,
    imageStyle,
    descriptionStyle,
    buttonStyle,
    title,
    event,
  } = props

  const { themed } = useAppTheme()

  // Get status info based on event
  const getStatusInfo = () => {
    switch (event) {
      case "active":
        return {
          showButton: true,
          buttonText: buttonText || "Acheter",
          statusText: null,
          buttonStyle: themed($button),
          statusStyle: null,
        }
      case "comingSoon":
        return {
          showButton: false,
          buttonText: null,
          statusText: "Service bientôt disponible",
          buttonStyle: null,
          statusStyle: themed($comingSoonStatus),
        }
      case "inactive":
        return {
          showButton: false,
          buttonText: null,
          statusText: "Service temporairement inactif",
          buttonStyle: null,
          statusStyle: themed($inactiveStatus),
        }
      default:
        return {
          showButton: false,
          buttonText: null,
          statusText: "Service indisponible",
          buttonStyle: null,
          statusStyle: themed($unavailableStatus),
        }
    }
  }

  const statusInfo = getStatusInfo()

  // Combine base styles with optional override styles
  const $containerStyles = [themed($container), style]
  const $imageStyles = [$image, imageStyle]
  const $descriptionStyles = [themed($descriptionText), descriptionStyle]
  const $buttonStyles = [statusInfo.buttonStyle, buttonStyle]

  return (
    <View style={$containerStyles}>
      <View
        style={{
          height: 130,
          width: "100%",
          paddingBottom: spacing.xl,
        }}
      >
        <ImageBackground
          source={imageSource}
          style={$imageStyles}
          // imageStyle={styles.modalHeaderImage}
        />
      </View>
      <View
        style={{
          flex: 1,
          paddingVertical: spacing.xl,
          paddingHorizontal: spacing.md,
        }}
      >
        <Text
          style={{
            fontSize: 18,
            fontWeight: "bold",
            marginBottom: 10,
            color: colors.palette.neutral900,
          }}
        >
          {title}
        </Text>
        <Text style={$descriptionStyles}>{description}</Text>
      </View>

      {/* Conditional rendering based on event status */}
      {statusInfo.showButton ? (
        <Button
          testID="service-button"
          preset="reversed"
          text={statusInfo.buttonText || undefined}
          style={$buttonStyles}
          onPress={onBuyPress}
        />
      ) : (
        <View style={statusInfo.statusStyle}>
          <Text style={themed($statusText)}>{statusInfo.statusText}</Text>
        </View>
      )}
    </View>
  )
}
const $container: ThemedStyle<ViewStyle> = ({ colors }) => ({
  alignItems: "center", // Center items horizontally
  overflow: "hidden",
  borderRadius: 16,

  // marginHorizontal: spacing.md,
  borderWidth: 0.3,
  borderColor: colors.palette.neutral500,
  backgroundColor: colors.palette.neutral100,
  elevation: 3,
  marginBottom: spacing.xl,
  shadowColor: colors.palette.neutral900,
  shadowOffset: { width: 1, height: 2 },
  shadowOpacity: 0.5,
  shadowRadius: 8,
})

const $image: ImageStyle = {
  width: "100%",
  height: 160,
  marginBottom: 15,
  paddingBottom: 10,
  borderTopLeftRadius: 16,
  borderTopRightRadius: 16,
  borderRadius: 4,
}

const $descriptionText: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontFamily: typography.primary.normal,
  fontSize: 14,
  color: colors.textDim, // Example text color
  // textAlign: "center",
  marginBottom: 16,
})

const $button: ThemedStyle<ViewStyle> = ({ colors }) => ({
  backgroundColor: colors.palette.neutral900,
  borderRadius: 16,
  elevation: 3,
  paddingVertical: spacing.md,
  shadowColor: colors.palette.neutral900,
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
  shadowRadius: 4,
  width: "90%",
  bottom: spacing.md,
})

// Status styles for different service states
const $comingSoonStatus: ThemedStyle<ViewStyle> = ({ colors }) => ({
  backgroundColor: colors.palette.primary100,
  borderRadius: 16,
  paddingVertical: spacing.md,
  paddingHorizontal: spacing.sm,
  width: "90%",
  bottom: spacing.md,
  alignItems: "center",
  borderWidth: 1,
  borderColor: colors.palette.primary300,
})

const $inactiveStatus: ThemedStyle<ViewStyle> = ({ colors }) => ({
  backgroundColor: colors.palette.terciary200,
  borderRadius: 16,
  paddingVertical: spacing.md,
  paddingHorizontal: spacing.sm,
  width: "90%",
  bottom: spacing.md,
  alignItems: "center",
  // borderWidth: 1,
  // borderColor: colors.palette.neutral400,
})

const $unavailableStatus: ThemedStyle<ViewStyle> = ({ colors }) => ({
  backgroundColor: colors.palette.terciary200,
  borderRadius: 16,
  paddingVertical: spacing.md,
  paddingHorizontal: spacing.sm,
  width: "90%",
  bottom: spacing.md,
  alignItems: "center",
  // borderWidth: 1,
  // borderColor: colors.palette.angry100,
})

const $statusText: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontFamily: typography.primary.medium,
  fontSize: 14,
  fontWeight: "600",
  color: colors.text,
  textAlign: "center",
})

/* eslint-disable react-native/sort-styles */
/* eslint-disable react-native/no-inline-styles */
/* eslint-disable no-restricted-imports */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable react-native/no-color-literals */
import React, {
  forwardRef,
  useState,
  useCallback,
  useImperativeHandle,
  useRef,
  useEffect,
} from "react"
import {
  View,
  StyleSheet,
  Pressable,
  TextInput as RNTextInput,
  StyleProp,
  ViewStyle,
  TextStyle,
  TextInputProps,
} from "react-native"
import { colors } from "@/theme"
import { Text } from "."

export interface OtpEntryInputProps {
  maxLength: number
  autoFocus?: boolean
  onPinChange?: (pin: string) => void
  containerStyle?: StyleProp<ViewStyle>
  otpContainerStyle?: StyleProp<ViewStyle>
  otpBoxStyle?: StyleProp<ViewStyle>
  otpTextStyle?: StyleProp<TextStyle>
  otpBorderColor?: string
  otpBorderFocusedColor?: string
  textInputProps?: TextInputProps
}

export interface OtpEntryInputRef extends RNTextInput {
  clear: () => void
}

export const OtpEntryInput = forwardRef<OtpEntryInputRef, OtpEntryInputProps>(
  function OtpEntryInput(
    {
      maxLength,
      onPinChange,
      autoFocus = true,
      containerStyle,
      otpContainerStyle,
      otpBoxStyle,
      otpTextStyle,
      otpBorderColor = "#F6F6F6",
      otpBorderFocusedColor = colors.palette.primary600,
      textInputProps,
    },
    ref,
  ) {
    const [isInputBoxFocused, setIsInputBoxFocused] = useState<boolean>(autoFocus)
    const [otp, setOtp] = useState<string>("")
    const inputRef = useRef<RNTextInput>(null)

    useImperativeHandle(ref, () => {
      const instance = inputRef.current!
      return {
        ...instance,
        clear: () => {
          setOtp("")
          instance.clear()
        },
      }
    })

    const handlePinChange = useCallback(
      (pin: string) => {
        setOtp(pin)
        onPinChange?.(pin)
      },
      [onPinChange],
    )

    const handleOnPress = () => {
      setIsInputBoxFocused(true)
      // Ensure the input gets focus and keyboard shows up
      inputRef.current?.focus()
    }

    const handleOnBlur = () => {
      setIsInputBoxFocused(false)
    }

    // Add this effect to handle focus
    useEffect(() => {
      if (autoFocus) {
        inputRef.current?.focus()
      }
    }, [autoFocus])

    return (
      <View style={[styles.container, containerStyle]}>
        <RNTextInput
          style={[
            styles.textInput,
            // Make the hidden input take up the full width of the container
            { width: "100%", height: "100%", position: "absolute" },
          ]}
          value={otp}
          inputMode="numeric"
          onChangeText={handlePinChange}
          maxLength={maxLength}
          ref={inputRef}
          onBlur={handleOnBlur}
          onFocus={() => setIsInputBoxFocused(true)}
          keyboardType="numeric"
          autoFocus={autoFocus}
          {...textInputProps}
        />
        <Pressable
          style={[styles.otpContainer, otpContainerStyle]}
          onPress={handleOnPress}
          // Make the Pressable take up the full size of its container
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          {Array.from({ length: maxLength }).map((_, index) => {
            const isFocused =
              isInputBoxFocused &&
              (index === otp.length || (index === maxLength - 1 && otp.length === maxLength))
            return (
              <View
                key={index}
                style={[
                  styles.otpBox,
                  otpBoxStyle,
                  { borderColor: isFocused ? otpBorderFocusedColor : otpBorderColor },
                ]}
              >
                <Text style={[styles.otpText, otpTextStyle]}>{otp[index] || ""}</Text>
              </View>
            )
          })}
        </Pressable>
      </View>
    )
  },
)

const styles = StyleSheet.create({
  container: {
    alignItems: "center",
    justifyContent: "center",
    position: "relative", // Add this
    minHeight: 60, // Add minimum height
  },
  otpBox: {
    backgroundColor: colors.palette.neutral300,
    borderRadius: 10,
    borderWidth: 2,
    height: 50,
    justifyContent: "center",
    padding: 12,
    width: 50,
  },
  otpContainer: {
    flexDirection: "row",
    justifyContent: "space-evenly",
    width: "100%",
    zIndex: 1, // Add this
  },
  otpText: {
    color: "black",
    fontSize: 20,
    fontWeight: "bold",
    textAlign: "center",
  },
  textInput: {
    opacity: 0,
    position: "absolute",
    zIndex: 2, // Add this
  },
})

import { FC } from "react"
import {
  StyleProp,
  TextStyle,
  TouchableOpacity,
  View,
  ViewStyle,
  Image,
  ImageSourcePropType,
  ImageStyle,
} from "react-native"
// import { useAppTheme } from "@/utils/useAppTheme" // Removed unused
import { colors, spacing, typography } from "@/theme"
import { Text, Button, Icon } from "."

// Service status types
export type ServiceStatus = "active" | "comingSoon" | "inactive"

// Single layout props (from BuyScreen)
export interface SingleLayoutProps {
  title: string
  description: string
  image: ImageSourcePropType
  event: ServiceStatus
  onPress: () => void
}

// Multi layout props (current implementation)
export interface MultiLayoutProps {
  title: string
  description: string
  items: Array<{
    id: string
    icon: string
    title: string
    buttonText: string
    data?: any
  }>
  onItemPress: (item: any) => void
}

// Main component props
export interface ServiceCardProps {
  layout?: "single" | "multi"
  style?: StyleProp<ViewStyle>
  // Single layout props
  title?: string
  description?: string
  image?: ImageSourcePropType
  event?: ServiceStatus
  onPress?: () => void
  // Multi layout props
  items?: Array<{
    id: string
    icon: string
    title: string
    buttonText: string
    data?: any
  }>
  onItemPress?: (item: any) => void
}

/**
 * Reusable ServiceCard component with Single and Multi layouts
 *
 * Single Layout: Beautiful service cards with images, status handling, and action buttons
 * Multi Layout: List-style cards with multiple items and individual actions
 *
 * @param layout - "single" (default) or "multi"
 * @param title - Service title
 * @param description - Service description
 * @param image - Service image (single layout only)
 * @param event - Service status: "active", "comingSoon", "inactive" (single layout only)
 * @param onPress - Action handler (single layout only)
 * @param items - Array of service items (multi layout only)
 * @param onItemPress - Item action handler (multi layout only)
 */
export const ServiceCard: FC<ServiceCardProps> = ({
  layout = "single",
  style,
  title = "",
  description = "",
  image,
  event = "active",
  onPress,
  items = [],
  onItemPress,
}) => {
  // const { themed } = useAppTheme() // Removed unused

  // Single Layout Component
  const SingleLayout: FC<SingleLayoutProps> = ({ title, description, image, event, onPress }) => {
    const getStatusInfo = () => {
      switch (event) {
        case "active":
          return {
            showButton: true,
            buttonText: "Acheter maintenant",
            statusText: null,
            buttonColor: colors.palette.primary600,
            statusColor: null,
          }
        case "comingSoon":
          return {
            showButton: false,
            buttonText: null,
            statusText: "Bientôt disponible",
            buttonColor: null,
            statusColor: colors.palette.neutral800,
          }
        case "inactive":
          return {
            showButton: false,
            buttonText: null,
            statusText: "Temporairement indisponible",
            buttonColor: null,
            statusColor: colors.palette.neutral800,
          }
        default:
          return {
            showButton: false,
            buttonText: null,
            statusText: "Service indisponible",
            buttonColor: null,
            statusColor: colors.palette.secondary300,
          }
      }
    }

    const statusInfo = getStatusInfo()

    return (
      <View style={$singleCard}>
        <View style={$singleImageContainer}>
          {image && <Image source={image} style={$singleImage} />}
          {/* {event !== "active" && (
            <View style={$singleOverlay}>
              <Text style={$overlayText}>{statusInfo.statusText}</Text>
            </View>
          )} */}
        </View>

        <View style={$singleContent}>
          <Text style={$singleTitle}>{title}</Text>
          <Text style={$singleDescription}>{description}</Text>

          {statusInfo.showButton ? (
            <Button
              text={statusInfo.buttonText || ""}
              style={[
                $singleButton,
                statusInfo.buttonColor ? { backgroundColor: statusInfo.buttonColor } : {},
              ]}
              textStyle={$singleButtonText}
              onPress={onPress}
            />
          ) : (
            <View
              style={[
                $singleStatus,
                statusInfo.statusColor ? { backgroundColor: `${statusInfo.statusColor}20` } : {},
              ]}
            >
              <Text
                style={[
                  $singleStatusText,
                  statusInfo.statusColor ? { color: statusInfo.statusColor } : {},
                ]}
              >
                {statusInfo.statusText}
              </Text>
            </View>
          )}
        </View>
      </View>
    )
  }

  // Multi Layout Component
  const MultiLayout: FC<MultiLayoutProps> = ({ title, description, items, onItemPress }) => {
    return (
      <View style={$multiCard}>
        <View style={$multiHeader}>
          <Text style={$multiTitle}>{title}</Text>
          <Text style={$multiDescription}>{description}</Text>
        </View>

        {items.map((item, index) => (
          <View key={item.id || index} style={$multiItemContainer}>
            <View style={$multiLeftSection}>
              <View style={$multiIconContainer}>
                <Icon icon={item.icon as any} size={24} color={colors.palette.primary500} />
              </View>
              <Text style={$multiItemTitle}>{item.title}</Text>
            </View>
            <TouchableOpacity
              style={$multiActionButton}
              onPress={() => onItemPress && onItemPress(item)}
            >
              {/* <Text style={$multiButtonText}>{item.buttonText}</Text> */}
              <Text style={$multiButtonText}>Achetert</Text>
            </TouchableOpacity>
          </View>
        ))}
      </View>
    )
  }

  // Render appropriate layout
  const renderLayout = () => {
    if (layout === "multi") {
      return (
        <MultiLayout
          title={title}
          description={description}
          items={items}
          onItemPress={onItemPress || (() => {})}
        />
      )
    } else {
      return (
        <SingleLayout
          title={title}
          description={description}
          image={image || require("../../assets/images/banners/Snel.png")}
          event={event}
          onPress={onPress || (() => {})}
        />
      )
    }
  }

  return <View style={style}>{renderLayout()}</View>
}

// =============================================================================
// STYLES
// =============================================================================

// Single Layout Styles
const $singleCard: ViewStyle = {
  backgroundColor: colors.palette.neutralBg,
  borderRadius: 16,
  overflow: "hidden",
  elevation: 3,
  shadowColor: colors.palette.neutral900,
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.08,
  shadowRadius: 8,
  marginBottom: spacing.md,
}

const $singleImageContainer: ViewStyle = {
  position: "relative",
  height: 160,
}

const $singleImage: ImageStyle = {
  width: "100%",
  height: "100%",
  resizeMode: "cover",
}

// const $singleOverlay: ViewStyle = {
//   position: "absolute",
//   top: 0,
//   left: 0,
//   right: 0,
//   bottom: 0,
//   backgroundColor: "rgba(0, 0, 0, 0.6)",
//   justifyContent: "center",
//   alignItems: "center",
// }

// const $overlayText: TextStyle = {
//   fontFamily: typography.primary.medium,
//   fontSize: 16,
//   fontWeight: "600",
//   color: colors.palette.neutral100,
//   textAlign: "center",
// }

const $singleContent: ViewStyle = {
  padding: spacing.md,
}

const $singleTitle: TextStyle = {
  fontFamily: typography.primary.bold,
  fontSize: 18,
  fontWeight: "700",
  color: colors.palette.neutral900,
  marginBottom: spacing.xs,
}

const $singleDescription: TextStyle = {
  fontFamily: typography.primary.normal,
  fontSize: 14,
  color: colors.palette.neutral600,
  lineHeight: 20,
  marginBottom: spacing.md,
}

const $singleButton: ViewStyle = {
  backgroundColor: colors.palette.primary600,
  borderRadius: 12,
  paddingVertical: spacing.sm,
  paddingHorizontal: spacing.md,
  alignItems: "center",
}

const $singleButtonText: TextStyle = {
  fontFamily: typography.primary.medium,
  fontSize: 14,
  fontWeight: "600",
  color: colors.palette.neutral100,
}

const $singleStatus: ViewStyle = {
  borderRadius: 12,
  paddingVertical: spacing.sm,
  paddingHorizontal: spacing.md,
  alignItems: "center",
  borderWidth: 1,
  borderColor: colors.palette.neutral300,
}

const $singleStatusText: TextStyle = {
  fontFamily: typography.primary.medium,
  fontSize: 14,
  fontWeight: "600",
}

// Multi Layout Styles
const $multiCard: ViewStyle = {
  backgroundColor: colors.palette.neutral100,
  // borderRadius: 16,
  padding: spacing.xs,
  marginBottom: spacing.md,
  // elevation: 2,
  // shadowColor: colors.palette.neutral900,
  // shadowOffset: { width: 0, height: 1 },
  // shadowOpacity: 0.05,
  // shadowRadius: 4,
}

const $multiHeader: ViewStyle = {
  marginBottom: spacing.md,
}

const $multiTitle: TextStyle = {
  fontFamily: typography.primary.bold,
  fontSize: 20,
  fontWeight: "700",
  color: colors.palette.neutral900,
  marginBottom: spacing.xs,
}

const $multiDescription: TextStyle = {
  fontFamily: typography.primary.normal,
  fontSize: 14,
  color: colors.palette.neutral600,
  lineHeight: 20,
}

const $multiItemContainer: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
  paddingVertical: spacing.sm,
  paddingHorizontal: spacing.sm,
  backgroundColor: colors.palette.neutral300,
  borderRadius: 12,
  marginBottom: spacing.xs,
}

const $multiLeftSection: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  flex: 1,
}

const $multiIconContainer: ViewStyle = {
  width: 40,
  height: 40,
  borderRadius: 20,
  backgroundColor: colors.palette.neutral100,
  justifyContent: "center",
  alignItems: "center",
  marginRight: spacing.sm,
  borderWidth: 1,
  borderColor: colors.palette.primary200,
}

const $multiItemTitle: TextStyle = {
  fontFamily: typography.primary.medium,
  fontSize: 16,
  fontWeight: "500",
  color: colors.palette.neutral900,
  flex: 1,
}

const $multiActionButton: ViewStyle = {
  backgroundColor: colors.palette.neutral900,
  paddingHorizontal: spacing.sm,
  paddingVertical: spacing.xs,
  borderRadius: 20,
  minWidth: 80,
  alignItems: "center",
}

const $multiButtonText: TextStyle = {
  fontFamily: typography.primary.medium,
  fontSize: 14,
  fontWeight: "600",
  color: colors.palette.neutral100,
}

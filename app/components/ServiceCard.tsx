import { StyleProp, TextStyle, TouchableOpacity, View, ViewStyle } from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"
import { Text } from "."

export interface ServiceCardProps {
  title: string
  description: string
  items: any
  onItemPress: any
  layout: "single" | "multi"
  style?: StyleProp<ViewStyle>
}

/**
 * Describe your component here
 */

export const ServiceCard = (props: ServiceCardProps) => {
  const { style, items } = props
  const $styles = [$container, style]
  const { themed } = useAppTheme()

  return (
    <View style={$styles}>
      <View style={styles.cardContainer}>
        <View style={styles.header}>
          <Text style={styles.title}>{title}</Text>
          <Text style={styles.description}>{description}</Text>
        </View>
        {items && (
          <View style={styles.itemContainer}>
            <View style={styles.leftSection}>
              <View style={styles.iconContainer}>
                <Icon name={item.icon} size={24} color="#4FC3F7" />
              </View>
              <Text style={styles.itemTitle}>{item.title}</Text>
            </View>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => onItemPress && onItemPress(item)}
            >
              <Text style={styles.buttonText}>{item.buttonText}</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>
    </View>
  )
}

const $container: ViewStyle = {
  justifyContent: "center",
}

const $text: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontFamily: typography.primary.normal,
  fontSize: 14,
  color: colors.palette.primary500,
})

cardContainer: {
  backgroundColor: '#2a2a2a',
  borderRadius: 12,
  padding: 16,
  marginVertical: 8,
},
header: {
  marginBottom: 16,
},
title: {
  fontSize: 24,
  fontWeight: 'bold',
  color: '#ffffff',
  marginBottom: 8,
},
description: {
  fontSize: 16,
  color: '#b0b0b0',
  lineHeight: 22,
},
itemContainer: {
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'space-between',
  paddingVertical: 16,
  paddingHorizontal: 12,
  backgroundColor: '#3a3a3a',
  borderRadius: 8,
},
const leftSection: {
  flexDirection: 'row',
  alignItems: 'center',
  flex: 1,
},
const iconContainer: {
  width: 48,
  height: 48,
  borderRadius: 24,
  backgroundColor: '#2a2a2a',
  justifyContent: 'center',
  alignItems: 'center',
  marginRight: 16,
  borderWidth: 2,
  borderColor: '#4FC3F7',
},
const itemTitle: {
  fontSize: 18,
  fontWeight: '500',
  color: '#ffffff',
  flex: 1,
},
const actionButton: {
  backgroundColor: '#FF7043',
  paddingHorizontal: 20,
  paddingVertical: 10,
  borderRadius: 20,
  minWidth: 80,
  alignItems: 'center',
},
const buttonText: {
  color: '#ffffff',
  fontSize: 16,
  fontWeight: '600',
},
const separator: {
  height: 8,
},
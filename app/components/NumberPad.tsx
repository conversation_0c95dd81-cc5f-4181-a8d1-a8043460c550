/* eslint-disable no-restricted-imports */
import React from "react"
import { StyleProp, TextStyle, View, ViewStyle, TouchableOpacity, Animated } from "react-native"
import { colors, typography } from "@/theme"
import { Text, Icon } from "."

export interface NumberPadProps {
  onPress: (num: string) => void
  style?: StyleProp<ViewStyle>
  disabled?: boolean
}

const numbers = [
  ["1", "2", "3"],
  ["4", "5", "6"],
  ["7", "8", "9"],
  [".", "0", "⌫"],
]

export const NumberPad = ({ onPress, style, disabled }: NumberPadProps) => {
  const $styles = [$container, style]

  // Animation setup for button press
  const createButtonAnimation = () => new Animated.Value(1)
  const buttonAnimations = React.useMemo(
    () => numbers.flat().map(() => createButtonAnimation()),
    [],
  )

  const handlePress = (num: string, index: number) => {
    if (disabled) return

    // Animate button press
    Animated.sequence([
      Animated.timing(buttonAnimations[index], {
        toValue: 0.9,
        duration: 50,
        useNativeDriver: true,
      }),
      Animated.timing(buttonAnimations[index], {
        toValue: 1,
        duration: 50,
        useNativeDriver: true,
      }),
    ]).start()

    onPress(num)
  }

  const renderButton = (num: string, index: number) => {
    const isDelete = num === "⌫"
    const animation = buttonAnimations[index]
    const isZeroButton = num === "0"

    return (
      <Animated.View
        key={num}
        style={[
          $buttonContainer,
          isZeroButton && $zeroButtonContainer,
          { transform: [{ scale: animation }] },
        ]}
      >
        <TouchableOpacity
          style={[$button, disabled && $buttonDisabled]}
          onPress={() => handlePress(num, index)}
          disabled={disabled}
        >
          {isDelete ? (
            <Icon icon="caretLeft" size={24} color={colors.palette.neutral800} />
          ) : (
            <Text style={[$text, disabled && $textDisabled]}>{num}</Text>
          )}
        </TouchableOpacity>
      </Animated.View>
    )
  }

  return (
    <View style={$styles}>
      {numbers.map((row, rowIndex) => (
        <View key={`row-${rowIndex}`} style={$row}>
          {row.map((num, colIndex) => renderButton(num, rowIndex * 3 + colIndex))}
        </View>
      ))}
    </View>
  )
}

const $container: ViewStyle = {
  padding: 20,
  flex: 1,
}

const $row: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-around",
  marginBottom: 20,
}

const $buttonContainer: ViewStyle = {
  flex: 1,
  margin: 5,
}

const $zeroButtonContainer: ViewStyle = {
  flex: 2, // Make zero button wider
}

const $button: ViewStyle = {
  height: 69,
  justifyContent: "center",
  alignItems: "center",
  backgroundColor: colors.palette.neutral200,
  borderRadius: 12,
}

const $buttonDisabled: ViewStyle = {
  opacity: 0.5,
}

const $text: TextStyle = {
  fontFamily: typography.primary.medium,
  fontSize: 22,
  color: colors.palette.neutral600,
}

const $textDisabled: TextStyle = {
  color: colors.palette.neutral600,
}

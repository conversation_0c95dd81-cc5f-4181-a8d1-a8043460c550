/* eslint-disable react-native/no-color-literals */
/* eslint-disable react-native/no-inline-styles */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable import/no-unresolved */
import {
  ActivityIndicator,
  Alert,
  Dimensions,
  Linking,
  Modal,
  StyleProp,
  Animated,
  View,
  ViewStyle,
  TouchableOpacity,
  TextStyle,
  ImageStyle,
} from "react-native"
import { useCameraDevice, Camera, useCodeScanner } from "react-native-vision-camera"
import { Text, Icon, Button } from "../.."
import { useEffect, useRef, useState } from "react"
import { Svg, Defs, Rect, Mask } from "react-native-svg"
import { colors, spacing } from "@/theme"

export interface ScannerProps {
  visible: boolean
  flash: boolean
  onClose: () => void
  onscan: (data: any) => void
  style?: StyleProp<ViewStyle>
}

const SCREEN_HEIGHT = Dimensions.get("window").height
const SCREEN_WIDTH = Dimensions.get("window").width
const SCAN_AREA_SIZE = 280

export const Scanner = (props: ScannerProps) => {
  const { style, onscan, visible, onClose, flash } = props
  const [hasPermission, setHasPermission] = useState<boolean>(false)
  const [isScanning, setIsScanning] = useState(false)
  const [scanError, setScanError] = useState<string | null>(null)
  const [isQRInFrame, setIsQRInFrame] = useState(false) // Track if QR is in frame
  const device = useCameraDevice("back")
  const cameraRef = useRef<Camera>(null)
  const slideAnim = useRef(new Animated.Value(0)).current
  const frameAnimValue = useRef(new Animated.Value(0)).current // Animation for frame

  const showErrorMessage = () => {
    Animated.timing(slideAnim, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start()
  }

  const hideErrorMessage = () => {
    Animated.timing(slideAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start()
  }

  const requestCameraPermission = async () => {
    const permission = await Camera.requestCameraPermission()
    if (permission === "granted") {
      setHasPermission(true)
    } else {
      Alert.alert(
        "L'autorisation de la caméra est refusée",
        "Veuillez autoriser l'accès à la caméra dans les paramètres de votre appareil.",
        [{ text: "Ouvrir les paramètres", onPress: () => Linking.openSettings() }],
      )
    }
  }

  // // Calculate the scan area boundaries
  // const scanAreaBounds = {
  //   x: (SCREEN_WIDTH - SCAN_AREA_SIZE) / 2,
  //   y: (SCREEN_HEIGHT - SCAN_AREA_SIZE) / 4,
  //   width: SCAN_AREA_SIZE,
  //   height: SCAN_AREA_SIZE,
  // }

  // // Animate the frame when QR is detected in frame
  // const animateFrame = (inFrame: boolean) => {
  //   // Only trigger animation if the state is changing
  //   if (inFrame !== isQRInFrame) {
  //     setIsQRInFrame(inFrame)

  //     // Animate the frame to indicate QR code is in position
  //     Animated.timing(frameAnimValue, {
  //       toValue: inFrame ? 1 : 0,
  //       duration: 300,
  //       useNativeDriver: true,
  //     }).start()
  //   }
  // }

  const handleScan = async (codes: any) => {
    // Only process if we have codes and we're not already processing a scan
    if (codes.length > 0 && !isScanning) {
      const code = codes[0]
      const qrCodeData = code?.value

      // console.log("QR Code Data:", qrCodeData)

      if (qrCodeData) {
        // Set scanning state to true to prevent multiple scans
        setIsScanning(true)

        setTimeout(() => {
          try {
            // Try to parse the QR code data as JSON if it starts with { or [
            let parsedData = qrCodeData
            if (
              typeof qrCodeData === "string" &&
              (qrCodeData.trim().startsWith("{") || qrCodeData.trim().startsWith("["))
            ) {
              try {
                parsedData = JSON.parse(qrCodeData)
                console.log("Successfully parsed QR code data as JSON:", parsedData)
              } catch (parseError) {
                console.log("Failed to parse QR code data as JSON, using raw data")
              }
            }

            // Call the onscan callback with the parsed data
            onscan(parsedData)
          } catch (error: any) {
            setScanError(error.message || "Échec de la lecture du code QR. Veuillez réessayer.")
            showErrorMessage()
            // If there's an error, allow scanning again
            setIsScanning(false)
          }
        }, 500)
      }
    } else {
      // animateFrame(false)
    }
  }

  useEffect(() => {
    if (visible) {
      requestCameraPermission()
    }
  }, [visible])

  const codeScanner = useCodeScanner({
    codeTypes: ["qr"],
    onCodeScanned: handleScan,
  })

  const renderLoadingState = () => (
    <View style={$loadingContainer}>
      <ActivityIndicator size="large" color={colors.palette.primary500} />
      <Text style={$loadingText}>
        {!hasPermission
          ? "Demande d'autorisation de la Camera..."
          : "Chargement du dispositif de la caméra..."}
      </Text>
    </View>
  )

  const renderCameraFrame = () => (
    <View style={$overlayContainer}>
      <Svg height="100%" width="100%">
        <Defs>
          <Mask id="mask" x="0" y="0" height="100%" width="100%">
            <Rect width="100%" height="100%" fill="white" />
            <Rect
              x={(SCREEN_WIDTH - SCAN_AREA_SIZE) / 2}
              y={(SCREEN_HEIGHT - SCAN_AREA_SIZE) / 4}
              width={SCAN_AREA_SIZE}
              height={SCAN_AREA_SIZE}
              fill="black"
            />
          </Mask>
        </Defs>
        <Rect width="100%" height="100%" fill="rgba(0, 0, 0, 0.8)" mask="url(#mask)" />

        {/* Static frame with fixed color */}
        <Rect
          x={(SCREEN_WIDTH - SCAN_AREA_SIZE) / 2}
          y={(SCREEN_HEIGHT - SCAN_AREA_SIZE) / 4}
          width={SCAN_AREA_SIZE}
          height={SCAN_AREA_SIZE}
          fill="rgba(0, 0, 0, 0.1)"
          strokeWidth="5"
          stroke={isQRInFrame ? colors.palette.primary500 : "white"}
        />
      </Svg>

      <View style={$scanTextContainer}>
        <Text style={[$scanText, isQRInFrame && $scanTextSuccess]}>
          {isQRInFrame
            ? "QR code détecté! Maintien en position..."
            : "Placez le QR code dans le cadre"}
        </Text>
      </View>
    </View>
  )

  const renderErrorMessage = () => {
    const getErrorDetails = () => {
      switch (scanError?.toLowerCase()) {
        case "code qr invalide":
          return {
            title: "Code QR non reconnu",
            message:
              "Le code QR scanné n'est pas un code Fedha valide. Veuillez vérifier et réessayer.",
            icon: "faildqrcode",
          }
        case "échec de la lecture du code qr. veuillez réessayer.":
          return {
            title: "Erreur de lecture",
            message:
              "La lecture du code QR a échoué. Assurez-vous que le code est bien visible et stable.",
            icon: "refresharrow",
          }
        case "network request failed":
          return {
            title: "Erreur de connexion",
            message: "Impossible de vérifier le code QR. Vérifiez votre connexion internet.",
            icon: "faildwifi",
          }
        default:
          return {
            title: "Erreur",
            message: "Une erreur inattendue s'est produite. Veuillez réessayer.",
            icon: "warning",
          }
      }
    }

    const errorDetails = getErrorDetails()

    return (
      <>
        <TouchableOpacity style={$errorOverlay} activeOpacity={1} />
        <Animated.View
          style={[
            $errorContainer,
            {
              transform: [
                {
                  translateY: slideAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [200, 0],
                  }),
                },
              ],
            },
          ]}
        >
          <Icon
            icon={errorDetails.icon as any}
            size={32}
            color={colors.palette.angry500}
            style={$errorIcon}
          />
          <Text style={$errorTitle}>{errorDetails.title}</Text>
          <Text style={$errorMessage}>{errorDetails.message}</Text>
          <View style={$errorButtons}>
            <Button
              text="Réessayer"
              onPress={() => {
                hideErrorMessage()
                setScanError(null)
              }}
              style={$retryButton}
              textStyle={$retryButtonText}
            />
            <Button
              text="Fermer"
              // preset="secondary"
              onPress={onClose}
              style={$closeErrorButton}
              textStyle={$closeErrorButtonText}
            />
          </View>
        </Animated.View>
      </>
    )
  }

  const renderScanningOverlay = () => (
    <View style={$scanningOverlay}>
      <ActivityIndicator size="large" color={colors.palette.primary500} />
      <Text style={$scanningText}>Traitement du code QR...</Text>
    </View>
  )

  return (
    <View style={$container}>
      {!hasPermission || !device ? (
        renderLoadingState()
      ) : (
        <View style={$cameraContainer}>
          <Camera
            ref={cameraRef}
            style={$camera}
            device={device}
            codeScanner={codeScanner}
            isActive={visible && !isScanning && !scanError}
            enableZoomGesture
            torch={flash ? "on" : "off"}
            photo={false}
            video={false}
            audio={false}
          />
          {/* {console.log("ScannE", scanError)} */}
          {renderCameraFrame()}
          {isScanning && renderScanningOverlay()}
          {scanError && renderErrorMessage()}
        </View>
      )}
    </View>
  )
}

const $container: ViewStyle = {
  flex: 1,
}

const $scanTextContainer: ViewStyle = {
  position: "absolute",
  bottom: SCREEN_HEIGHT * 0.2,
  left: 0,
  right: 0,
  alignItems: "center",
}

const $scanText: TextStyle = {
  color: colors.palette.neutral100,
  fontSize: 16,
  marginBottom: spacing.sm,
}

const $scanTextSuccess: TextStyle = {
  color: colors.palette.primary500,
  fontWeight: "bold",
}

const $cameraContainer: ViewStyle = {
  flex: 1,
}

const $camera: ViewStyle = {
  flex: 1,
}

const $overlayContainer: ViewStyle = {
  position: "absolute",
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
}

const $loadingContainer: ViewStyle = {
  flex: 1,
  justifyContent: "center",
  alignItems: "center",
}

const $loadingText: TextStyle = {
  color: colors.palette.neutral100,
  marginTop: 16,
  textAlign: "center",
}

const $errorButtons: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-around",
  width: "100%",
}

const $retryButton: ViewStyle = {
  backgroundColor: colors.palette.primary500,
  paddingHorizontal: 20,
  borderRadius: 15,
  marginRight: 10,
}

const $errorOverlay: ViewStyle = {
  position: "absolute",
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  backgroundColor: "rgba(0, 0, 0, 0.5)",
}

const $errorContainer: ViewStyle = {
  position: "absolute",
  bottom: 0,
  left: 0,
  right: 0,
  backgroundColor: colors.palette.neutral100,
  padding: spacing.xl,
  paddingBottom: spacing.xxl,
  borderTopLeftRadius: 20,
  borderTopRightRadius: 20,
  alignItems: "center",
  elevation: 5,
  shadowColor: "#000",
  shadowOffset: {
    width: 0,
    height: -5,
  },
  shadowOpacity: 0.25,
  shadowRadius: 3.84,
}

const $scanningOverlay: ViewStyle = {
  backgroundColor: "rgba(0, 0, 0, 0.7)",
  justifyContent: "center",
  alignItems: "center",
}

const $scanningText: TextStyle = {
  color: colors.palette.neutral100,
  marginTop: 20,
  fontSize: 16,
}

const $errorIcon: ImageStyle = {
  marginBottom: spacing.xs,
}

const $errorTitle: TextStyle = {
  fontSize: 18,
  fontWeight: "bold",
  color: colors.palette.angry500,
  marginBottom: spacing.xs,
}

const $errorMessage: TextStyle = {
  color: colors.text,
  textAlign: "center",
  marginBottom: spacing.lg,
  fontSize: 16,
}

const $closeErrorButton: ViewStyle = {
  backgroundColor: colors.palette.neutral300,
  paddingHorizontal: 20,
  borderRadius: 15,
  marginLeft: 10,
}

const $closeErrorButtonText: TextStyle = {
  color: colors.palette.neutral800,
}

const $retryButtonText: TextStyle = {
  color: colors.palette.neutral100,
}

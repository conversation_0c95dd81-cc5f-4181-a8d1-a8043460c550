import { StyleProp, View, ViewStyle } from "react-native"
import { FencyTextInput } from "."
import { useNavigation, useRoute, useIsFocused } from "@react-navigation/native"
import { useEffect } from "react"

export interface FedhaPochiInputProps {
  onChange: (value: any) => void
  value: string
  style?: StyleProp<ViewStyle>
}

// Type definitions are handled by useNavigation<any> and useRoute<any>

export const FedhaPochiInput = (props: FedhaPochiInputProps) => {
  const { style, onChange, value } = props
  const $styles = [$container, style]
  const navigation = useNavigation<any>()
  const route = useRoute<any>()
  const isFocused = useIsFocused()

  // Handle data from ScannerScreen when returning
  useEffect(() => {
    if (isFocused && route.params?.scannedData) {
      const scannedData = route.params.scannedData

      // Extract wallet_id based on data type
      if (scannedData) {
        if (typeof scannedData === "string") {
          onChange(scannedData)
        } else if (scannedData.wallet_id) {
          onChange(scannedData.wallet_id)
        }
      }

      // Clear the params to prevent reprocessing
      navigation.setParams({ scannedData: undefined, dataType: undefined })
    }
  }, [isFocused, route.params, navigation, onChange])

  // Navigate to ScannerScreen
  // const handleScan = () => {
  //   navigation.navigate("Scanner", {
  //     hideTabBar: true,
  //     returnScreen: route.name,
  //     user_type: "standard",
  //   })
  // }

  return (
    <View style={$styles}>
      <FencyTextInput
        value={value}
        onChange={onChange}
        maxLength={9}
        keyboardType="numeric"
        inputname="FedhaPochi ID"
        placeholder="Entrez l'ID FedhaPochi"
        // LeftIcon="scan"
        // leftonPress={handleScan}
      />
    </View>
  )
}

const $container: ViewStyle = {
  // justifyContent: "center"
}

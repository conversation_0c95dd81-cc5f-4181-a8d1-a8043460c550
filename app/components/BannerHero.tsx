/* eslint-disable react-native/no-inline-styles */
/* eslint-disable import/no-unresolved */
import { Dimensions, StyleProp, TextStyle, View, ViewStyle, ImageBackground } from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import { colors, type ThemedStyle } from "@/theme"
import { Text } from "."
import Svg, { Path } from "react-native-svg"

export interface BannerHeroProps {
  backgroundImage?: any
  textColor?: string
  style?: StyleProp<ViewStyle>
  title?: string
  subtitle?: string
}

const { height: SCREEN_HEIGHT } = Dimensions.get("window")
const BANNER_HEIGHT = SCREEN_HEIGHT * 0.3

export const CurvyBottom = () => {
  return (
    <Svg
      style={{ position: "absolute", bottom: -5 }}
      width="100%"
      height={50}
      viewBox="0 0 100 25"
      preserveAspectRatio="none"
    >
      <Path d="M0 30 L0 15 Q50 30 100 15 L100 30 Z" fill={colors.palette.neutral100} />
    </Svg>
  )
}

export const BannerHero = (props: BannerHeroProps) => {
  const {
    style,
    backgroundImage = require("../../assets/images/banners/DefaultBackground.png"),
    textColor,
    title,
    subtitle,
  } = props
  const $styles = [$container, style]
  const { themed } = useAppTheme()

  return (
    <View style={$styles}>
      <ImageBackground source={backgroundImage} style={$backgroundImage} resizeMode="cover">
        <View style={$contentContainer}>
          <Text style={[themed($headingText), { color: textColor }]} preset="heading">
            {title}
          </Text>
          <Text style={[themed($subheadingText), { color: textColor }]}> {subtitle} </Text>
        </View>
        <CurvyBottom />
      </ImageBackground>
    </View>
  )
}

const $container: ViewStyle = {
  height: BANNER_HEIGHT,
  width: "100%",
  overflow: "hidden",
}

const $backgroundImage: ViewStyle = {
  flex: 1,
  width: "100%",
 
}

const $contentContainer: ViewStyle = {
  flex: 1,
  justifyContent: "flex-start",
  alignItems: "flex-start",
  paddingHorizontal: 24,
  paddingTop: 40,
  
}

const $headingText: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontSize: 54,
  top: 10,
  fontFamily: typography.primary.normal,
  lineHeight: 57,
  color: colors.palette.neutral800,
  marginBottom: 25,
})

const $subheadingText: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontFamily: typography.primary.normal,
  fontSize: 24,
  color: colors.palette.neutral800,
  lineHeight: 30,
  // opacity: 0.9,
})

/* eslint-disable react-native/no-inline-styles */
/* eslint-disable import/no-unresolved */
import { StyleProp, TextStyle, View, ViewStyle } from "react-native"
import { colors, spacing } from "@/theme"
import { But<PERSON>, Text } from "."
// import React from "react"
// Remove the Skeleton import and create a simple animated loading component
// const SkeletonLoader = ({ width, height, style }: any) => {
//   const animatedValue = new Animated.Value(0)

//   React.useEffect(() => {
//     Animated.loop(
//       Animated.sequence([
//         Animated.timing(animatedValue, {
//           toValue: 1,
//           duration: 1000,
//           useNativeDriver: true,
//         }),
//         Animated.timing(animatedValue, {
//           toValue: 0,
//           duration: 1000,
//           useNativeDriver: true,
//         }),
//       ]),
//     ).start()
//   }, [])

//   return (
//     <Animated.View
//       style={[
//         {
//           width,
//           height,
//           backgroundColor: colors.palette.neutral300, // Change this to any color from your palette
//           opacity: animatedValue.interpolate({
//             inputRange: [0, 1],
//             outputRange: [0.3, 0.7], // You can adjust these values to control the animation intensity
//           }),
//           borderRadius: 4,
//         },
//         style,
//       ]}
//     />
//   )
// }

export interface SneLeRecuProps {
  style?: StyleProp<ViewStyle>
  meterNumber?: string
  moredata?: any
  amount?: any
  units?: any
  totalValue?: any
  currency?: any
  metername?: string
  CreditNumber?: any
  date?: string
  status?: "pending" | "completed" | "failed"
}

export const SneLeRecu = (props: SneLeRecuProps) => {
  const {
    style,
    meterNumber = "N/A",
    amount = 0,
    moredata,
    metername = "Unkown Meter Owner",
    totalValue = 0,
    status = "pending",
    date = new Date().toLocaleDateString(),
  } = props

  const $styles = [$container, style]
  const isPending = status === "pending"
  const isCompleted = status === "completed"

  // Extract the currency code or use a fallback
  const currencyCode = moredata?.requests?.[0]?.currency

  const token = isCompleted && moredata?.requests?.[0]?.token
  const totalUnits = isCompleted ? moredata?.requests?.[0]?.total_unit : null
  const ismoredtails = isCompleted && moredata?.requests?.[0]?.more_details

  const displayMeterName =
    metername === "Unkown Meter Owner" ? "Searching meter name..." : metername

  // console.log("moredata", moredata)
  return (
    <View style={$styles}>
      <View style={$header}>
        <Text style={$title}>SNEL RDC</Text>
        <Text style={$date}>{date}</Text>
      </View>

      <View style={$meterSection}>
        <Text style={$label}>Mon Compteur</Text>
        <Text style={$meterNumber}>{meterNumber}</Text>
        <Text style={[$label, isPending && $searchingText]}>{displayMeterName}</Text>
        <Text style={$date}>{ismoredtails.location_id}</Text>
      </View>

      <View style={$divider} />

      {/* Token Section - Only show if completed */}
      {isCompleted && token && (
        <>
          <View style={$meterSection}>
            <Text style={$label}>Token</Text>
            <Text style={$tokenNumber}>{token}</Text>
          </View>
          <View style={$divider} />
        </>
      )}

      {/* <View style={$meterSection}>
        <Text style={$label}>Crédit d&lsquo;électricité</Text>
        {isPending ? (
          <View style={$loadingContainer}>
            <View style={$skeletonContainer}>
              <SkeletonLoader height={20} width={150} style={{ marginTop: spacing.sm }} />
            </View>
            <Text style={$loadingText}>En cours de traitement...</Text>
          </View>
        ) : (
          <Text style={$CreditNumber}>{totalUnits || units} kWh</Text>
        )}
      </View> */}

      <View style={$divider} />

      <View style={$detailsContainer}>
        <View style={$detailRow}>
          <Text style={$label}>Montant encaissé</Text>
          <Text style={$value}>
            {amount} {currencyCode}
          </Text>
        </View>

        {isCompleted && (
          <>
            <View style={$detailRow}>
              <Text style={$label}>Total Unités</Text>
              <Text style={$value}>{totalUnits} kWh</Text>
            </View>

            {/* {moreDetails &&
              Object.entries(moreDetails).map(([key, value]) => (
                <View key={key} style={$detailRow}>
                  <Text style={$label}>{key}</Text>
                  <Text style={$value}>{value}</Text>
                </View>
              ))} */}

            <View style={$divider} />

            <View style={$detailRow}>
              <Text style={[$label, $totalLabel]}>Valeur Total</Text>
              <Text style={[$value, $totalValue]}>
                {totalValue} {currencyCode}
              </Text>
            </View>
          </>
        )}
      </View>

      {/* Show cancel button for both pending and non-completed states */}
      {!isCompleted && (
        <Button
          preset="default"
          text="Annuler la demande"
          style={$cancelButton}
          onPress={() => console.log("Cancel request")}
        />
      )}

      {/* Show share button only for completed requests */}
      {/* {isCompleted && (
        <Button
          preset="default"
          text="Partager ce reçu"
          style={[$cancelButton, $shareButton]}
          onPress={() => console.log("Share request")}
        />
      )} */}
    </View>
  )
}

const $container: ViewStyle = {
  backgroundColor: colors.palette.neutral100,
  borderRadius: 12,
  padding: spacing.md,
  marginVertical: spacing.sm,
  marginHorizontal: spacing.sm,
  borderWidth: 1,
  borderColor: colors.palette.neutral300,
}

const $header: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
  marginBottom: spacing.md,
}

const $title: TextStyle = {
  fontSize: 17,
  fontWeight: "bold",
  color: colors.palette.neutral900,
}

const $date: TextStyle = {
  fontSize: 14,
  color: colors.palette.neutral600,
}

const $meterSection: ViewStyle = {
  alignItems: "center",
  marginBottom: spacing.md,
}

const $meterNumber: TextStyle = {
  fontSize: 24,
  fontWeight: "bold",
  color: colors.palette.neutral900,
  marginTop: spacing.xs,
}

// const $CreditNumber: TextStyle = {
//   fontSize: 20,
//   fontWeight: "bold",
//   color: colors.palette.primary500,
//   marginTop: spacing.xs,
// }

const $divider: ViewStyle = {
  height: 1,
  backgroundColor: colors.palette.neutral300,
  marginVertical: spacing.sm,
}

const $detailsContainer: ViewStyle = {
  marginTop: spacing.sm,
}

const $detailRow: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
  marginVertical: spacing.xs,
}

const $label: TextStyle = {
  fontSize: 16,
  color: colors.palette.neutral800,
}

const $value: TextStyle = {
  fontSize: 16,
  fontWeight: "500",
  color: colors.palette.neutral900,
}

const $totalLabel: TextStyle = {
  fontSize: 18,
  fontWeight: "bold",
}

const $totalValue: TextStyle = {
  fontSize: 18,
  fontWeight: "bold",
  color: colors.palette.neutral900,
}

// const $loadingContainer: ViewStyle = {
//   alignItems: "center",
//   padding: spacing.md,
// }

// const $loadingText: TextStyle = {
//   marginTop: spacing.sm,
//   fontSize: 16,
//   color: colors.palette.terciary100,
// }

// const $skeletonContainer: ViewStyle = {
//   alignItems: "center",
//   marginBottom: spacing.md,
// }

const $cancelButton: ViewStyle = {
  marginTop: spacing.md,
  // backgroundColor: colors.palette.neutral900,
  paddingHorizontal: spacing.md,
  paddingVertical: spacing.xs,
  borderRadius: 15,
}

const $tokenNumber: TextStyle = {
  fontSize: 20,
  fontWeight: "bold",
  color: colors.palette.primary500,
  marginTop: spacing.xs,
}

// const $shareButton: ViewStyle = {
//   backgroundColor: colors.palette.primary500,
//   marginTop: spacing.xs,
// }

const $searchingText: TextStyle = {
  color: colors.palette.neutral600,
  fontStyle: "italic",
}

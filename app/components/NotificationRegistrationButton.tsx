/* eslint-disable no-restricted-imports */
/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useState } from "react"
import { ActivityIndicator, Alert, View, StyleSheet } from "react-native"
import { Button } from "./atoms/Button"
import { registerAndVerifyPushToken } from "../utils/notificationHelpers"
import { colors } from "@/theme"
import { Text } from "./atoms/Text"

export function NotificationRegistrationButton() {
  const [isRegistering, setIsRegistering] = useState(false)
  const [registrationStatus, setRegistrationStatus] = useState<"none" | "success" | "failed">(
    "none",
  )

  const handleRegistration = async () => {
    try {
      setIsRegistering(true)
      setRegistrationStatus("none")

      const result = await registerAndVerifyPushToken()

      console.log("✅ Notification Registration Success:", result)
      setRegistrationStatus("success")

      Alert.alert(
        "Succès",
        "Notifications activées avec succès! Vous recevrez désormais des notifications importantes.",
        [{ text: "OK" }],
      )
    } catch (error) {
      console.error("❌ Notification Registration Error:", error)
      setRegistrationStatus("failed")

      Alert.alert(
        "Erreur",
        "Impossible d'activer les notifications. Veuillez vérifier vos paramètres.",
        [{ text: "OK" }],
      )
    } finally {
      setIsRegistering(false)
    }
  }

  return (
    <View style={styles.container}>
      <Button
        preset="reversed"
        text={isRegistering ? "Activation..." : "Activer les notifications"}
        style={[styles.button, isRegistering && styles.disabledButton]}
        disabled={isRegistering}
        onPress={handleRegistration}
      >
        {isRegistering && <ActivityIndicator size="small" color="white" />}
      </Button>

      {registrationStatus === "success" && (
        <Text size="sm" style={styles.successText}>
          ✅ Notifications activées
        </Text>
      )}

      {registrationStatus === "failed" && (
        <Text size="sm" style={styles.errorText}>
          ❌ Échec de l&lsquo;activation
        </Text>
      )}
    </View>
  )
}

const styles = StyleSheet.create({
  button: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "center",
    minWidth: 200,
  },
  container: {
    alignItems: "center",
    padding: 16,
  },
  disabledButton: {
    opacity: 0.7,
  },
  errorText: {
    color: colors.palette.angry500,
    marginTop: 8,
  },
  successText: {
    color: colors.palette.accent400,
    marginTop: 8,
  },
})

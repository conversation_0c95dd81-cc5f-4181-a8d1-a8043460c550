import { StyleProp, TextStyle, TouchableOpacity, ViewStyle } from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import { colors, type ThemedStyle } from "@/theme"
import { Icon, Text } from "."
import { useCartStore } from "@/store/CartStore"

export interface CartWidgetProps {
  style?: StyleProp<ViewStyle>
  navigation: any
}

export const CartWidget = (props: CartWidgetProps) => {
  const { style, navigation } = props
  const $styles = [$container, style]
  const { themed } = useAppTheme()

  // Get the cart item count from the cart store
  const itemCount = useCartStore((state) => state.getItemCount())

  // Only show the count if there are items in the cart
  const showCount = itemCount > 0

  return (
    <TouchableOpacity
      style={[$styles, showCount && $containerWithCount]}
      onPress={() => navigation.navigate("MyCart")}
    >
      {showCount && (
        <Text style={[themed($text), showCount && { color: colors.palette.neutral100 }]}>
          {itemCount}
        </Text>
      )}
      <Icon
        icon={"shoppingBag"}
        size={25}
        color={showCount ? colors.palette.neutral100 : colors.palette.neutral900}
      />
    </TouchableOpacity>
  )
}

const $container: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  paddingHorizontal: 12,
  justifyContent: "center",
  alignContent: "center",
  paddingVertical: 10,
}
const $containerWithCount: ViewStyle = {
  backgroundColor: colors.palette.neutral900,
  borderRadius: 10,
}

const $text: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontFamily: typography.primary.normal,
  fontSize: 18,
  paddingHorizontal: 10,
  color: colors.palette.primary600,
})

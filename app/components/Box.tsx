/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable import/no-unresolved */
import { StyleProp, TextStyle, View, ViewStyle } from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import { $Gstyles, spacing, type ThemedStyle } from "@/theme"
import { Text } from "."
import { TxKeyPath } from "@/i18n"
import { ReactNode } from "react"

interface BoxProps {
  name?: TxKeyPath | any
  description?: TxKeyPath
  layout?: "column" | "row"
  itemStyle?: ViewStyle
  header?: ReactNode
  children: ReactNode
  footer?: ReactNode
  footerStyle?: ViewStyle
  onPress?: any
  headerStyle?: ViewStyle
  style?: StyleProp<ViewStyle>
}

/**
 * @param {DemoUseCaseProps} props - The props for the `DemoUseCase` component.
 * @returns {JSX.Element} The rendered `DemoUseCase` component.
 */

export const Box = (props: BoxProps) => {
  const {
    name,
    description,
    children,
    style,
    header,
    footer,
    headerStyle,
    onPress,
    footerStyle,
    layout = "column",
    itemStyle = {},
  } = props
  const { themed } = useAppTheme()
  const $contStyles = [$container, style]
  const $headerStyle = [$hderstyle, headerStyle]
  const $footerStyle = [$footrstyle, footerStyle]

  return (
    <View style={$contStyles}>
      {/* <Text style={themed($name)}>{translate(name)}</Text> */}
      {header && <View style={$headerStyle}>{header}</View>}
      {description && <Text style={themed($description)}>{description}</Text>}

      <View style={[itemStyle, layout === "row" && $Gstyles.row, themed($item)]}>{children}</View>
      {footer && <View style={$footerStyle}>{footer}</View>}
    </View>
  )
}

const $container: ViewStyle = {
  justifyContent: "center",
  // backgroundColor: "#fff",
  // padding: spacing.sm,
  top: spacing.lg,
  borderRadius: spacing.sm,
  // shadowColor: "#000",
  // shadowOffset: { width: 0, height: 2 },
  // shadowOpacity: 0.1,
  // shadowRadius: 1,
  // elevation: 1,
}

const $description: ThemedStyle<TextStyle> = ({ spacing }) => ({
  marginTop: spacing.md,
})

const $item: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  // backgroundColor: colors.palette.neutral100,
  borderRadius: 8,
  // paddingHorizontal: spacing.md,
  marginVertical: spacing.md,
})

const $hderstyle: ViewStyle = {
  paddingHorizontal: spacing.xs,
  alignContent: "center",
  // marginVertical: spacing.md,
}

const $footrstyle: ViewStyle = {
  paddingHorizontal: spacing.md,
  alignContent: "center",
  marginVertical: spacing.xs,
}

const $name: ThemedStyle<TextStyle> = ({ typography }) => ({
  fontFamily: typography.primary.bold,
})

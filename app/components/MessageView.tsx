import { StyleProp, View, ViewStyle, FlatList, StyleSheet } from "react-native"
import { colors, spacing } from "@/theme"
import { Text, Icon } from "."
import { format } from "date-fns"
import { fr } from "date-fns/locale"

// Notification type from API
interface Notification {
  business: string | null
  created_at: string
  has_been_assisted: boolean
  id: number
  is_read: boolean
  message: string
  notification_type: string
  user: string
}

export interface MessageViewProps {
  /**
   * An optional style override useful for padding & margin.
   */
  style?: StyleProp<ViewStyle>
  /**
   * List of notifications to display
   */
  notifications: Notification[]
  /**
   * Type of notifications being displayed
   */
  notificationType: string
}

/**
 * Component to display a list of notification messages in a chat-like interface
 */
export const MessageView = (props: MessageViewProps) => {
  const { style, notifications, notificationType } = props
  const $styles = [$container, style]

  // Format date for display
  const formatMessageDate = (dateString: string) => {
    try {
      const date = new Date(dateString)
      return format(date, "dd MMM yyyy, HH:mm", { locale: fr })
    } catch {
      return dateString
    }
  }

  // Get icon for notification type
  const getNotificationTypeIcon = (type: string): string => {
    const typeIcons: Record<string, string> = {
      transaction_notif: "monytrans",
      service_request: "utilities",
      system_notification: "info",
      account_notification: "user",
      security_alert: "lock",
    }
    return typeIcons[type] || "message"
  }

  // Render a single notification message
  const renderNotification = ({ item }: { item: Notification }) => {
    return (
      <View
        style={[styles.messageContainer, item.is_read ? styles.readMessage : styles.unreadMessage]}
      >
        <View style={styles.messageHeader}>
          <Icon
            icon={getNotificationTypeIcon(notificationType) as any}
            size={20}
            color={colors.palette.primary500}
            containerStyle={styles.messageIcon}
          />
          <Text style={styles.messageDate}>{formatMessageDate(item.created_at)}</Text>
          {!item.is_read && <View style={styles.unreadIndicator} />}
        </View>
        <Text style={styles.messageText}>{item.message}</Text>
        {item.has_been_assisted && (
          <View style={styles.assistedContainer}>
            <Icon
              icon="Rcheck"
              size={16}
              color={colors.palette.neutral700}
              containerStyle={styles.assistedIcon}
            />
            <Text style={styles.assistedText}>Traité</Text>
          </View>
        )}
      </View>
    )
  }

  return (
    <View style={$styles}>
      {notifications.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>Aucun message</Text>
        </View>
      ) : (
        <FlatList
          data={notifications}
          renderItem={renderNotification}
          keyExtractor={(item) => item.id.toString()}
          contentContainerStyle={styles.listContent}
          showsVerticalScrollIndicator={false}
        />
      )}
    </View>
  )
}

const $container: ViewStyle = {
  flex: 1,
  padding: spacing.sm,
}

const styles = StyleSheet.create({
  assistedContainer: {
    alignItems: "center",
    flexDirection: "row",
    marginTop: spacing.xs,
  },
  assistedIcon: {
    marginRight: spacing.xxs,
  },
  assistedText: {
    color: colors.palette.neutral700,
    fontSize: 12,
  },
  emptyContainer: {
    alignItems: "center",
    flex: 1,
    justifyContent: "center",
    padding: spacing.xl,
  },
  emptyText: {
    color: colors.palette.neutral600,
    textAlign: "center",
  },
  listContent: {
    paddingBottom: spacing.lg,
  },
  messageContainer: {
    backgroundColor: colors.palette.neutral200,
    borderRadius: 12,
    elevation: 2,
    marginVertical: spacing.xs,
    padding: spacing.md,
    shadowColor: colors.palette.neutral900,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  messageDate: {
    color: colors.palette.neutral600,
    flex: 1,
    fontSize: 12,
  },
  messageHeader: {
    alignItems: "center",
    flexDirection: "row",
    marginBottom: spacing.xs,
  },
  messageIcon: {
    marginRight: spacing.xs,
  },
  messageText: {
    color: colors.palette.neutral800,
    fontSize: 14,
    lineHeight: 20,
  },
  readMessage: {
    opacity: 0.8,
  },
  unreadIndicator: {
    backgroundColor: colors.palette.primary500,
    borderRadius: 4,
    height: 8,
    width: 8,
  },
  unreadMessage: {
    backgroundColor: colors.palette.neutral100,
    borderLeftColor: colors.palette.primary500,
    borderLeftWidth: 3,
  },
})

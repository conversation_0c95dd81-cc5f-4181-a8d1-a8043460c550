/* eslint-disable react-native/no-color-literals */
/* eslint-disable react-native/no-inline-styles */
/* eslint-disable no-restricted-imports */
/* eslint-disable @typescript-eslint/no-unused-vars */
import React from "react"
import {
  StyleProp,
  TextStyle,
  View,
  ViewStyle,
  TouchableOpacity,
  Modal,
  ScrollView,
  StatusBar,
  SafeAreaView,
} from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import { colors, spacing, typography } from "@/theme"
import { Text, Icon } from "."

export interface ConfirmationWidgetProps {
  visible: boolean
  isSuccess: boolean
  apiResponse?: any
  errorMessage?: string
  title?: string
  subtitle?: string
  buttonText?: string

  onButtonPress: () => void

  transactionType?: string
  showConfetti?: boolean
  style?: StyleProp<ViewStyle>
}

export const ConfirmationWidget = (props: ConfirmationWidgetProps) => {
  const {
    visible,
    isSuccess,
    apiResponse,
    errorMessage,
    title,
    subtitle,
    buttonText = "OK",
    onButtonPress,
    transactionType = "Transaction",
    showConfetti = true,
    style,
  } = props

  // Generate title and subtitle based on success/failure
  const getDisplayTitle = () => {
    if (title) return title
    return isSuccess ? "Transaction Réussie!" : "Transaction Échouée"
  }

  const getDisplaySubtitle = () => {
    if (subtitle) return subtitle
    if (isSuccess) {
      return `Votre ${transactionType.toLowerCase()} a été effectuée avec succès.`
    } else {
      return errorMessage || "Une erreur s'est produite lors de la transaction."
    }
  }

  // Extract transaction details from API response
  const getTransactionDetails = () => {
    if (!isSuccess || !apiResponse) return []

    const details = []

    // Common transaction details
    if (apiResponse.amount) {
      details.push({
        label: "Montant",
        value: `${apiResponse.amount} ${apiResponse.currency || "CDF"}`,
      })
    }

    if (apiResponse.transaction_id || apiResponse.id) {
      details.push({
        label: "ID Transaction",
        value: apiResponse.transaction_id || apiResponse.id,
      })
    }

    if (apiResponse.reference) {
      details.push({
        label: "Référence",
        value: apiResponse.reference,
      })
    }

    if (apiResponse.status) {
      details.push({
        label: "Statut",
        value: apiResponse.status,
        isStatus: true,
      })
    }

    if (apiResponse.created_at || apiResponse.date) {
      const date = new Date(apiResponse.created_at || apiResponse.date)
      details.push({
        label: "Date",
        value: date.toLocaleString("fr-FR"),
      })
    }

    // Service-specific details
    if (apiResponse.meter_number) {
      details.push({
        label: "Numéro Compteur",
        value: apiResponse.meter_number,
      })
    }

    if (apiResponse.phone_number) {
      details.push({
        label: "Numéro Téléphone",
        value: apiResponse.phone_number,
      })
    }

    if (apiResponse.token) {
      details.push({
        label: "Token",
        value: apiResponse.token,
      })
    }

    if (apiResponse.units) {
      details.push({
        label: "Unités",
        value: `${apiResponse.units} kWh`,
      })
    }

    return details
  }

  const renderConfetti = () => {
    if (!showConfetti || !isSuccess) return null

    return (
      <View style={$confettiContainer}>
        {/* Animated confetti pieces */}
        <View
          style={[
            $confettiPiece,
            { top: 120, left: 100, backgroundColor: colors.palette.primary500 },
          ]}
        />
        <View
          style={[
            $confettiPiece,
            { top: 140, left: 150, backgroundColor: colors.palette.secondary500 },
          ]}
        />
        <View
          style={[
            $confettiPiece,
            { top: 110, right: 100, backgroundColor: colors.palette.terciary100 },
          ]}
        />
        <View
          style={[
            $confettiPiece,
            { top: 160, right: 120, backgroundColor: colors.palette.primary300 },
          ]}
        />
        <View
          style={[
            $confettiPiece,
            { top: 180, left: 80, backgroundColor: colors.palette.secondary300 },
          ]}
        />
        <View
          style={[
            $confettiPiece,
            { top: 200, right: 80, backgroundColor: colors.palette.terciary200 },
          ]}
        />
      </View>
    )
  }

  const renderIcon = () => {
    const iconColor = isSuccess ? colors.palette.primary500 : colors.palette.angry500
    const iconName = isSuccess ? "check" : "x"

    return (
      <View style={[$iconContainer, { borderColor: iconColor }]}>
        <View style={[$iconInner, { backgroundColor: iconColor }]}>
          <Icon icon={iconName} size={32} color={colors.palette.neutral100} />
        </View>
      </View>
    )
  }

  const renderDetails = () => {
    const details = getTransactionDetails()

    if (details.length === 0) return null

    return (
      <View style={$detailsContainer}>
        <Text style={$sectionTitle}>Détails de la Transaction</Text>
        {details.map((detail, index) => (
          <View key={index} style={$detailRow}>
            <Text style={$detailLabel}>{detail.label}</Text>
            <Text
              style={[
                $detailValue,
                detail.isStatus && {
                  color: isSuccess ? colors.palette.primary500 : colors.palette.angry500,
                  fontWeight: "600",
                },
              ]}
            >
              {detail.isStatus && isSuccess && "✓ "}
              {detail.value}
            </Text>
          </View>
        ))}
      </View>
    )
  }

  // Debug logging
  // console.log("🎭 ConfirmationWidget render:", {
  //   visible,
  //   isSuccess,
  //   hasApiResponse: !!apiResponse,
  //   errorMessage,
  //   transactionType,
  // })

  if (!visible) {
    return null
  }

  console.log("🎭 ConfirmationWidget rendering with visible:", visible)

  return (
    <Modal visible={visible} animationType="fade" transparent={true} statusBarTranslucent={false}>
      <TouchableOpacity
        style={$modalOverlay}
        activeOpacity={1}
        onPress={() => console.log("🔘 Modal background tapped")}
      >
        <StatusBar barStyle="light-content" backgroundColor={colors.palette.neutral900} />
        <SafeAreaView style={$container}>
          <ScrollView contentContainerStyle={$scrollContainer} showsVerticalScrollIndicator={false}>
            {renderConfetti()}

            <View style={$contentContainer}>
              {renderIcon()}

              <Text style={$titleText}>{getDisplayTitle()}</Text>
              <Text style={$subtitleText}>{getDisplaySubtitle()}</Text>

              {renderDetails()}

              <TouchableOpacity
                style={[
                  $actionButton,
                  {
                    backgroundColor: isSuccess
                      ? colors.palette.primary500
                      : colors.palette.primary600,
                  },
                ]}
                activeOpacity={0.7}
                onPress={onButtonPress}
                // onPressIn={() => console.log("🔘 Button press started")}
                // onPressOut={() => console.log("🔘 Button press ended")}
              >
                <Text style={$buttonText}>{buttonText}</Text>
              </TouchableOpacity>
            </View>
          </ScrollView>
        </SafeAreaView>
      </TouchableOpacity>
    </Modal>
  )
}

// =============================================================================
// STYLES
// =============================================================================

const $modalOverlay: ViewStyle = {
  flex: 1,
  backgroundColor: "rgba(0, 0, 0, 0.95)",
  justifyContent: "center",
  alignItems: "center",
}

const $container: ViewStyle = {
  flex: 1,
  backgroundColor: colors.palette.neutral900,
}

const $scrollContainer: ViewStyle = {
  flexGrow: 1,
  justifyContent: "center",
  alignItems: "center",
  paddingHorizontal: spacing.lg,
  paddingVertical: spacing.xl,
}

const $contentContainer: ViewStyle = {
  alignItems: "center",
  width: "100%",
  maxWidth: 400,
}

const $confettiContainer: ViewStyle = {
  position: "absolute",
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  zIndex: 1,
}

const $confettiPiece: ViewStyle = {
  position: "absolute",
  width: 8,
  height: 8,
  borderRadius: 4,
}

const $iconContainer: ViewStyle = {
  width: 100,
  height: 100,
  borderRadius: 50,
  borderWidth: 3,
  justifyContent: "center",
  alignItems: "center",
  marginBottom: spacing.lg,
}

const $iconInner: ViewStyle = {
  width: 80,
  height: 80,
  borderRadius: 40,
  justifyContent: "center",
  alignItems: "center",
}

const $titleText: TextStyle = {
  fontFamily: typography.primary.bold,
  fontSize: 26,
  color: colors.palette.neutral100,
  textAlign: "center",
  marginBottom: spacing.sm,
  fontWeight: "700",
}

const $subtitleText: TextStyle = {
  fontFamily: typography.primary.normal,
  fontSize: 16,
  color: colors.palette.neutral400,
  textAlign: "center",
  lineHeight: 22,
  marginBottom: spacing.xl,
}

const $sectionTitle: TextStyle = {
  fontFamily: typography.primary.medium,
  fontSize: 18,
  color: colors.palette.neutral100,
  marginBottom: spacing.md,
  fontWeight: "600",
}

const $detailsContainer: ViewStyle = {
  width: "100%",
  marginBottom: spacing.xl,
  backgroundColor: colors.palette.neutral800,
  borderRadius: 12,
  padding: spacing.md,
}

const $detailRow: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
  paddingVertical: spacing.xs,
  borderBottomWidth: 1,
  borderBottomColor: colors.palette.neutral700,
}

const $detailLabel: TextStyle = {
  fontFamily: typography.primary.normal,
  fontSize: 14,
  color: colors.palette.neutral400,
  flex: 1,
}

const $detailValue: TextStyle = {
  fontFamily: typography.primary.medium,
  fontSize: 14,
  color: colors.palette.neutral100,
  textAlign: "right",
  flex: 1,
}

const $apiResponseContainer: ViewStyle = {
  width: "100%",
  marginBottom: spacing.xl,
  backgroundColor: colors.palette.neutral800,
  borderRadius: 12,
  padding: spacing.md,
  maxHeight: 200,
}

const $apiScrollView: ViewStyle = {
  maxHeight: 150,
}

const $apiResponseText: TextStyle = {
  fontFamily: typography.code?.normal || typography.primary.normal,
  fontSize: 12,
  color: colors.palette.neutral300,
  lineHeight: 16,
}

const $actionButton: ViewStyle = {
  width: "100%",
  paddingVertical: spacing.md,
  borderRadius: 25,
  alignItems: "center",
  marginTop: spacing.lg,
  minHeight: 50, // Ensure minimum touch target
  justifyContent: "center",
}

const $buttonText: TextStyle = {
  color: colors.palette.neutral100,
  fontSize: 18,
  fontWeight: "600",
  fontFamily: typography.primary.medium,
}

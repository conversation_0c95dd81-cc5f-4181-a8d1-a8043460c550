/* eslint-disable react-native/no-color-literals */
/* eslint-disable react-native/no-inline-styles */
/* eslint-disable no-restricted-imports */
/* eslint-disable @typescript-eslint/no-unused-vars */
import React from "react"
import { StyleProp, TextStyle, View, ViewStyle, TouchableOpacity, Animated } from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"
import { Text } from "."

export interface ConfirmationWidgetProps {
  /**
   * An optional style override useful for padding & margin.
   */
  style?: StyleProp<ViewStyle>

  /**
   * Whether the action was successful or failed
   */
  isSuccess: boolean

  /**
   * Main title text (e.g., "Payment Successful!" or "Payment Failed!")
   */
  title: string

  /**
   * Subtitle/description text
   */
  subtitle: string

  /**
   * Transaction details array
   */
  details: Array<{
    label: string
    value: string
    isStatus?: boolean
  }>

  /**
   * Button text for the action button
   */
  buttonText: string

  /**
   * Button press handler
   */
  onButtonPress: () => void

  /**
   * Show confetti animation (optional)
   */
  showConfetti?: boolean
}

/**
 * Confirmation widget for success/failure states
 */
export const ConfirmationWidget = (props: ConfirmationWidgetProps) => {
  const {
    style,
    isSuccess,
    title,
    subtitle,
    details,
    buttonText,
    onButtonPress,
    showConfetti = true,
  } = props
  const $styles = [$container, style]
  const { themed } = useAppTheme()

  const renderConfetti = () => {
    if (!showConfetti || !isSuccess) return null

    return (
      <View style={$confettiContainer}>
        {/* Simplified confetti elements */}
        <View style={[$confettiPiece, { top: 120, left: 100, backgroundColor: "#4CAF50" }]} />
        <View style={[$confettiPiece, { top: 140, left: 150, backgroundColor: "#2196F3" }]} />
        <View style={[$confettiPiece, { top: 110, right: 100, backgroundColor: "#FF9800" }]} />
        <View style={[$confettiPiece, { top: 160, right: 120, backgroundColor: "#9C27B0" }]} />
        <View style={[$confettiPiece, { top: 180, left: 80, backgroundColor: "#4CAF50" }]} />
        <View style={[$confettiPiece, { top: 200, right: 80, backgroundColor: "#2196F3" }]} />
      </View>
    )
  }

  const renderIcon = () => {
    return (
      <View style={[$iconContainer, { borderColor: isSuccess ? "#4CAF50" : "#F44336" }]}>
        <View style={[$iconInner, { backgroundColor: isSuccess ? "#4CAF50" : "#F44336" }]}>
          <Text style={$iconText}>{isSuccess ? "✓" : "✕"}</Text>
        </View>
      </View>
    )
  }

  const renderDetails = () => {
    return (
      <View style={$detailsContainer}>
        <Text style={themed($sectionTitle)}>Payment methods</Text>
        {details.map((detail, index) => (
          <View key={index} style={$detailRow}>
            <Text style={themed($detailLabel)}>{detail.label}</Text>
            <Text
              style={[
                themed($detailValue),
                detail.isStatus && {
                  color: isSuccess ? "#4CAF50" : "#F44336",
                  fontWeight: "600",
                },
              ]}
            >
              {detail.isStatus && isSuccess && "✓ "}
              {detail.value}
            </Text>
          </View>
        ))}
      </View>
    )
  }

  return (
    <View style={$styles}>
      {renderConfetti()}

      <View style={$contentContainer}>
        {renderIcon()}

        <Text style={themed($titleText)}>{title}</Text>
        <Text style={themed($subtitleText)}>{subtitle}</Text>

        {renderDetails()}

        <TouchableOpacity
          style={[$actionButton, { backgroundColor: isSuccess ? "#8BC34A" : "#4CAF50" }]}
          onPress={onButtonPress}
        >
          <Text style={$buttonText}>{buttonText}</Text>
        </TouchableOpacity>
      </View>
    </View>
  )
}

const $container: ViewStyle = {
  flex: 1,
  backgroundColor: "#000000",
  justifyContent: "center",
  alignItems: "center",
  paddingHorizontal: 20,
}

const $contentContainer: ViewStyle = {
  alignItems: "center",
  width: "100%",
  maxWidth: 400,
}

const $confettiContainer: ViewStyle = {
  position: "absolute",
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  zIndex: 1,
}

const $confettiPiece: ViewStyle = {
  position: "absolute",
  width: 8,
  height: 8,
  borderRadius: 4,
}

const $iconContainer: ViewStyle = {
  width: 80,
  height: 80,
  borderRadius: 40,
  borderWidth: 2,
  justifyContent: "center",
  alignItems: "center",
  marginBottom: 24,
}

const $iconInner: ViewStyle = {
  width: 60,
  height: 60,
  borderRadius: 30,
  justifyContent: "center",
  alignItems: "center",
}

const $iconText: TextStyle = {
  color: "white",
  fontSize: 24,
  fontWeight: "bold",
}

const $titleText: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontFamily: typography.primary.bold,
  fontSize: 24,
  color: "white",
  textAlign: "center",
  marginBottom: 12,
})

const $subtitleText: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontFamily: typography.primary.normal,
  fontSize: 16,
  color: "#888888",
  textAlign: "center",
  lineHeight: 22,
  marginBottom: 40,
})

const $sectionTitle: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontFamily: typography.primary.medium,
  fontSize: 18,
  color: "white",
  marginBottom: 20,
})

const $detailsContainer: ViewStyle = {
  width: "100%",
  marginBottom: 40,
}

const $detailRow: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
  paddingVertical: 12,
}

const $detailLabel: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontFamily: typography.primary.normal,
  fontSize: 16,
  color: "#888888",
})

const $detailValue: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontFamily: typography.primary.medium,
  fontSize: 16,
  color: "white",
})

const $actionButton: ViewStyle = {
  width: "100%",
  paddingVertical: 16,
  borderRadius: 25,
  alignItems: "center",
  marginTop: 20,
}

const $buttonText: TextStyle = {
  color: "#000000",
  fontSize: 18,
  fontWeight: "600",
}

/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable no-restricted-imports */
/* eslint-disable react-native/no-inline-styles */
import React from "react"
import { View, ViewStyle, TextStyle } from "react-native"
import { Text, Icon } from "@/components"
import { colors, spacing, typography } from "@/theme"

export interface VerificationPendingMessageProps {
  style?: ViewStyle
}

/**
 * A beautiful and professional verification pending message component
 */
export const VerificationPendingMessage = (props: VerificationPendingMessageProps) => {
  const { style } = props

  return (
    <View style={[$container, style]}>
      <View style={$iconContainer}>
        <Icon icon="T0clock" size={32} color={colors.palette.primary500} />
      </View>

      <View style={$contentContainer}>
        <Text style={$title}>Vérification en cours</Text>

        <Text style={$message}>Toutes vos informations sont en cours de vérification.</Text>

        <View style={$timelineContainer}>
          <View style={$timelineItem}>
            <View style={$timelineDot} />
            <Text style={$timelineText}>Temps de vérification : 2-3 jours ouvrables</Text>
          </View>

          <View style={$timelineItem}>
            <View style={$timelineDot} />
            <Text style={$timelineText}>Des documents supplémentaires peuvent être demandés</Text>
          </View>

          <View style={$timelineItem}>
            <View style={$timelineDot} />
            <Text style={$timelineText}>
              Nous vous contacterons par message ou email si nécessaire
            </Text>
          </View>
        </View>

        <View style={$footerContainer}>
          <Icon icon="check" size={16} color={colors.palette.primary500} style={$checkIcon} />
          <Text style={$footerText}>Merci pour votre patience</Text>
        </View>
      </View>
    </View>
  )
}

const $container: ViewStyle = {
  backgroundColor: colors.palette.neutral500,
  borderRadius: 16,
  padding: spacing.lg,
  marginVertical: spacing.md,
  borderWidth: 1,
  borderColor: colors.palette.primary100,
  shadowColor: colors.palette.neutral900,
  shadowOffset: {
    width: 0,
    height: 2,
  },
  shadowOpacity: 0.1,
  shadowRadius: 8,
  elevation: 3,
}

const $iconContainer: ViewStyle = {
  alignItems: "center",
  marginBottom: spacing.md,
}

const $contentContainer: ViewStyle = {
  alignItems: "center",
}

const $title: TextStyle = {
  fontFamily: typography.primary.semiBold,
  fontSize: 20,
  color: colors.palette.neutral900,
  marginBottom: spacing.sm,
  textAlign: "center",
}

const $message: TextStyle = {
  fontFamily: typography.primary.normal,
  fontSize: 16,
  color: colors.palette.neutral700,
  textAlign: "center",
  marginBottom: spacing.lg,
  lineHeight: 24,
}

const $timelineContainer: ViewStyle = {
  alignSelf: "stretch",
  marginBottom: spacing.lg,
}

const $timelineItem: ViewStyle = {
  flexDirection: "row",
  alignItems: "flex-start",
  marginBottom: spacing.sm,
}

const $timelineDot: ViewStyle = {
  width: 8,
  height: 8,
  borderRadius: 4,
  backgroundColor: colors.palette.primary500,
  marginRight: spacing.sm,
  marginTop: 6,
}

const $timelineText: TextStyle = {
  fontFamily: typography.primary.normal,
  fontSize: 14,
  color: colors.palette.neutral600,
  flex: 1,
  lineHeight: 20,
}

const $footerContainer: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  backgroundColor: colors.palette.primary500,
  paddingHorizontal: spacing.md,
  paddingVertical: spacing.sm,
  borderRadius: 8,
}

const $checkIcon = {
  marginRight: spacing.xs,
}

const $footerText: TextStyle = {
  fontFamily: typography.primary.medium,
  fontSize: 14,
  color: colors.palette.primary600,
}

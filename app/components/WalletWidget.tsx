/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable react-native/no-inline-styles */
/* eslint-disable import/no-unresolved */
import {
  ImageStyle,
  StyleProp,
  TextStyle,
  TouchableOpacity,
  View,
  ViewStyle,
  ImageBackground,
} from "react-native"
import { LinearGradient } from "expo-linear-gradient"
import { colors, spacing } from "@/theme"
import { Icon, Text } from "."
import { useMemo } from "react"
import { useStores } from "@/store/rootStore"
import { capitalizeAllLetters } from "@/utils/actions"
import { useNavigation } from "@react-navigation/native"

export interface WalletWidgetProps {
  walletname: string
  style?: StyleProp<ViewStyle>
  walletstatus: string
  walletnumber: string
  walletbalance: number
}

/**
 * Describe your component here
 */

export const WalletWidget = (props: WalletWidgetProps) => {
  const navigation = useNavigation()
  const {
    auth: { user },
  } = useStores()
  const { style, walletname, walletnumber, walletbalance } = props
  const $styles = [$container, style]

  const formattedNumber = useMemo(() => {
    return walletnumber.replace(/(\d{4})/g, "$1 ").trim()
  }, [walletnumber])

  const formattedBalance = useMemo(() => {
    return walletbalance.toLocaleString("fr-CD", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })
  }, [walletbalance])

  const capitalizedName = useMemo(() => {
    const firstName = capitalizeAllLetters(user?.first_name || "")
    const lastName = capitalizeAllLetters(user?.last_name || "")
    return `${firstName} ${lastName}`.trim()
  }, [user?.first_name, user?.last_name])

  return (
    <TouchableOpacity style={$styles}>
      <ImageBackground
        source={require("../../assets/images/backgr.png")}
        style={$backgroundImage}
        resizeMode="cover"
      >
        <LinearGradient
          colors={["rgba(46, 106, 107, 0.9)", "rgba(13, 81, 82, 0.9)"]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={$card}
        >
          <View style={$cardHeader}>
            <Text style={$cardType}>FedhaPochi</Text>
            <Icon
              icon={"fedhapochi"}
              color={colors.palette.neutral200}
              size={30}
              containerStyle={{ padding: spacing.xxs }}
            />
          </View>

          <View style={$cardNumber}>
            <Text style={$numberText}>{formattedNumber}</Text>
            {/* <TouchableOpacity
              style={$rechargeButton}
              onPress={() => navigation.navigate("Recevoir" as never)}
            >
              <Text style={$rechargeButtonText}>Recharger</Text>
            </TouchableOpacity> */}
          </View>

          <View style={$cardFooter}>
            <View>
              <Text style={$label}>TITULAIRE</Text>
              <Text style={$value}>{capitalizedName}</Text>
            </View>
            <View>
              <Text style={$label}>SOLDE</Text>
              <Text style={$value}>FC {formattedBalance}</Text>
            </View>
          </View>
        </LinearGradient>
      </ImageBackground>
    </TouchableOpacity>
  )
}

const $container: ViewStyle = {
  shadowColor: "#000",
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.25,
  shadowRadius: 3.84,
  elevation: 5,
}

const $card: ViewStyle = {
  borderRadius: 10,
  padding: spacing.md,
  height: 220,
  justifyContent: "space-between",
}

const $cardHeader: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
}

const $cardType: TextStyle = {
  color: colors.palette.neutral100,
  fontSize: 20,
  fontWeight: "bold",
  letterSpacing: 2,
}

const $chipIcon: ViewStyle = {
  width: 40,
  height: 30,
  backgroundColor: "#FFD700",
  borderRadius: 5,
  opacity: 0.8,
}

const $cardNumber: ViewStyle = {
  flexDirection: "row",
  flex: 1,
  justifyContent: "space-between",
  marginVertical: spacing.lg,
}

const $numberText: TextStyle = {
  color: colors.palette.neutral100,
  fontSize: 22,
  flex: 1,
  letterSpacing: 4,
  fontWeight: "500",
}

const $cardFooter: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
  marginTop: spacing.sm,
}

const $label: TextStyle = {
  color: colors.palette.neutral300,
  fontSize: 12,
  letterSpacing: 1,
}

const $value: TextStyle = {
  color: colors.palette.neutral100,
  fontSize: 15,
  fontWeight: "500",
  marginTop: spacing.xs,
}

const $backgroundImage: ImageStyle = {
  width: "100%",
  height: 220,
  borderRadius: 10,
  overflow: "hidden", // This ensures the image respects the border radius
}

// const $rechargeButton: ViewStyle = {
//   backgroundColor: "rgba(255, 255, 255, 0.2)",
//   borderRadius: 20,
//   paddingVertical: spacing.xxs,
//   paddingHorizontal: spacing.xs,
//   alignSelf: "center",
//   right: 0,
//   // marginBottom: spacing.sm,
//   borderWidth: 1,
//   borderColor: colors.palette.neutral200,
//   position: "absolute",
//   // bottom: 50,
// }

// const $rechargeButtonText: TextStyle = {
//   color: colors.palette.neutral100,
//   fontSize: 14,
//   fontWeight: "500",
//   textAlign: "center",
// }

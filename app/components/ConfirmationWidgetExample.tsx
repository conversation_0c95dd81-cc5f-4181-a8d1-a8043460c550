import React, { useState } from "react"
import { View, Button } from "react-native"
import { ConfirmationWidget } from "./ConfirmationWidget"

/**
 * Example usage of the ConfirmationWidget component
 * This shows how to integrate it into your topup screens
 */
export const ConfirmationWidgetExample = () => {
  const [showConfirmation, setShowConfirmation] = useState(false)
  const [isSuccess, setIsSuccess] = useState(true)

  // Example successful API response
  const successApiResponse = {
    id: "TXN_123456789",
    transaction_id: "FED_2024_001234",
    amount: 5000,
    currency: "CDF",
    status: "completed",
    reference: "REF_ABC123",
    meter_number: "***********",
    token: "1234-5678-9012-3456-7890",
    units: 25.5,
    phone_number: "+************",
    created_at: "2024-01-15T10:30:00Z",
    service_provider: "SNEL",
    customer_name: "<PERSON>",
    balance_before: 15000,
    balance_after: 10000,
  }

  // Example error message
  const errorMessage = "Insufficient funds. Please top up your account and try again."

  const handleSuccess = () => {
    setIsSuccess(true)
    setShowConfirmation(true)
  }

  const handleFailure = () => {
    setIsSuccess(false)
    setShowConfirmation(true)
  }

  const handleClose = () => {
    setShowConfirmation(false)
    // Navigate to dashboard
    console.log("🏠 Navigating to dashboard...")
  }

  return (
    <View style={{ flex: 1, justifyContent: "center", padding: 20, gap: 20 }}>
      <Button title="Show Success Confirmation" onPress={handleSuccess} />
      <Button title="Show Failure Confirmation" onPress={handleFailure} />

      <ConfirmationWidget
        visible={showConfirmation}
        isSuccess={isSuccess}
        apiResponse={isSuccess ? successApiResponse : undefined}
        errorMessage={isSuccess ? undefined : errorMessage}
        transactionType="Recharge SNEL"
        onButtonPress={handleClose}
      />
    </View>
  )
}

/**
 * How to integrate into your topup screens:
 * 
 * 1. Add state for confirmation visibility and result:
 * ```typescript
 * const [showConfirmation, setShowConfirmation] = useState(false)
 * const [transactionResult, setTransactionResult] = useState<{
 *   isSuccess: boolean
 *   apiResponse?: any
 *   errorMessage?: string
 * } | null>(null)
 * ```
 * 
 * 2. Handle API response in your topup function:
 * ```typescript
 * const handleTopup = async () => {
 *   try {
 *     const response = await topupAPI(data)
 *     
 *     if (response.success) {
 *       setTransactionResult({
 *         isSuccess: true,
 *         apiResponse: response.data
 *       })
 *     } else {
 *       setTransactionResult({
 *         isSuccess: false,
 *         errorMessage: response.message
 *       })
 *     }
 *     setShowConfirmation(true)
 *   } catch (error) {
 *     setTransactionResult({
 *       isSuccess: false,
 *       errorMessage: "Network error. Please try again."
 *     })
 *     setShowConfirmation(true)
 *   }
 * }
 * ```
 * 
 * 3. Add the ConfirmationWidget to your screen:
 * ```typescript
 * <ConfirmationWidget
 *   visible={showConfirmation}
 *   isSuccess={transactionResult?.isSuccess || false}
 *   apiResponse={transactionResult?.apiResponse}
 *   errorMessage={transactionResult?.errorMessage}
 *   transactionType="SNEL Electricity"
 *   onButtonPress={() => {
 *     setShowConfirmation(false)
 *     navigation.navigate("Dashboard")
 *   }}
 * />
 * ```
 */

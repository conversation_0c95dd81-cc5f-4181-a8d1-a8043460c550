import { StyleProp, TextStyle, View, ViewStyle } from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"
import { Text } from "."
import { useEffect, useState } from "react"

export interface OtpExpirationTimerProps {
  otpExpiration: number
  style?: StyleProp<ViewStyle>
}

export const OtpExpirationTimer = (props: OtpExpirationTimerProps) => {
  const { style, otpExpiration } = props
  const $styles = [$container, style]
  const { themed } = useAppTheme()
  const [timeLeft, setTimeLeft] = useState(0)

  // Format the remaining time as MM:SS
  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 1000 / 60)
    const seconds = Math.floor((time / 1000) % 60)
    return `${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`
  }

  useEffect(() => {
    const interval = setInterval(() => {
      const remainingTime = otpExpiration - Date.now()
      if (remainingTime <= 0) {
        clearInterval(interval)
        setTimeLeft(0) // Time has expired
      } else {
        setTimeLeft(remainingTime)
      }
    }, 1000) // Update every second

    // Clear the interval when the component unmounts
    return () => clearInterval(interval)
  }, [otpExpiration])

  return (
    <View style={$styles}>
      <Text style={themed($text)}>{formatTime(timeLeft)}</Text>
    </View>
  )
}

const $container: ViewStyle = {
  justifyContent: "center",
}

const $text: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontFamily: typography.primary.normal,
  fontSize: 14,
  color: colors.palette.primary500,
})

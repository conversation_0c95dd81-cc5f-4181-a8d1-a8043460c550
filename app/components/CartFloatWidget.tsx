import { StyleProp, View, ViewStyle } from "react-native"
import { colors, spacing } from "@/theme"
import { CartWidget } from "."

export interface CartFloatWidgetProps {
  navigation: any
  style?: StyleProp<ViewStyle>
}

/**
 * Describe your component here
 */

export const CartFloatWidget = (props: CartFloatWidgetProps) => {
  const { style, navigation } = props
  const $styles = [$container, style]
  // const { themed } = useAppTheme()

  return (
    <View style={$styles}>
      <CartWidget navigation={navigation} />
    </View>
  )
}

const $container: ViewStyle = {
  position: "absolute",
  // bottom: spacing.lg,
  top: spacing.xxl * 2,
  left: spacing.xxs,
  backgroundColor: colors.palette.neutral100,
  borderRadius: 30,
  shadowColor: "#000",
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.25,
  shadowRadius: 3.84,
  elevation: 5,
  // padding: spacing.xs,
}

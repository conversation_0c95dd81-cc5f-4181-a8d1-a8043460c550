/* eslint-disable no-restricted-imports */
/* eslint-disable @typescript-eslint/no-unused-vars */
import React from "react"
import {
  Modal,
  View,
  TouchableOpacity,
  ViewStyle,
  TextStyle,
  Dimensions,
  TouchableWithoutFeedback,
  Image,
  ImageStyle,
} from "react-native"
import { Avatar } from "react-native-paper"
import { colors, spacing } from "@/theme"
import { Text, Icon } from "."
import { useStores } from "@/store/rootStore"
import { capitalizeAllLetters } from "@/utils/actions"

export interface UserProfileModalProps {
  isVisible: boolean
  onClose: () => void
}

export const UserProfileModal = (props: UserProfileModalProps) => {
  const { isVisible, onClose } = props
  const {
    auth: { user },
    fedhapochi: { wallet },
  } = useStores()

  const handleBackdropPress = () => {
    onClose()
  }

  const handleModalContentPress = (e: any) => {
    e.stopPropagation()
  }

  const fullName =
    `${capitalizeAllLetters(user?.first_name || "")} ${capitalizeAllLetters(user?.last_name || "")}`.trim()
  const initials = `${capitalizeAllLetters(user?.first_name?.[0]) || ""}${capitalizeAllLetters(user?.last_name?.[0]) || ""}`

  return (
    <Modal visible={isVisible} transparent animationType="fade" onRequestClose={onClose}>
      <TouchableWithoutFeedback onPress={handleBackdropPress}>
        <View style={$modalOverlay}>
          <TouchableWithoutFeedback onPress={handleModalContentPress}>
            <View style={$modalContent}>
              {/* Close Button */}
              <TouchableOpacity style={$closeButton} onPress={onClose}>
                <Icon icon="x" size={24} color={colors.palette.neutral600} />
              </TouchableOpacity>

              {/* Profile Picture */}
              <View style={$profileSection}>
                {user?.profile_picture ? (
                  <Avatar.Image
                    size={90}
                    source={{ uri: user.profile_picture }}
                    style={$profileImage}
                  />
                ) : (
                  <Avatar.Text
                    size={90}
                    label={initials}
                    style={[$profileImage, { backgroundColor: colors.palette.neutral900 }]}
                    labelStyle={$avatarLabel}
                  />
                )}
              </View>

              {/* User Name */}
              <View style={$nameSection}>
                <Text style={$userName}>{fullName || "Utilisateur"}</Text>
              </View>
              {/* QR Code Section */}
              {wallet?.qr_image && (
                <View style={$qrSection}>
                  <View style={$qrContainer}>
                    <Image
                      source={{ uri: wallet.qr_image }}
                      style={$qrImage}
                      resizeMode="contain"
                    />
                  </View>
                </View>
              )}
              <View style={$infoTextContainer}>
                <Text style={$infoLabel}>FedhaPochi ID: {wallet?.wallet || "Non disponible"}</Text>
              </View>
            </View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  )
}

const { width, height } = Dimensions.get("window")

const $modalOverlay: ViewStyle = {
  flex: 1,
  backgroundColor: "rgba(0, 0, 0, 0.6)",
  justifyContent: "center",
  alignItems: "center",
}

const $modalContent: ViewStyle = {
  backgroundColor: colors.palette.neutral100,
  borderRadius: 20,
  padding: spacing.xl,
  width: width * 0.9,
  maxWidth: 400,
  maxHeight: height * 0.8,
  shadowColor: colors.palette.neutral900,
  shadowOffset: { width: 0, height: 8 },
  shadowOpacity: 0.15,
  shadowRadius: 16,
  elevation: 8,
  position: "relative",
}

const $closeButton: ViewStyle = {
  position: "absolute",
  right: spacing.md,
  top: spacing.md,
  zIndex: 1,
  backgroundColor: colors.palette.neutral200,
  borderRadius: 20,
  padding: spacing.xs,
}

const $profileSection: ViewStyle = {
  alignItems: "center",
  marginTop: spacing.lg,
  marginBottom: spacing.lg,
}

const $profileImage: ViewStyle = {
  shadowColor: colors.palette.neutral900,
  shadowOffset: { width: 0, height: 4 },
  shadowOpacity: 0.1,
  shadowRadius: 8,
  elevation: 4,
}

const $avatarLabel: TextStyle = {
  fontSize: 36,
  fontWeight: "bold",
}

const $nameSection: ViewStyle = {
  alignItems: "center",
  // marginBottom: spacing.xl,
}

const $userName: TextStyle = {
  fontSize: 17,
  color: colors.palette.neutral800,
  textAlign: "center",
}

const $infoTextContainer: ViewStyle = {
  alignItems: "center",
}

const $infoLabel: TextStyle = {
  fontSize: 14,
  color: colors.palette.neutral800,
  // marginBottom: spacing.xxs,
}

const $qrSection: ViewStyle = {
  alignItems: "center",
}

// const $qrLabel: TextStyle = {
//   fontSize: 16,
//   fontWeight: "600",
//   color: colors.palette.neutral700,
//   marginBottom: spacing.md,
// }

const $qrContainer: ViewStyle = {
  // backgroundColor: colors.palette.neutral100,
  // borderRadius: 16,
  // padding: spacing.md,
  // shadowColor: colors.palette.neutral900,
  // shadowOffset: { width: 0, height: 2 },
  // shadowOpacity: 0.1,
  // shadowRadius: 4,
  // elevation: 2,
}

const $qrImage: ImageStyle = {
  width: 250,
  height: 250,
  borderRadius: 8,
}

/* eslint-disable import/no-unresolved */
import { StyleProp, TextStyle, TouchableOpacity, View, Modal, ViewStyle } from "react-native"
import { colors, spacing } from "@/theme"
import { Text } from "."
import { useStores } from "@/store/rootStore"

export interface ChangeCurencyProps {
  isVisible: boolean
  onClose: () => void
  style?: StyleProp<ViewStyle>
}

export const ChangeCurency = (props: ChangeCurencyProps) => {
  const {
    appsettings: { currency, setCurrency },
    fedhapochi: { availableCurrencies, updateCurrentBalance },
  } = useStores()
  const { isVisible, onClose, style } = props
  const $styles = [$container, style]

  const handleCurrencyChange = (newCurrency: "FC" | "USD") => {
    setCurrency(newCurrency)
    updateCurrentBalance(newCurrency)
    onClose()
  }

  // console.log("z", availableCurrencies)
  const getCurrencyLabel = (currencyCode: string) => {
    switch (currencyCode) {
      case "FC":
        return "Franc Congolais (FC)"
      case "USD":
        return "American Dollar (USD)"
      default:
        return currencyCode
    }
  }

  const isCurrencySelected = (currencyCode: string) => {
    return (
      (currency === "FC" && currencyCode === "FC") || (currency === "USD" && currencyCode === "USD")
    )
  }

  return (
    <View style={$styles}>
      <Modal visible={isVisible} transparent animationType="slide" onRequestClose={onClose}>
        <View style={$modalOverlay}>
          <View style={$modalContent}>
            <Text style={$modalTitle}>Select Currency</Text>

            {availableCurrencies.map((currencyCode) => (
              <TouchableOpacity
                key={currencyCode}
                style={[$currencyOption, isCurrencySelected(currencyCode) && $selectedOption]}
                onPress={() => handleCurrencyChange(currencyCode as "FC" | "USD")}
              >
                <Text style={$currencyText}>{getCurrencyLabel(currencyCode)}</Text>
              </TouchableOpacity>
            ))}

            <TouchableOpacity style={$closeButton} onPress={onClose}>
              <Text style={$closeButtonText}>Close</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  )
}

const $container: ViewStyle = {
  justifyContent: "center",
}

const $modalOverlay: ViewStyle = {
  flex: 1,
  backgroundColor: "rgba(0, 0, 0, 0.5)",
  justifyContent: "center",
  alignItems: "center",
}

const $modalContent: ViewStyle = {
  backgroundColor: colors.palette.neutral100,
  padding: spacing.lg,
  borderRadius: 10,
  width: "80%",
}

const $modalTitle: TextStyle = {
  fontSize: 20,
  fontWeight: "600",
  textAlign: "center",
  marginBottom: spacing.md,
  color: colors.palette.neutral900,
}

const $currencyOption: ViewStyle = {
  padding: spacing.md,
  borderRadius: 8,
  marginVertical: spacing.xs,
  backgroundColor: colors.palette.neutral200,
}

const $selectedOption: ViewStyle = {
  backgroundColor: colors.palette.primary100,
}

const $currencyText: TextStyle = {
  fontSize: 16,
  color: colors.palette.neutral900,
}

const $closeButton: ViewStyle = {
  marginTop: spacing.md,
  padding: spacing.sm,
  backgroundColor: colors.palette.primary500,
  borderRadius: 8,
  alignItems: "center",
}

const $closeButtonText: TextStyle = {
  color: colors.palette.neutral100,
  fontSize: 16,
  fontWeight: "600",
}

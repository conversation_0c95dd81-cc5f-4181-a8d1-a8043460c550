import React from "react"
import { TouchableOpacity, Text, StyleSheet, ViewStyle, TextStyle } from "react-native"
import { colors, spacing } from "@/theme"
import { useNotification } from "@/context/NotificationContext"

interface TestNotificationButtonProps {
  style?: ViewStyle
}

export const TestNotificationButton = (props: TestNotificationButtonProps) => {
  const { style } = props
  const { sendTestNotification } = useNotification()

  const handlePress = async () => {
    try {
      await sendTestNotification()
    } catch (error) {
      console.error("Failed to send test notification:", error)
    }
  }

  return (
    <TouchableOpacity style={[styles.button, style]} onPress={handlePress}>
      <Text style={styles.text}>Test Notification Sound</Text>
    </TouchableOpacity>
  )
}

const styles = StyleSheet.create({
  button: {
    backgroundColor: colors.palette.primary500,
    padding: spacing.sm,
    borderRadius: 8,
    alignItems: "center",
    marginVertical: spacing.sm,
  },
  text: {
    color: colors.palette.neutral100,
    fontWeight: "bold",
  },
})

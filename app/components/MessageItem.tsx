import { StyleProp, TextStyle, TouchableOpacity, View, ViewStyle } from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import { colors, spacing, type ThemedStyle } from "@/theme"
import { Icon, Text } from "."

export interface MessageItemProps {
  title: string
  subtext: string
  timeStamp: any
  icon?: string
  unreadCount?: number
  style?: StyleProp<ViewStyle>
  onPress?: () => void
}

/**
 * Message item component that displays a notification group
 * with icon, title, preview text, timestamp, and unread count
 */

export const MessageItem = (props: MessageItemProps) => {
  const { style, title, subtext, timeStamp, icon = "noIcon", unreadCount = 0, onPress } = props

  const $styles = [$container, unreadCount > 0 && $unreadContainer, style]
  const { themed } = useAppTheme()

  return (
    <TouchableOpacity style={$styles} onPress={onPress}>
      <View style={$iconContainer}>
        <Icon
          icon={(icon as any) || "noIcon"}
          size={29}
          color={colors.palette.primary500}
          containerStyle={$iconStyle}
        />
        {/* {unreadCount > 0 && (
          <View style={$badgeContainer}>
            <Text style={$badgeText}>{unreadCount > 9 ? "9+" : unreadCount}</Text>
          </View>
        )} */}
      </View>
      <View style={$optionTextContainer}>
        <View style={$titleContainer}>
          <Text style={themed($title)}>{title}</Text>
          <Text style={themed($timeStamp)}>{timeStamp}</Text>
        </View>
        <Text style={[themed($subtitle), unreadCount > 0 && $unreadText]} numberOfLines={1}>
          {subtext}
        </Text>
      </View>
    </TouchableOpacity>
  )
}

const $container: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  padding: spacing.md,
  borderRadius: 10,
  marginTop: 10,
  marginHorizontal: spacing.xs,
  backgroundColor: colors.palette.neutral100,
  borderBottomWidth: 1,
  borderBottomColor: colors.palette.neutral300,
  position: "relative",
}

const $unreadContainer: ViewStyle = {
  backgroundColor: colors.palette.neutral200,
  borderLeftWidth: 3,
  borderLeftColor: colors.palette.primary500,
}

const $title: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontFamily: typography.primary.normal,
  fontSize: 17,
  color: colors.palette.neutral900,
  flex: 1,
})

const $subtitle: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontFamily: typography.primary.normal,
  fontSize: 12,
  color: colors.palette.accent400,
  marginTop: spacing.xs,
})

const $unreadText: TextStyle = {
  fontWeight: "bold",
  color: colors.palette.neutral800,
}

const $timeStamp: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontFamily: typography.primary.normal,
  fontSize: 11,
  color: colors.palette.neutral500,
})

const $iconContainer: ViewStyle = {
  position: "relative",
}

const $iconStyle: ViewStyle = {
  borderRadius: 20,
  borderWidth: 2,
  padding: 5,
  borderColor: colors.palette.neutral300,
}

// const $badgeContainer: ViewStyle = {
//   position: "absolute",
//   top: -5,
//   right: -5,
//   backgroundColor: colors.palette.angry500,
//   borderRadius: 10,
//   width: 20,
//   height: 20,
//   justifyContent: "center",
//   alignItems: "center",
// }

// const $badgeText: TextStyle = {
//   color: colors.palette.neutral100,
//   fontSize: 10,
//   fontWeight: "bold",
// }

const $titleContainer: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
}

const $optionTextContainer: ViewStyle = {
  flex: 1,
  marginLeft: spacing.sm,
}

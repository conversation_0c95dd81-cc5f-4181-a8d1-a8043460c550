/* eslint-disable @typescript-eslint/no-unused-vars */
import { StyleProp, TextStyle, View, ViewStyle } from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"
import { Text } from "."

export interface TarrifValue {
  [key: string]: number // e.g., { cdf: 50000, usd: 3 }
}

// Describes the structure of each item in the 'data' array
export interface TarrifItem {
  title: string // Changed from 'label'
  value: TarrifValue
}

export interface TarrifsProps {
  /**
   * An optional style override useful for padding & margin.
   */
  style?: StyleProp<ViewStyle>
  /**
   * Array of tarrif items to display in the table
   */
  data: TarrifItem[]
  /**
   * Optional title for the table
   */
  title?: string
  tariftype?: string
  lastUpdated?: string
  currencyHeaders?: [string, string]
  valueKeys: [string, string]
}

const DEFAULT_CURRENCY_HEADERS: [string, string] = ["FC", "$"]

// Helper function to format currency values
const formatCurrencyValue = (rawValue: number | undefined, currencySymbol: string): string => {
  if (rawValue === 0) {
    return "Gratuit"
  }
  if (typeof rawValue === "number") {
    // You could add more sophisticated number formatting here if needed (e.g., thousands separators)
    return `${rawValue}`
  }
  return "N/A" // Fallback for undefined or non-numeric values
}

export const Tarrifs = (props: TarrifsProps) => {
  const { style, data, title, lastUpdated, valueKeys, tariftype } = props
  const $styles = [$container, style]
  const columnDisplayNames = props.currencyHeaders || DEFAULT_CURRENCY_HEADERS
  const $componentStyles = [$container, style]
  const { themed } = useAppTheme()

  if (!valueKeys || valueKeys.length !== 2) {
    // Basic validation for the mandatory prop in development
    if (__DEV__) {
      console.warn(
        "Tarrifs component: 'valueKeys' prop is mandatory and must be an array of two strings.",
      )
    }
    return null // Or render an error message
  }

  return (
    <View style={$componentStyles}>
      {(title || lastUpdated) && (
        <View style={$titleSectionContainer}>
          {title && <Text style={themed($titleText)}>{title}</Text>}
          {lastUpdated && <Text style={themed($lastUpdatedText)}>{lastUpdated}</Text>}
        </View>
      )}

      <View style={themed($tableOuterContainer)}>
        {/* Header Row */}
        <View style={[themed($tableRow), themed($headerRowStyle)]}>
          <Text style={[themed($labelCellText), themed($headerLabelCellText)]}>
            {tariftype && tariftype}
          </Text>
          <View style={$valueColumnsWrapper}>
            <Text
              style={[themed($valueCellBaseText), themed($headerValueCellText), $valueColumnLayout]}
            >
              {columnDisplayNames[0]}
            </Text>
            <Text
              style={[themed($valueCellBaseText), themed($headerValueCellText), $valueColumnLayout]}
            >
              {columnDisplayNames[1]}
            </Text>
          </View>
        </View>

        {/* Data Rows */}
        {data.map((item, index) => {
          const rawValue1 = item.value?.[valueKeys[0]]
          const rawValue2 = item.value?.[valueKeys[1]]

          const displayValue1 = formatCurrencyValue(rawValue1, columnDisplayNames[0])
          const displayValue2 = formatCurrencyValue(rawValue2, columnDisplayNames[1])

          return (
            <View
              key={index} // It's better to use a unique ID from 'item' if available, e.g., item.id
              style={[themed($tableRow), index === data.length - 1 && $lastRowStyle]}
            >
              <Text style={themed($labelCellText)}>{item.title}</Text>
              <View style={$valueColumnsWrapper}>
                <Text style={[themed($valueCellBaseText), $valueColumnLayout]}>
                  {displayValue1}
                </Text>
                <Text style={[themed($valueCellBaseText), $valueColumnLayout]}>
                  {displayValue2}
                </Text>
              </View>
            </View>
          )
        })}
      </View>
    </View>
  )
}

// Styles (remain largely the same as the previous version)
const $container: ViewStyle = {
  // flex: 1,
  paddingVertical: 18,
  paddingHorizontal: 18,
}

const $titleSectionContainer: ViewStyle = {
  // flexDirection: "row",
  justifyContent: "center",
  alignItems: "center",
  marginBottom: 16,
}

const $titleText: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontFamily: typography.primary.medium,
  fontSize: 18,
  color: colors.text,
  flexShrink: 1,
  marginRight: 8,
})

const $lastUpdatedText: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontFamily: typography.primary.normal,
  fontSize: 12,
  color: colors.palette.neutral400,
  textAlign: "right",
})

const $tableOuterContainer: ThemedStyle<ViewStyle> = ({ colors }) => ({
  backgroundColor: colors.palette.neutral900,
  borderRadius: 8,
  overflow: "hidden",
})

const $tableRow: ThemedStyle<ViewStyle> = ({ colors }) => ({
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
  paddingHorizontal: 16,
  paddingVertical: 14,
  borderBottomWidth: 1,
  borderBottomColor: colors.palette.neutral700,
})

const $headerRowStyle: ThemedStyle<ViewStyle> = ({ colors }) => ({
  paddingVertical: 12,
})

const $lastRowStyle: ViewStyle = {
  borderBottomWidth: 0,
}

const $labelCellText: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontFamily: typography.primary.normal,
  fontSize: 14,
  color: colors.palette.neutral100,
  flexGrow: 1,
  flexShrink: 1,
  marginRight: 8,
})

const $headerLabelCellText: ThemedStyle<TextStyle> = ({ typography }) => ({
  fontFamily: typography.primary.medium,
})

const $valueColumnsWrapper: ViewStyle = {
  flexDirection: "row",
}

const $valueCellBaseText: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontFamily: typography.primary.normal,
  fontSize: 14,
  color: colors.palette.neutral100,
})

const $headerValueCellText: ThemedStyle<TextStyle> = ({ typography, colors }) => ({
  fontFamily: typography.primary.medium,
  color: colors.palette.neutral200,
})

const $valueColumnLayout: TextStyle = {
  width: 80, // Adjusted width slightly, tune as needed
  textAlign: "right",
  marginLeft: 4,
}

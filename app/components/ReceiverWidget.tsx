/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable import/no-unresolved */
import { StyleProp, TextStyle, View, ViewStyle } from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import { colors, type ThemedStyle } from "@/theme"
import { Text } from "."
import { Avatar } from "react-native-paper"
import { getNameInitials } from "@/utils/actions"

export interface ReceiverWidgetProps {
  username: string
  avatar?: string | null
  style?: StyleProp<ViewStyle>
}

/**
 * Describe your component here
 */

export const ReceiverWidget = (props: ReceiverWidgetProps) => {
  const { style, avatar, username } = props
  const $styles = [$container, style]
  const { themed } = useAppTheme()

  // const getInitials = (name: string): string => {
  //   return name
  //     .split(" ")
  //     .map((part) => part.charAt(0))
  //     .join("")
  //     .toUpperCase()
  //     .slice(0, 2)
  // }
  // const imgDf = require("../../assets/images/jeklulu.jpg")
  return (
    <View style={$styles}>
      {avatar ? (
        <Avatar.Image size={100} source={{ uri: avatar }} />
      ) : (
        <Avatar.Text
          size={100}
          label={getNameInitials(username)}
          style={{ backgroundColor: colors.palette.neutral900 }}
          labelStyle={$avatarLabel}
        />
      )}
      <Text style={themed($text)}>{username}</Text>
    </View>
  )
}

const $container: ViewStyle = {
  justifyContent: "center",
  alignItems: "center",
  flex: 1,
}

const $avatarLabel: TextStyle = {
  fontSize: 32,
  fontWeight: "bold",
}

const $text: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  // fontFamily: typography.primary.normal,
  fontSize: 24,
  marginVertical: 10,
  // fontWeight: "bold",
  alignSelf: "center",
  letterSpacing: 2,
  color: colors.text,
})

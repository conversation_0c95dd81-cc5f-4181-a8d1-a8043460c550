/* eslint-disable react-native/no-color-literals */
import { FC } from "react"
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ImageBackground,
  StyleProp,
  ViewStyle,
  ImageSourcePropType,
} from "react-native"
import { Icon } from "@/components"
import { colors, spacing } from "@/theme"

export interface MainCourseProps {
  style?: StyleProp<ViewStyle>
  title: string
  subtitle: string
  duration: string
  hasliked?: boolean
  likes?: number
  points?: string
  image: ImageSourcePropType
  onPress?: () => void
  type?: "video" | "quiz" | "challenge"
}

export const MainCourse: FC<MainCourseProps> = ({
  style,
  title,
  subtitle,
  duration,
  likes,
  points,
  hasliked,
  image,
  onPress,
  type = "challenge",
}: MainCourseProps) => {
  return (
    <TouchableOpacity style={[styles.container, style]} activeOpacity={0.9} onPress={onPress}>
      <ImageBackground
        source={image}
        style={styles.image}
        imageStyle={styles.imageStyle}
        resizeMode="cover"
      >
        <View style={styles.overlay}>
          <View style={styles.content}>
            <View style={styles.titleContainer}>
              <Text style={styles.title}>{title}</Text>
              <Text style={styles.subtitle}>{subtitle}</Text>
            </View>

            <View style={styles.bottomRow}>
              <View style={styles.metaContainer}>
                <Icon icon="T0clock" size={24} color={colors.palette.neutral200} />
                <Text style={styles.metaText}>{duration}</Text>
                {points && (
                  <View style={styles.pointsContainer}>
                    <Icon icon="increase" size={24} color={colors.palette.neutral200} />
                    <Text style={styles.metaText}>{points}</Text>
                  </View>
                )}
                <TouchableOpacity style={styles.pointsContainer} onPress={() => {}}>
                  {likes && hasliked ? (
                    <Icon icon="likefull" size={24} color={colors.palette.neutral300} />
                  ) : (
                    <Icon icon="heart" size={24} color={colors.palette.neutral200} />
                  )}

                  <Text style={styles.metaText}>{likes}</Text>
                </TouchableOpacity>
              </View>

              <View style={styles.progressContainer}>
                {/* <View style={styles.progressBar}>
                  <View style={styles.progressFill} />
                </View> */}
              </View>
            </View>
          </View>
        </View>

        <TouchableOpacity style={styles.playButton}>
          <Icon
            icon={type === "quiz" ? "caretRight" : "play"}
            size={20}
            color={colors.palette.neutral100}
          />
        </TouchableOpacity>
      </ImageBackground>
    </TouchableOpacity>
  )
}

const styles = StyleSheet.create({
  bottomRow: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: spacing.xs,
  },
  container: {
    borderRadius: 16,
    elevation: 3,
    height: 250,
    marginHorizontal: spacing.md,
    marginVertical: spacing.md,
    overflow: "hidden",
    shadowColor: colors.palette.neutral900,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  content: {
    padding: spacing.md,
  },
  image: {
    height: "100%",
    width: "100%",
  },
  imageStyle: {
    borderRadius: 16,
  },
  metaContainer: {
    alignItems: "center",
    flexDirection: "row",
  },
  metaText: {
    color: colors.palette.neutral300,
    fontSize: 12,
    marginLeft: spacing.xs / 2,
  },
  overlay: {
    backgroundColor: "rgba(0, 0, 0, 0.4)",
    flex: 1,
    justifyContent: "flex-end",
  },
  playButton: {
    alignItems: "center",
    backgroundColor: colors.palette.primary500,
    borderRadius: 20,
    bottom: spacing.md,
    elevation: 5,
    height: 40,
    justifyContent: "center",
    position: "absolute",
    right: spacing.md,
    shadowColor: colors.palette.neutral900,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    width: 40,
  },
  pointsContainer: {
    alignItems: "center",
    flexDirection: "row",
    marginLeft: spacing.sm,
  },
  // progressBar: {
  //   backgroundColor: colors.palette.neutral300 + "50",
  //   borderRadius: 2,
  //   height: 4,
  // },
  progressContainer: {
    flex: 1,
    marginLeft: spacing.md,
    maxWidth: 100,
  },
  // progressFill: {
  //   backgroundColor: colors.palette.primary500,
  //   borderRadius: 2,
  //   height: "100%",
  //   width: "30%",
  // },
  subtitle: {
    color: colors.palette.neutral300,
    fontSize: 14,
  },
  title: {
    color: colors.palette.neutral100,
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: spacing.xs / 2,
  },
  titleContainer: {
    marginBottom: spacing.lg,
  },
})

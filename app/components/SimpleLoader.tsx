/* eslint-disable import/no-unresolved */
import { Modal, View, ViewStyle } from "react-native"
import { colors } from "@/theme"
import { FedhaLoader } from "./atoms/FedhaLoader"

export interface SimpleLoaderProps {
  visible: boolean
}
export const SimpleLoader = (props: SimpleLoaderProps) => {
  const { visible } = props

  return (
    <Modal visible={visible} transparent animationType="fade">
      <View style={$modalContainer}>
        <FedhaLoader size={280} />
      </View>
    </Modal>
  )
}

const $modalContainer: ViewStyle = {
  flex: 1,
  justifyContent: "center",
  alignItems: "center",
  backgroundColor: colors.palette.neutral100, // Semi-transparent dark background
  position: "absolute",
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  zIndex: 9999, // Ensure it's on top of everything
}

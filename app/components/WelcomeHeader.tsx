/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable react/jsx-no-undef */
import { StyleProp, TextStyle, View, ViewStyle, Image, ImageStyle } from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withRepeat,
  withDelay,
  withSequence,
  withTiming,
  Easing,
} from "react-native-reanimated"

import type { ThemedStyle } from "@/theme"
import { Text } from "."

export interface WelcomeHeaderProps {
  /**
   * An optional style override useful for padding & margin.
   */
  style?: StyleProp<ViewStyle>
}

const BouncingImage = ({
  source,
  style,
  delay,
  duration,
}: {
  source: any
  style: any
  delay: number
  duration: number
}) => {
  const translateY = useSharedValue(0)

  translateY.value = withDelay(
    delay,
    withRepeat(
      withSequence(
        withTiming(-10, { duration, easing: Easing.inOut(Easing.ease) }),
        withTiming(0, { duration, easing: Easing.inOut(Easing.ease) }),
      ),
      -1, // Infinite loop
      false, // No alternating, just loops the sequence
    ),
  )

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: translateY.value }],
  }))

  return <Animated.Image source={source} style={[style, animatedStyle]} resizeMode="contain" />
}

export const WelcomeHeader = (props: WelcomeHeaderProps) => {
  const { style } = props
  const $styles = [$container, style]
  const { themed } = useAppTheme()

  const IMG1 = require("../../assets/images/people/business.png")
  const IMG2 = require("../../assets/images/people/familly.png")
  const IMG3 = require("../../assets/images/people/womenmobile-phone.png")
  const IMG4 = require("../../assets/images/people/taptoScan.png")

  return (
    <View style={$styles}>
      <BouncingImage source={IMG1} style={themed($Img1)} delay={0} duration={1200} />
      <BouncingImage source={IMG2} style={themed($Img2)} delay={500} duration={1000} />
      <BouncingImage source={IMG3} style={themed($Img3)} delay={1000} duration={1400} />
      <BouncingImage source={IMG4} style={themed($Img4)} delay={1500} duration={1100} />
    </View>
  )
}

const $container: ViewStyle = {
  justifyContent: "center",
}

const $Img1: ThemedStyle<ImageStyle> = ({ spacing }) => ({
  height: 144,
  width: 144,
  position: "absolute",
  // borderRadius: 30,
  top: 60,
  // transform: [{ translateX: 20 }, { translateY: 50 }, { rotate: "-15deg" }],
})

const $Img2: ThemedStyle<ImageStyle> = ({ spacing }) => ({
  height: 130,
  width: 120,
  position: "absolute",
  borderRadius: 10,
  top: 44,
  left: 185,
  // transform: [{ translateX: 50 }, { translateY: 50 }, { rotate: "-8deg" }],
})

const $Img3: ThemedStyle<ImageStyle> = ({ spacing }) => ({
  height: 160,
  width: 160,
  position: "absolute",
  borderRadius: 90,
  top: 190,
  left: -35,
  // transform: [{ translateX: 50 }, { translateY: 50 }, { rotate: "10deg" }],
})

const $Img4: ThemedStyle<ImageStyle> = ({ spacing }) => ({
  height: 240,
  width: 240,
  position: "absolute",
  borderRadius: 10,
  top: 190,
  left: 150,
  // transform: [{ translateX: 50 }, { translateY: 50 }, { rotate: "-15deg" }],
})

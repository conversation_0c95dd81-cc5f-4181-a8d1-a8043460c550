/* eslint-disable import/no-unresolved */
/* eslint-disable @typescript-eslint/no-redeclare */
import {
  StyleProp,
  TextStyle,
  View,
  ViewStyle,
  Image,
  ImageStyle,
  TouchableOpacity,
} from "react-native"
import { colors, spacing } from "@/theme"
import { Icon, Text } from "."
import { CircularProgress } from "react-native-circular-progress"
import { Badge } from "react-native-paper"

export interface ProgressReportProps {
  style?: StyleProp<ViewStyle>
  progress?: number // 0 to 100
  status?: "pending" | "completed" | "failed"
  notificationCount?: number
  onPress?: () => void
  description?: string
  transactionId?: string
}

/**
 * Describe your component here
 */

export const ProgressReport = (props: ProgressReportProps) => {
  const {
    style,
    progress = 0,
    status = "pending",
    notificationCount = 0,
    onPress,
    description = "Achat du SNEL CASH POWER de 20 USD",
    transactionId = "84680029810",
  } = props
  const $styles = [$container, style]
  const profileImageSource = require("../../assets/images/snel.png")

  const getStatusColor = () => {
    switch (status) {
      case "completed":
        return colors.palette.primary100
      case "failed":
        return colors.palette.angry500
      default:
        return colors.palette.accent500
    }
  }

  return (
    <TouchableOpacity style={$styles} onPress={onPress}>
      <Image source={profileImageSource} style={$profileImage} />
      {notificationCount > 0 && <Badge style={$badge}>{notificationCount}</Badge>}
      <View style={$optionTextContainer}>
        <Text preset="default" style={$optionTitle}>
          {transactionId}
        </Text>
        <Text preset="formHelper" style={$optionDescription} numberOfLines={1} ellipsizeMode="tail">
          {description}
        </Text>
      </View>
      {status === "completed" ? (
        <Icon icon="check" size={24} color={colors.palette.primary500} />
      ) : (
        <CircularProgress
          size={35}
          width={3}
          fill={progress}
          tintColor={getStatusColor()}
          backgroundColor={colors.palette.neutral400}
        />
      )}
    </TouchableOpacity>
  )
}
const $container: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  padding: spacing.md,
  borderRadius: 10,
  // marginTop: 10,
  // marginBottom: 20,
  // backgroundColor: colors.palette.neutral300,
  borderBottomWidth: 1,
  borderBottomColor: colors.palette.neutral300,
  position: "relative",
}

const $optionTextContainer: ViewStyle = {
  flex: 1,
  marginLeft: spacing.sm,
}

const $optionTitle: TextStyle = {
  color: colors.palette.neutral800,
  fontSize: 14,
  fontWeight: "600",
}

const $badge: ViewStyle = {
  position: "absolute",
  top: -5,
  right: -5,
  backgroundColor: colors.palette.angry500,
  borderRadius: 10,
  minWidth: 20,
  height: 20,
  justifyContent: "center",
  alignItems: "center",
  zIndex: 1,
}

const $optionDescription: TextStyle = {
  color: colors.palette.neutral700,
  fontSize: 12,
}

const $profileImage: ImageStyle = {
  width: 40,
  height: 40,
  borderRadius: 10,
  borderWidth: 1,
  borderColor: colors.palette.neutral600,
}

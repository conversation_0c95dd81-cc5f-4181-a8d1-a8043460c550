/* eslint-disable import/no-unresolved */
/* eslint-disable react-native/no-inline-styles */
import { Animated, StyleProp, ViewStyle } from "react-native"
import { colors } from "@/theme"
import { useEffect } from "react"

export interface SkeletonLoaderProps {
  width: number
  height: number
  style?: StyleProp<ViewStyle>
}

/**
 * Describe your component here
 */

export const SkeletonLoader = (props: SkeletonLoaderProps) => {
  const { style, width, height } = props
  const $styles = [style]
  const animatedValue = new Animated.Value(0)

  useEffect(() => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(animatedValue, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(animatedValue, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: true,
        }),
      ]),
    ).start()
  }, [])

  return (
    <Animated.View
      style={[
        {
          width,
          height,
          backgroundColor: colors.palette.neutral300, // Change this to any color from your palette
          opacity: animatedValue.interpolate({
            inputRange: [0, 1],
            outputRange: [0.3, 0.7], // You can adjust these values to control the animation intensity
          }),
          borderRadius: 4,
        },
        $styles,
      ]}
    />
  )
}

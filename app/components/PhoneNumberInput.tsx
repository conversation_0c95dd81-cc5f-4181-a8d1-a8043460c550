/* eslint-disable react-native/sort-styles */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable react-native/no-unused-styles */
/* eslint-disable react-native/no-color-literals */
import { StyleProp, StyleSheet, View, Image, ViewStyle, TextInput } from "react-native"
import { Controller } from "react-hook-form"
import { Text, TextProps } from "."
import { colors } from "@/theme"

export interface PhoneNumberInputProps {
  control: any
  name: string
  rules: any
  error?: string
  helper?: TextProps["text"]
  status?: string
  errorMessage?: string
  style?: StyleProp<ViewStyle>
}

export const PhoneNumberInput = (props: PhoneNumberInputProps) => {
  const { style, control, name, error, errorMessage, helper, status, rules } = props
  const $styles = [$container, style]

  const validatePhoneNumber = (value: string) => {
    if (!value) return true // Don't show error for empty field

    const validPrefixes = ["4", "5", "7", "8", "9"]

    if (value.startsWith("0")) {
      return "Ne pas inclure le 0 au début"
    }

    // Only show prefix error if we have at least one digit
    if (value.length >= 1 && !validPrefixes.includes(value[0])) {
      return "Le numéro doit commencer par 4, 5, 7, 8 ou 9"
    }

    // Only show length error if the number is complete
    if (value.length === 9 && !/^\d{9}$/.test(value)) {
      return "Le numéro doit contenir 9 chiffres"
    }

    // If number is incomplete, don't show error
    if (value.length < 9) {
      return true
    }

    // If all validations pass
    return true
  }

  return (
    <Controller
      control={control}
      name={name}
      rules={rules}
      render={({ field, fieldState }) => (
        <>
          <View style={$styles}>
            <View
              style={[
                styles.inputContainer,
                fieldState.error && fieldState.isDirty && styles.errorBorder,
              ]}
            >
              <Image
                source={require("../../assets/images/flags/cd.png")}
                style={styles.flag}
                resizeMode="cover"
              />
              <Text style={styles.countryCode}>+243</Text>
              <View style={styles.inputWrapper}>
                <TextInput
                  style={styles.input}
                  placeholder="9********"
                  keyboardType="numeric"
                  placeholderTextColor={colors.palette.accent400}
                  maxLength={9}
                  value={field.value}
                  onChangeText={(text) => {
                    const formatted = text.replace(/\D/g, "").replace(/^0+/, "")
                    field.onChange(formatted)
                  }}
                  onBlur={field.onBlur}
                />
              </View>
            </View>
          </View>
          {fieldState.error && fieldState.isDirty && (
            <Text preset="formHelper" text={fieldState.error.message} style={styles.errorText} />
          )}
        </>
      )}
    />
  )
}

const $container: ViewStyle = {
  justifyContent: "center",
}

const styles = StyleSheet.create({
  countryCode: {
    fontSize: 18,
    fontWeight: "bold",
    marginRight: 3,
    color: colors.palette.terciary100,
  },
  errorBorder: {
    borderColor: colors.palette.angry500,
    borderWidth: 1,
  },
  errorText: {
    color: colors.palette.angry500,
    marginTop: 9,
    position: "absolute",
    bottom: -25,
    left: 0,
  },
  flag: {
    borderRadius: 16,
    height: 36,
    marginRight: 8,
    width: 36,
  },
  input: {
    flex: 1,
    fontSize: 18,
    paddingVertical: 15,
  },
  inputContainer: {
    alignItems: "center",
    borderRadius: 14,
    borderWidth: 1,
    borderColor: colors.palette.neutral400,
    overflow: "hidden",
    flexDirection: "row",
    height: 60,
    paddingHorizontal: 15,
  },
  inputWrapper: {
    flex: 1,
    position: "relative",
  },
})

/* eslint-disable no-restricted-imports */
/* eslint-disable react-native/no-inline-styles */
/* eslint-disable @typescript-eslint/no-unused-vars */
import * as React from "react"
import { useState, type ReactNode } from "react"
import {
  Dimensions,
  StyleProp,
  TextStyle,
  TouchableOpacity,
  View,
  ViewStyle,
  Image,
  ImageStyle,
  Modal,
  SafeAreaView,
  StatusBar,
} from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import { $Gstyles, colors, spacing, type ThemedStyle } from "@/theme"
import { Button, Icon, Text, TransferFedhaPochi, WalletBalanceWidget, UserProfileModal } from "."
import { useStores } from "@/store/rootStore"
import { getFedhaPochi } from "@/services/api"
import { formatDate, loadDateFnsLocale } from "@/utils/formatDate"
import { Avatar } from "react-native-paper"
import { capitalizeAllLetters } from "@/utils"

export interface MainHeaderProps {
  navigation: any
  style?: StyleProp<ViewStyle>
}

export const MainHeader = (props: MainHeaderProps) => {
  const {
    auth: { user, logout },
    fedhapochi: { wallet },
  } = useStores()

  const { style, navigation } = props
  const $styles = [$container, style]
  const { themed } = useAppTheme()
  const [envoyerstate, setEnvoyerState] = useState(false)
  const [profileModalVisible, setProfileModalVisible] = useState(false)

  const balanceupdatedate = wallet?.last_update
    ? (() => {
        loadDateFnsLocale() // This will load French locale since it's set as default
        return formatDate(wallet.last_update, "d MMMM yyyy, HH:mm")
      })()
    : "..."

  const walletBalance = wallet?.balance ? wallet.balance.toFixed(2) : 0.0
  return (
    <>
      <View style={$styles}>
        <View
          style={{ flexDirection: "row", alignContent: "center", justifyContent: "space-between" }}
        >
          <View style={{ flex: 1 }}>
            <Text style={themed($text)}>Solde disponible</Text>
            <WalletBalanceWidget amount={walletBalance} />
            <Text
              style={[themed($text), { marginTop: -4 }]}
            >{`Mise à jour le ${balanceupdatedate}`}</Text>
          </View>

          <TouchableOpacity onPress={() => setProfileModalVisible(true)} style={{ top: 15 }}>
            {user?.profile_picture ? (
              <Avatar.Image
                size={50}
                source={{ uri: user?.profile_picture }}
                style={$profileImage}
              />
            ) : (
              <Avatar.Text
                size={50}
                label={`${capitalizeAllLetters(user?.first_name?.[0]) || ""}${capitalizeAllLetters(user?.last_name?.[0]) || ""}`}
                style={$profileImage}
              />
            )}
          </TouchableOpacity>
        </View>
        <View
          style={{
            flexDirection: "row",
            justifyContent: "space-between",
            alignContent: "center",
          }}
        >
          <TouchableOpacity
            style={[$buttonStyle, { marginRight: 10, backgroundColor: colors.palette.neutral900 }]}
            onPress={() => navigation.navigate("Recevoir" as never)}
          >
            <Icon
              icon={"plus"}
              color={colors.palette.neutral200}
              size={20}
              containerStyle={{ padding: spacing.xs }}
            />
            <Text style={themed($textbtn)}>Recharger</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              $buttonStyle,
              { marginLeft: 10, borderColor: colors.palette.terciary100, borderWidth: 1 },
            ]}
            onPress={() => setEnvoyerState(true)}
          >
            <Text style={[themed($textbtn), { color: colors.palette.neutral800 }]}>Envoyer</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Full-screen Transfer Modal */}
      <Modal visible={envoyerstate} animationType="slide" transparent={false} statusBarTranslucent>
        <SafeAreaView style={$modalContainer}>
          <View style={$modalHeader}>
            <TouchableOpacity style={$backButton} onPress={() => setEnvoyerState(false)}>
              <Icon icon="backicon" size={24} color={colors.palette.neutral800} />
            </TouchableOpacity>
            <Text style={$modalTitle}>Envoyer</Text>
            <View style={$emptySpace} />
          </View>

          <TransferFedhaPochi />
        </SafeAreaView>
      </Modal>

      {/* User Profile Modal */}
      <UserProfileModal
        isVisible={profileModalVisible}
        onClose={() => setProfileModalVisible(false)}
      />
    </>
  )
}

const $container: ViewStyle = {
  justifyContent: "center",
  borderBottomWidth: 1,
  borderBottomColor: colors.palette.neutral300,
  paddingTop: spacing.lg,
  // marginBottom: spacing.md,
  // flex: 1,
}

const $profileImage: ImageStyle = {
  width: 50,
  height: 50,
  justifyContent: "center",
  alignItems: "center",
  borderRadius: 35,
  borderWidth: 1,
  backgroundColor: colors.palette.accent100,
  borderColor: colors.palette.neutral600,
}

const $text: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontFamily: typography.primary.normal,
  fontSize: 13,
  color: colors.palette.terciary100,
})

const $textbtn: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontFamily: typography.primary.normal,
  color: colors.palette.neutral100,
})

const $buttonStyle: ViewStyle = {
  borderRadius: 14,
  marginVertical: 15,
  paddingVertical: 6,
  flex: 1,
  justifyContent: "center",
  alignItems: "center",
  width: "100%",
  flexDirection: "row",
}

// Modal styles
const $modalContainer: ViewStyle = {
  flex: 1,
  backgroundColor: colors.palette.neutral100,
  paddingTop: 30,
}

const $modalHeader: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
  paddingHorizontal: spacing.md,
  paddingVertical: spacing.md,
  borderBottomWidth: 1,
  borderBottomColor: colors.palette.neutral300,
}

const $backButton: ViewStyle = {
  padding: spacing.xs,
}

const $modalTitle: TextStyle = {
  fontSize: 18,
  fontWeight: "bold",
  color: colors.palette.neutral800,
}

const $emptySpace: ViewStyle = {
  width: 24, // Same width as the back button for balanced layout
}

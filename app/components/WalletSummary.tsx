/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable react-native/sort-styles */
/* eslint-disable react-native/no-unused-styles */
/* eslint-disable import/no-unresolved */
import {
  ActivityIndicator,
  FlatList,
  StyleProp,
  TextStyle,
  View,
  ViewStyle,
  StyleSheet,
  TouchableOpacity,
} from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import { colors, spacing, type ThemedStyle } from "@/theme"
import { ActionIcon, PortalView, Text, UpdateLimits } from "."
import { useEffect, useState } from "react"
import React from "react"

export interface WalletSummaryProps {
  walletData: any[]
  style?: StyleProp<ViewStyle>
}

interface WalletData {
  id: string
  name: string
  balance: string
  currency: string
  transactions: {
    id: string
    date: string
    description: string
    amount: string
    type: "credit" | "debit"
  }[]
}

export const WalletSummary = (props: WalletSummaryProps) => {
  const { style, walletData } = props
  const $styles = [$container, style]
  const [activeWallet, setActiveWallet] = useState(walletData[0])
  const [activeTab, setActiveTab] = useState<"Transactions" | "Settings">("Transactions")
  const [isLoading, setIsLoading] = useState(true)
  const [loading, setLoading] = useState(true)

  const [modalState, setModalState] = useState({
    visible: false,
    content: null as React.ReactNode | null,
    iscontent: "",
  })

  const openModal = (title: string, content: React.ReactNode) => {
    setModalState({ visible: true, iscontent: title, content })
  }

  const closeModal = () => {
    setModalState({ visible: false, iscontent: "", content: null })
  }

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false)
      if (walletData.length > 0) {
        setActiveWallet(walletData[0])
      }
    }, 1000)

    return () => clearTimeout(timer)
  }, [walletData])

  const renderTransactionItem = ({ item }: { item: WalletData["transactions"][0] }) => {
    const isCredit = item.type === "credit"
    return (
      <View style={styles.transactionItem}>
        {/* <Text style={styles.transactionDate}>{item.date}</Text>
        <Text style={styles.transactionDescription}>{item.balance}</Text>
        <Text style={[styles.transactionAmount, isCredit ? styles.credit : styles.debit]}>
          {`${isCredit ? "+" : "-"} ${activeWallet?.currency} ${item.amount}`}
        </Text> */}
      </View>
    )
  }

  const renderTransactions = () => {
    if (!activeWallet?.transactions || activeWallet.transactions.length === 0) {
      return (
        <View style={styles.emptyState}>
          <Text style={styles.emptyStateText}>
            Aucune transaction disponible pour votre FedhaPochi
          </Text>
        </View>
      )
    }

    return (
      <FlatList
        data={activeWallet.transactions}
        renderItem={renderTransactionItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.transactionList}
        showsVerticalScrollIndicator={false} // Hide vertical scroll indicator for a cleaner UI
      />
    )
  }

  const renderSettings = () => {
    return (
      <View style={styles.settingsContainer}>
        <ActionIcon
          icon={"updatelimits"}
          title={"Modifier Limites"}
          // onPress={() => console.log("")}
          onPress={() => openModal("Modifier Limites", <UpdateLimits limitData={activeWallet} />)}
        />

        {/* This will come in the version 1.0.1
        <ActionIcon
          icon={"subscription"}
          title={"Mes Abonnements"}
          onPress={() => console.log("")}
        /> */}
        <ActionIcon
          icon={"tempblockcard"}
          title={"Blocage Temporaire"}
          onPress={() => console.log("")}
        />
      </View>
    )
  }

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.palette.primary400} />
        <Text>Loading wallets...</Text>
      </View>
    )
  }

  return (
    <>
      <View>
        <View style={styles.tabBar}>
          <TouchableOpacity
            style={[styles.tab, activeTab === "Transactions" && styles.activeTab]}
            onPress={() => setActiveTab("Transactions")}
          >
            <Text style={styles.tabText}>Transactions</Text>
          </TouchableOpacity>
          {/* <TouchableOpacity
            style={[styles.tab, activeTab === "Settings" && styles.activeTab]}
            onPress={() => setActiveTab("Settings")}
          >
            <Text style={styles.tabText}>Options du compte</Text>
          </TouchableOpacity> */}
        </View>
      </View>

      <View style={$styles}>
        {activeTab === "Transactions" ? renderTransactions() : renderSettings()}
      </View>
      <PortalView
        visible={modalState.visible}
        status={loading}
        icon={"backicon"}
        title={modalState.iscontent}
        onClose={closeModal}
      >
        <View>{modalState.content}</View>
      </PortalView>
    </>
  )
}

const $container: ViewStyle = {
  justifyContent: "center",
}

const $text: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontFamily: typography.primary.normal,
  fontSize: 14,
  color: colors.palette.primary500,
})

const styles = StyleSheet.create({
  activeTab: {
    borderBottomColor: colors.palette.primary400,
    borderBottomWidth: 3,
  },
  activeWalletSelector: {
    // backgroundColor: colors.palette.primary400,
  },
  credit: {
    color: colors.palette.accent400,
  },
  debit: {
    color: colors.palette.primary400,
  },
  emptyState: {
    // flex: 1,
    justifyContent: "center",
    alignItems: "center",
    alignContent: "center",
  },
  emptyStateText: {
    color: colors.palette.neutral900,
    fontSize: 16,
  },
  loadingContainer: {
    alignItems: "center",
    flex: 1,
    justifyContent: "center",
  },
  noDataContainer: {
    alignItems: "center",
    flex: 1,
    justifyContent: "center",
  },
  noDataText: {
    fontSize: 16,
    // color: colors.palette.neutral600,
  },
  settingKey: {
    color: colors.palette.neutral800,
    fontSize: 14,
    fontWeight: "bold",
  },
  settingValue: {
    color: colors.palette.neutral600,
    fontSize: 14,
    marginBottom: spacing.md,
  },
  settingsContainer: {
    // alignContent: "center",
    // position: "relative",
    // alignItems: "center",
    flexWrap: "wrap",
    // backgroundColor: colors.palette.neutral800,
    flexDirection: "row",
    // justifyContent: "space-between",
  },
  tab: {
    alignItems: "center",
    flex: 1,
    justifyContent: "center",
    paddingVertical: spacing.md,
  },
  tabBar: {
    flexDirection: "row",
    // flex: 1,
    // backgroundColor: colors.palette.neutral200,
    borderBottomWidth: 1,
    // paddingVertical: spacing.md,
    borderColor: colors.palette.neutral300,
  },
  tabText: {
    fontSize: 16,
    fontWeight: "bold",
    // color: colors.palette.neutral800,
  },
  transactionAmount: {
    fontSize: 14,
    fontWeight: "bold",
  },
  transactionDate: {
    color: colors.palette.neutral600,
    fontSize: 14,
  },
  transactionDescription: {
    color: colors.palette.neutral800,
    flex: 1,
    fontSize: 14,
    marginLeft: spacing.sm,
  },
  transactionItem: {
    borderBottomColor: colors.palette.neutral300,
    borderBottomWidth: 1,
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: spacing.md,
    paddingBottom: spacing.sm,
  },
  transactionList: {
    padding: spacing.md,
  },
  walletBalance: {
    color: colors.palette.neutral100,
    fontSize: 16,
    marginTop: spacing.xs,
  },
  walletName: {
    color: colors.palette.neutral100,
    fontSize: 18,
    fontWeight: "bold",
  },
  walletSelector: {
    // padding: spacing.sm,
    // marginRight: spacing.md,
    borderRadius: spacing.sm,
    // backgroundColor: colors.palette.neutral200,
  },
  walletSelectorContainer: {
    paddingVertical: spacing.sm,
    // paddingHorizontal: spacing.md,
  },
  walletSelectorText: {
    color: colors.palette.neutral800,
    fontSize: 14,
  },
})

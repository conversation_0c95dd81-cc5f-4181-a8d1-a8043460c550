/* eslint-disable @typescript-eslint/no-unused-vars */
import {
  SectionList,
  StyleProp,
  TextStyle,
  View,
  ViewStyle,
  Modal,
  TouchableOpacity,
  Dimensions,
  ActivityIndicator,
} from "react-native"
import { colors, spacing } from "@/theme"
import { formatDate, formatDateTime } from "@/utils/dateUtils"
import { Text, Icon } from "."
import { useState } from "react"

export interface Transaction {
  id?: string
  balance?: string
  created_at?: string
  credit?: string
  currency?: string | number
  currencyCode?: string
  currencyName?: string
  debit?: string
  fees?: string
  topup?: string | null
  transaction?: string
  wallet?: string
  other_currencies?: Array<any>
  _uniqueId?: string // Added for unique identification in lists
}

export interface TransactionSection {
  title: string
  data: Transaction[]
}

export interface ListScrolProps {
  sections: TransactionSection[]
  onRefresh: () => void
  refreshing: boolean
  style?: StyleProp<ViewStyle>
  onEndReached?: () => void
  hasMoreData?: boolean
}
const { height } = Dimensions.get("window")
const HEADER_HEIGHT = height * 0.6
const TransactionItem = ({ item }: { item: Transaction }) => {
  // Safely parse credit and debit values
  const credit = item.credit ? parseFloat(item.credit) : 0
  const debit = item.debit ? parseFloat(item.debit) : 0
  const isCredit = credit > 0

  // Format date safely using utility function
  const formattedDate = formatDate(item.created_at)
// console.log(item)
  return (
    <View style={$transactionItem}>
      <View style={$transactionLeft}>
        <Text style={$transactionType}>{isCredit ? "Crédit" : "Débit"}</Text>
        <Text style={$transactionDate}>{formattedDate}</Text>
      </View>
      <View style={$transactionRight}>
        <Text
          style={[
            $transactionAmount,
            {
              color: isCredit ?  colors.palette.angry500 : colors.palette.neutral800,
            },
          ]}
        >
          {isCredit ? `${credit.toLocaleString()}` : `${debit.toLocaleString()}`}
        </Text>
        <Text style={$currencyCode}>{item.currency || "FC"}</Text>
      </View>
    </View>
  )
}

const SectionHeader = ({ section }: { section: TransactionSection }) => (
  <View style={$sectionHeader}>
    <Text style={$sectionHeaderText}>{section.title}</Text>
  </View>
)

const TransactionModal = ({
  visible,
  transaction,
  onClose,
}: {
  visible: boolean
  transaction: Transaction | null
  onClose: () => void
}) => {
  if (!transaction) return null

  // Safely parse credit and debit values
  const credit = transaction.credit ? parseFloat(transaction.credit) : 0
  // const debit = transaction.debit ? parseFloat(transaction.debit) : 0 // Unused variable
  const isCredit = credit > 0
  const amount = isCredit ? transaction.credit : transaction.debit

  const formatAmount = (value?: string) => {
    if (!value) return "0.00"
    return parseFloat(value).toLocaleString("fr-FR", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })
  }

  return (
    <Modal visible={visible} transparent animationType="slide" onRequestClose={onClose}>
      <View style={$modalOverlay}>
        <View style={$modalContent}>
          {/* Header */}
          <View style={$modalHeader}>
            <View style={$modalHeaderContent}>
              {/* <Icon
                icon={isCredit ? "increase" : "decrease"}
                size={32}
                color={isCredit ? colors.palette.neutral400 : colors.palette.angry500}
              /> */}
              <View>
                <Text style={$modalTitle}>Détails de la transaction</Text>
                <Text style={$modalSubtitle}>{transaction.transaction}</Text>
              </View>
            </View>
            <TouchableOpacity onPress={onClose} style={$closeButton}>
              <Icon icon="x" size={24} color={colors.palette.neutral800} />
            </TouchableOpacity>
          </View>

          {/* Amount Section */}
          <View style={$amountSection}>
            <Text
              style={[
                $amountText,
                { color: colors.palette.neutral800 },
              ]}
            >
              {/* {isCredit ? "+" : "-"} */}
              {formatAmount(amount)} {transaction.currencyCode || "FC"}
            </Text>
            <Text style={$dateText}>{formatDateTime(transaction.created_at)}</Text>
          </View>

          {/* Details Section */}
          <View style={$detailsSection}>
            <View style={$detailRow}>
              <Text style={$detailLabel}>Type</Text>
              <Text style={$detailValue}>{isCredit ? "Crédit" : "Débit"}</Text>
            </View>

            <View style={$detailRow}>
              <Text style={$detailLabel}>ID Transaction</Text>
              <Text style={$detailValue}>{transaction.transaction}</Text>
            </View>

            <View style={$detailRow}>
              <Text style={$detailLabel}>Wallet ID</Text>
              <Text style={$detailValue}>{transaction.wallet}</Text>
            </View>

            <View style={$detailRow}>
              <Text style={$detailLabel}>Frais</Text>
              <Text style={$detailValue}>
                {transaction.fees} {transaction.currencyCode || "FC"}
              </Text>
            </View>

            <View style={$detailRow}>
              <Text style={$detailLabel}>Solde</Text>
              <Text style={$detailValue}>
                {formatAmount(transaction.balance)} {transaction.currencyCode || "FC"}
              </Text>
            </View>

            <View style={$detailRow}>
              <Text style={$detailLabel}>Devise</Text>
              <Text style={$detailValue}>{transaction.currencyName}</Text>
            </View>

            {transaction.topup && (
              <View style={$detailRow}>
                <Text style={$detailLabel}>Top-up</Text>
                <Text style={$detailValue}>{transaction.topup}</Text>
              </View>
            )}
          </View>

          {/* Footer Actions */}
          <View style={$modalFooter}>
            <TouchableOpacity
              style={$downloadButton}
              onPress={() => {
                /* Add download logic */
              }}
            >
              <Icon icon="reportDown" size={20} color={colors.palette.neutral100} />
              <Text style={$footerButtonText}>Télécharger le reçu</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  )
}

export const ListScrol = (props: ListScrolProps) => {
  const { style, sections, onRefresh, refreshing, onEndReached, hasMoreData } = props
  const $styles = [$container, style]
  const [selectedTransaction, setSelectedTransaction] = useState<Transaction | null>(null)
  const [modalVisible, setModalVisible] = useState(false)

  const handleTransactionPress = (transaction: Transaction) => {
    setSelectedTransaction(transaction)
    setModalVisible(true)
  }
  return (
    <View style={$styles}>
      <SectionList
        sections={sections}
        keyExtractor={(item, index) => {
          // Use the _uniqueId property if it exists (added in PochiScreen)
          if (item._uniqueId) return item._uniqueId

          // Otherwise, create a truly unique key by combining multiple properties
          const baseKey = item.id || item.transaction || ""
          const timestamp = item.created_at ? new Date(item.created_at).getTime() : 0
          // Add index to ensure uniqueness even if all other properties are the same
          return `${baseKey}_${timestamp}_${index}`
        }}
        renderItem={({ item }) => (
          <TouchableOpacity onPress={() => handleTransactionPress(item)}>
            <TransactionItem item={item} />
          </TouchableOpacity>
        )}
        renderSectionHeader={({ section }) => <SectionHeader section={section} />}
        refreshing={refreshing}
        onRefresh={onRefresh}
        contentContainerStyle={$reportList}
        style={$flatListStyle}
        stickySectionHeadersEnabled
        showsVerticalScrollIndicator={false}
        onEndReached={onEndReached}
        onEndReachedThreshold={0.5}
        ListFooterComponent={
          hasMoreData
            ? () => (
                <View style={$loadingMore}>
                  <ActivityIndicator size="small" color={colors.palette.primary500} />
                  <Text style={$loadingMoreText}>Chargement...</Text>
                </View>
              )
            : undefined
        }
        ListEmptyComponent={() => (
          <View style={$emptyState}>
            <Text style={$emptyStateText}>Aucune transaction disponible</Text>
          </View>
        )}
      />

      <TransactionModal
        visible={modalVisible}
        transaction={selectedTransaction}
        onClose={() => {
          setModalVisible(false)
          setSelectedTransaction(null)
        }}
      />
    </View>
  )
}

const $container: ViewStyle = {
  height: HEADER_HEIGHT,
}

const $reportList: ViewStyle = {
  paddingBottom: spacing.xl,
}

const $flatListStyle: ViewStyle = {
  // flex: 1,
}

const $sectionHeader: ViewStyle = {
  backgroundColor: colors.palette.neutral300,
  paddingVertical: spacing.xs,
  borderTopLeftRadius: 5,
  borderTopRightRadius: 5,
  paddingHorizontal: spacing.sm,
}

const $sectionHeaderText: TextStyle = {
  color: colors.palette.neutral900,
  fontSize: 14,
  fontWeight: "600",
}

const $emptyState: ViewStyle = {
  flex: 1,
  alignItems: "center",
  justifyContent: "center",
  paddingVertical: spacing.xl,
}

// const $emptyStateText: TextStyle = {
//   fontSize: 16,
//   color: colors.textDim,
// }

const $transactionItem: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
  padding: spacing.sm,
  bottom: spacing.sm,
  borderBottomWidth: 1,
  borderBottomColor: colors.palette.neutral300,
  backgroundColor: colors.palette.neutral100,
  flex: 1,
}

const $currencyCode: TextStyle = {
  fontSize: 16,
  fontWeight: "bold",
  color: colors.textDim,
  marginHorizontal: 5,
}

const $transactionLeft: ViewStyle = {
  flex: 1,
}

const $transactionRight: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
}

const $transactionType: TextStyle = {
  fontSize: 16,
  fontWeight: "500",
  color: colors.text,
}

const $transactionDate: TextStyle = {
  fontSize: 14,
  color: colors.tintInactive,
  marginTop: spacing.xs,
}

const $transactionAmount: TextStyle = {
  fontSize: 16,
  fontWeight: "bold",
}

const $modalOverlay: ViewStyle = {
  flex: 1,
  backgroundColor: "rgba(0, 0, 0, 0.5)",
  justifyContent: "flex-end",
}

const $modalContent: ViewStyle = {
  backgroundColor: colors.palette.neutral100,
  borderTopLeftRadius: 20,
  borderTopRightRadius: 20,
  padding: spacing.lg,
  maxHeight: "80%",
}

const $modalHeader: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
  marginBottom: spacing.md,
}

const $modalHeaderContent: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  gap: spacing.sm,
}

const $modalTitle: TextStyle = {
  fontSize: 18,
  fontWeight: "600",
  color: colors.palette.neutral800,
}

const $modalSubtitle: TextStyle = {
  fontSize: 14,
  color: colors.palette.neutral500,
}

const $closeButton: ViewStyle = {
  padding: spacing.xs,
}

const $amountSection: ViewStyle = {
  alignItems: "center",
  marginVertical: spacing.lg,
}

const $amountText: TextStyle = {
  fontSize: 24,
  fontWeight: "bold",
}

const $dateText: TextStyle = {
  fontSize: 14,
  color: colors.palette.neutral500,
  marginTop: spacing.xs,
}

const $detailsSection: ViewStyle = {
  marginBottom: spacing.xl,
}

const $detailRow: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
  paddingVertical: spacing.sm,
  borderBottomWidth: 1,
  borderBottomColor: colors.palette.neutral300,
}

const $detailLabel: TextStyle = {
  fontSize: 14,
  color: colors.palette.neutral500,
}

const $detailValue: TextStyle = {
  fontSize: 14,
  color: colors.palette.neutral800,
  fontWeight: "500",
  flex: 1,
  textAlign: "right",
}

const $modalFooter: ViewStyle = {
  marginTop: spacing.md,
  paddingBottom: spacing.md,
}

const $footerButtonText: TextStyle = {
  color: colors.palette.neutral100,
  fontSize: 16,
  fontWeight: "600",
}

const $downloadButton: ViewStyle = {
  backgroundColor: colors.palette.neutral900,
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "center",
  padding: spacing.sm,
  borderRadius: 8,
  gap: spacing.xs,
  marginTop: spacing.sm,
}

// const $emptyState: ViewStyle = {
//   padding: spacing.xl,
//   alignItems: "center",
//   justifyContent: "center",
// }

const $emptyStateText: TextStyle = {
  textAlign: "center",
  color: colors.palette.neutral500,
  marginTop: spacing.sm,
}

const $loadingMore: ViewStyle = {
  padding: spacing.md,
  alignItems: "center",
  justifyContent: "center",
  flexDirection: "row",
}

const $loadingMoreText: TextStyle = {
  marginLeft: spacing.xs,
  color: colors.palette.neutral500,
}

/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable react-native/no-inline-styles */
import { useRef, useState } from "react"
import {
  StyleProp,
  TextInput,
  TextStyle,
  View,
  ViewStyle,
  TextInputProps,
  TouchableOpacity,
} from "react-native"
import { Icon, Text } from "."
import { colors, spacing, typography } from "@/theme"

export interface FencyTextInputProps extends Omit<TextInputProps, "ref"> {
  Fieldname?: any
  inputname?: string
  status?: "error" | "disabled"
  value?: any
  leftonPress?: any
  defaultValue?: any
  maxLength?: number
  onChange?: any
  onFocus?: any
  multiline?: boolean
  numberOfLines?: any
  onBlur?: any
  keyboardType?: any
  isSecret?: boolean
  isFocus?: boolean
  placeholder?: any
  leftStyle?: StyleProp<ViewStyle>
  style?: StyleProp<ViewStyle>
  helper?: any
  LeftIcon?: any
}

/**
 * Describe your component here
 */

export const FencyTextInput = (props: FencyTextInputProps) => {
  const {
    style,
    inputname,
    placeholder,
    defaultValue,
    value,
    isSecret,
    onChange,
    onFocus,
    LeftIcon,
    maxLength,
    status,
    leftonPress,
    leftStyle,
    onBlur,
    keyboardType,
    multiline,
    numberOfLines,
    editable = true,
    ...restProps
  } = props
  const $styles = [$container, style]

  const [isFocused, setIsFocused] = useState(false)

  const handleFocus = () => {
    setIsFocused(true)
    if (onFocus) onFocus()
  }

  const input = useRef<TextInput>(null)

  const borderColor = isFocused ? colors.tint : "#7E958D"
  return (
    <View style={[$styles, { borderColor: status === "error" ? colors.error : borderColor }]}>
      <Text style={$title}>{inputname}</Text>

      <View style={{ flexDirection: "row" }}>
        <TextInput
          {...restProps}
          ref={input}
          defaultValue={defaultValue}
          keyboardType={keyboardType}
          onFocus={handleFocus}
          value={value}
          onChangeText={onChange}
          onBlur={onBlur}
          placeholder={placeholder}
          secureTextEntry={isSecret}
          maxLength={maxLength}
          multiline={multiline}
          numberOfLines={numberOfLines}
          placeholderTextColor="#a1a9b5"
          style={[$Input, multiline && $multilineInput]}
          // clearButtonMode="always"
        />
        {LeftIcon && (
          <TouchableOpacity style={$rightAccessoryStyle} onPress={leftonPress}>
            {!!LeftIcon && (
              <Icon
                size={25}
                icon={LeftIcon}
                color={colors.tint}
                // onPress={leftonPress}
                containerStyle={[$actionIconContainer, leftStyle]}
              />
            )}
          </TouchableOpacity>
        )}
      </View>
    </View>
  )
}

const $container: ViewStyle = {
  justifyContent: "center",
  borderRadius: 15,
  marginTop: spacing.md,
  paddingHorizontal: 15,
  paddingVertical: 9,
  borderWidth: 1.5,
}
const $rightAccessoryStyle: ViewStyle = {
  top: -10,
  justifyContent: "center",
  alignItems: "center",
  padding: 5,
  borderRadius: 20,
  backgroundColor: colors.palette.neutral300,
}

const $multilineInput: TextStyle = {
  height: "auto",
  minHeight: 90,
  textAlignVertical: "top",
}

const $title: TextStyle = {
  fontFamily: typography.primary.normal,
  fontSize: 16,
  color: "#333",
  fontWeight: "600",
}

const $actionIconContainer: ViewStyle = {
  flexGrow: 0,
  alignItems: "center",
  justifyContent: "center",
  // height: "100%",
  zIndex: 2,
}

const $Input: TextStyle = {
  fontSize: 17,
  color: "#333",
  width: "90%",
  height: 40,
  fontWeight: "400",
}

/* eslint-disable import/no-unresolved */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable react-native/no-inline-styles */
import {
  StyleProp,
  TextStyle,
  View,
  ViewStyle,
  Modal,
  StatusBar,
  Dimensions,
  SafeAreaView,
} from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import { colors, spacing } from "@/theme"
import { Icon, Text, Screen } from "."

export interface PortalViewProps {
  children: any
  visible: boolean
  status?: boolean
  icon?: string | any
  title?: string
  onClose: any
  style?: StyleProp<ViewStyle>
}

const { width: widthScreen, height: heightScreen } = Dimensions.get("screen")

export const PortalView = (props: PortalViewProps) => {
  const { style, children, visible, title, onClose, icon = "backicon" } = props
  const $styles = [$container, style]
  const { themed } = useAppTheme()

  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={visible}
      onRequestClose={onClose}
      style={$styles}
    >
      <SafeAreaView style={$safeArea}>
        <View style={$header}>
          <View style={$headerLeft}>
            <Icon
              size={24}
              icon={icon}
              color={colors.palette.neutral800}
              containerStyle={$iconContainer}
              onPress={onClose}
            />
          </View>
          {title && (
            <View style={$headerCenter}>
              <Text preset="subheading" style={$title}>
                {title}
              </Text>
            </View>
          )}
          <View style={$headerRight} />
        </View>
        <View style={$contentContainer}>
          {children}
        </View>
      </SafeAreaView>
    </Modal>
  )
}

const $safeArea: ViewStyle = {
  flex: 1,
  backgroundColor: colors.palette.neutral100,
}

const $container: ViewStyle = {
  flex: 1,
  paddingHorizontal: spacing.lg,
}

const $header: ViewStyle = {
  flexDirection: "row",
  height: 56,
  alignItems: "center",
  borderBottomWidth: 1,
  borderBottomColor: colors.palette.neutral200,
  backgroundColor: colors.palette.neutral100,
  zIndex: 10,
  elevation: 3,
  position: "relative",
}

const $headerLeft: ViewStyle = {
  width: 56,
  justifyContent: "center",
  zIndex: 11,
}

const $headerCenter: ViewStyle = {
  flex: 1,
  justifyContent: "center",
  alignItems: "center",
  zIndex: 11,
}

const $headerRight: ViewStyle = {
  width: 56,
  zIndex: 11,
}

const $iconContainer: ViewStyle = {
  height: 56,
  width: 56,
  justifyContent: "center",
  alignItems: "center",
}

const $title: TextStyle = {
  fontSize: 16,
  fontWeight: "600",
  color: colors.palette.neutral800,
}

const $contentContainer: ViewStyle = {
  flex: 1,
  backgroundColor: colors.palette.neutral100,
  zIndex: 1,
}

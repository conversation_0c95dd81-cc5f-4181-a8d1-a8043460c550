import { StyleProp, View, ViewStyle, StyleSheet, Dimensions } from "react-native"
import { colors } from "@/theme"
import Svg, { Path } from "react-native-svg"

export interface CurvedHeaderBackgroundProps {
  height: number
  curveDepth?: number
  style?: StyleProp<ViewStyle>
  backgroundColor?: string
}

const screenWidth = Dimensions.get("window").width

export const CurvedHeaderBackground = (props: CurvedHeaderBackgroundProps) => {
  const { style, height, curveDepth = 40, backgroundColor = colors.palette.neutral100 } = props

  // Calculate the visible height before the curve starts dipping
  const visibleHeaderHeight = height - curveDepth / 2

  // Generate the SVG path for the curve
  const pathData = `
    M 0 0
    L ${screenWidth} 0
    L ${screenWidth} ${visibleHeaderHeight}
    Q ${screenWidth / 2} ${visibleHeaderHeight + curveDepth}, 0 ${visibleHeaderHeight}
    L 0 0
    Z
  `

  return (
    <View style={[StyleSheet.absoluteFill, { height }, style]}>
      <Svg width={screenWidth} height={height} viewBox={`0 0 ${screenWidth} ${height}`}>
        <Path d={pathData} fill={backgroundColor} />
      </Svg>
    </View>
  )
}

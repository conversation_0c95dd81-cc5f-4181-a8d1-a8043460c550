import React, { forwardRef, useImperativeHandle, useRef, useState } from "react"
import { StyleProp, ViewStyle } from "react-native"
import { Scanner } from "./Features/FedhaVision/Scanner"
import { Camera } from "react-native-vision-camera"

export interface CustomScannerProps {
  visible: boolean
  onClose: () => void
  onscan: (data: any) => void
  style?: StyleProp<ViewStyle>
}

export interface CustomScannerRef {
  toggleFlash: () => void
  getFlashState: () => boolean
}

export const CustomScanner = forwardRef<CustomScannerRef, CustomScannerProps>((props, ref) => {
  const { visible, onClose, onscan, style } = props
  const [flashEnabled, setFlashEnabled] = useState(false)
  const cameraRef = useRef<Camera>(null)

  // Expose methods to parent component
  useImperativeHandle(ref, () => ({
    toggleFlash: () => {
      setFlashEnabled((prev) => !prev)
      return !flashEnabled
    },
    getFlashState: () => flashEnabled,
  }))

  return (
    <Scanner
      visible={visible}
      onClose={onClose}
      onscan={onscan}
      style={style}
      // The Scanner component internally manages flash state
      // We're just wrapping it to expose the flash control
    />
  )
})

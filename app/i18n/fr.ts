import { Translations } from "./en"

const fr: Translations = {
  common: {
    ok: "OK !",
    cancel: "Annuler",
    back: "Retour",
  },
  welcomeScreen: {
    bienvenuMSG: "Bienvenue à Fedha",
    readyForLaunch: "Votre application, presque prête pour le lancement !",
    exciting: "(ohh, c'est excitant !)",
  },
  errorScreen: {
    title: "Quelque chose s'est mal passé !",
    friendlySubtitle:
      "C'est l'écran que vos utilisateurs verront en production lorsqu'une erreur sera lancée. Vous voudrez personnaliser ce message (situé dans `app/i18n/fr.ts`) et probablement aussi la mise en page (`app/screens/ErrorScreen`). Si vous voulez le supprimer complètement, vérifiez `app/app.tsx` pour le composant <ErrorBoundary>.",
    reset: "RÉINITIALISER L'APPLICATION",
  },
  tabNavigator: {
    componentsTab: "Components",
    debugTab: "Debug",
    communityTab: "Community",
    podcastListTab: "Podcast",
    history: "Activité",
    home: "Accueil",
    wallets: "Pochi",
    more: "Menu",
  },
  BusintabNavigator: {
    componentsTab: "Components",
    debugTab: "Debug",
    payme: "Me Payé",
    podcastListTab: "Podcast",
    history: "Activité",
    home: "Vendez",
    mystore: "Boutique",
    more: "Menu",
  },
  emptyStateComponent: {
    generic: {
      heading: "Si vide... si triste",
      content:
        "Aucune donnée trouvée pour le moment. Essayez de cliquer sur le bouton pour rafraîchir ou recharger l'application.",
      button: "Essayons à nouveau",
    },
  },
  historicScreen: {
    emptyState: {
      heading: "Aucune Transaction",
      content:
        "Commencez votre parcours avec Fedha en effectuant votre première transaction. Envoyez de l'argent, payez vos factures ou rechargez votre compte.",
      button: "Faire une Transaction",
    },
  },
}

export default fr

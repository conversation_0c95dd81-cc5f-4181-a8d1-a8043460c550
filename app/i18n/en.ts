const en = {
  common: {
    ok: "OK!",
    cancel: "Cancel",
    back: "Back",
  },
  welcomeScreen: {
    bienvenuMSG: "Welcome to Fed<PERSON>",
    readyForLaunch: "Your app, almost ready for launch!",
    exciting: "(ohh, this is exciting!)",
  },
  errorScreen: {
    title: "Une erreur semble s’être produite!",
    friendlySubtitle:
      "Veuillez réinitialiser l’application. Si l’erreur persiste, veuillez nous contacter sur WhatsApp pour un support rapide.",
    reset: "Réinitialiser l’application",
  },
  tabNavigator: {
    componentsTab: "Components",
    debugTab: "Debug",
    communityTab: "Community",
    podcastListTab: "Podcast",
    home: "Accueil",
    wallets: "Pochi",
    history: "Activité",
    more: "Menu",
  },
  BusintabNavigator: {
    componentsTab: "Components",
    debugTab: "Debug",
    payme: "Me Payé",
    podcastListTab: "Podcast",
    history: "Activité",
    home: "Vendez",
    mystore: "Boutique",
    more: "Menu",
  },
  emptyStateComponent: {
    generic: {
      heading: "So empty... so sad",
      content: "No data found yet. Try clicking the button to refresh or reload the app.",
      button: "Let's try this again",
    },
  },
  historicScreen: {
    emptyState: {
      heading: "No Transactions Yet",
      content:
        "Start your journey with Fedha by making your first transaction. Send money, pay bills, or top up your account.",
      button: "Make a Transaction",
    },
  },
}

export default en
export type Translations = typeof en

/* eslint-disable import/no-unresolved */
import { create } from "zustand"
import { persist, createJSONStorage } from "zustand/middleware"
import { storage } from "@/utils/storage"
import axiosInstance from "@/config/axiosInstance"
import { CartItem } from "./CartStore"
import { Invoice } from "@/services/api/api.types"

interface InvoiceState {
  invoices: Invoice[]
  businessName: string
  loading: boolean
  error: string | null
  createInvoice: (
    businessId: string,
    cartItems: CartItem[],
    paymentMethod?: "scan" | "cash" | "share" | "bank" | "other",
  ) => Promise<{ success: boolean; data?: any; message?: string }>
  fetchInvoice: (businessId: string) => Promise<{ success: boolean; data?: any; message?: string }>
}

export const useInvoiceStore = create<InvoiceState>()(
  persist(
    (set, get) => ({
      invoices: [],
      currentInvoice: null,
      businessName: "",
      loading: false,
      error: null,

      // Create a new invoice
      createInvoice: async (
        businessId: string,
        cartItems: CartItem[],
        paymentMethod: "scan" | "cash" | "share" | "bank" | "other" = "scan",
      ) => {
        try {
          // Validate inputs
          if (!businessId) {
            throw new Error("Business ID is required")
          }

          if (!cartItems || cartItems.length === 0) {
            throw new Error("Cart items are required")
          }

          set({ loading: true, error: null })

          // Transform cart items to invoice items
          const invoiceItems = cartItems.map((item) => {
            // Ensure currency is in the correct format (API might expect "CDF" instead of "FC")
            let currency = item.currency
            if (currency === "FC") {
              currency = "CDF"
            }

            return {
              product: item.name,
              description: item.description || "",
              qt: item.quantity,
              amount: item.price,
              currency: currency,
            }
          })

          // Create the invoice data
          const invoiceData = {
            business: businessId,
            payment_method: paymentMethod.toLowerCase(), // Ensure lowercase for consistency
            client: null, // Use null instead of empty string for client
            items: invoiceItems,
          }

          console.log("Creating invoice with data:", invoiceData)

          // Make the API request with proper content type
          const response = await axiosInstance.post("/pay/generate/invoice/", invoiceData, {
            headers: {
              "Content-Type": "application/json",
            },
          })

          console.log("Invoice created:", response.data)

          // Update the store with the new invoice (add it at the beginning of the array)
          if (response.data) {
            const newInvoice = response.data.data ? response.data.data : response.data
            console.log("Adding new invoice to the beginning of the list:", newInvoice)

            set({
              invoices: [newInvoice, ...get().invoices],
              loading: false,
            })

            return { success: true, data: newInvoice }
          }

          set({ loading: false })
          return { success: true, data: response.data }
        } catch (error: any) {
          console.error("Error creating invoice:", error)

          // Log more detailed error information
          if (error.response) {
            console.error("Error response data:", error.response.data)
            console.error("Error response status:", error.response.status)
            console.error("Error response headers:", error.response.headers)
          }

          // Extract error message from response
          let errorMessage = "Failed to create invoice"

          if (error.response?.data) {
            // Check different possible error message formats
            if (typeof error.response.data === "string") {
              errorMessage = error.response.data
            } else if (error.response.data.message) {
              errorMessage = error.response.data.message
            } else if (error.response.data.error) {
              errorMessage = error.response.data.error
            } else if (error.response.data.detail) {
              errorMessage = error.response.data.detail
            }
          } else if (error.message) {
            errorMessage = error.message
          }

          set({
            loading: false,
            error: errorMessage,
          })

          return {
            success: false,
            message: errorMessage,
          }
        }
      },

      // Fetch invoices by business ID
      fetchInvoice: async (businessId) => {
        try {
          set({ loading: true, error: null })

          // Add a delay to ensure loading state is visible
          await new Promise((resolve) => setTimeout(resolve, 1500))

          // Make the API request
          const response = await axiosInstance.get(`/business/invoice/${businessId}/cart/all/`)

          // console.log("Invoices fetched:", response.data)

          // Process the response data
          if (response.data) {
            // Check if it has the new structure with business_name and result array
            if (
              response.data.business_name &&
              response.data.result &&
              Array.isArray(response.data.result)
            ) {
              console.log("Found business invoices with result array:", response.data.result.length)

              // Sort invoices by date (newest first) before updating the store
              let invoices = response.data.result[0] || []
              if (Array.isArray(invoices)) {
                invoices = invoices.sort((a, b) => {
                  // Convert dates to timestamps for comparison
                  const dateA = a.created_at ? new Date(a.created_at).getTime() : 0
                  const dateB = b.created_at ? new Date(b.created_at).getTime() : 0

                  // Sort in descending order (newest first)
                  return dateB - dateA
                })
              }

              // Update the store with the sorted invoices array and business name
              set({
                invoices: invoices,
                businessName: response.data.business_name || "",
                loading: false,
              })

              return { success: true, data: response.data.result }
            }
            // Check if it's a single invoice object with a data property
            else if (response.data.data && !Array.isArray(response.data)) {
              // It's a single invoice in the data property
              const invoice = response.data.data.result
              // console.log("Single invoice found:", invoice)

              // Sort invoices by date (newest first) if it's an array
              let sortedInvoice = invoice
              if (Array.isArray(sortedInvoice)) {
                sortedInvoice = sortedInvoice.sort((a, b) => {
                  const dateA = a.created_at ? new Date(a.created_at).getTime() : 0
                  const dateB = b.created_at ? new Date(b.created_at).getTime() : 0
                  return dateB - dateA
                })
              }

              // Update the store with the sorted invoice array
              set({
                invoices: sortedInvoice,
                loading: false,
              })

              return { success: true, data: [invoice] }
            }
            // Check if it's already an array
            else if (Array.isArray(response.data)) {
              // It's an array of invoices - sort by date (newest first)
              let sortedInvoices = response.data[0] || []
              if (Array.isArray(sortedInvoices)) {
                sortedInvoices = sortedInvoices.sort((a, b) => {
                  const dateA = a.created_at ? new Date(a.created_at).getTime() : 0
                  const dateB = b.created_at ? new Date(b.created_at).getTime() : 0
                  return dateB - dateA
                })
              }

              set({
                invoices: sortedInvoices,
                loading: false,
              })

              return { success: true, data: response.data }
            }
            // If it's a single invoice object without a data property
            else if (
              typeof response.data === "object" &&
              (response.data.id || response.data.invoice_no)
            ) {
              // Single invoice - just put it in an array
              set({
                invoices: [response.data],
                loading: false,
              })

              return { success: true, data: [response.data] }
            }
            // Unknown format but still has data
            else {
              console.log("Unknown invoice data format:", response.data)
              set({
                invoices: [],
                loading: false,
                error: "Invalid data format received from server",
              })

              return { success: false, message: "Invalid data format received from server" }
            }
          }

          // No data in response
          set({
            loading: false,
            invoices: [],
            error: "No invoice data received",
          })

          return { success: false, message: "No invoice data received" }
        } catch (error: any) {
          console.error("Error fetching invoices:", error)

          set({
            loading: false,
            error: error.response?.data?.message || error.message || "Failed to fetch invoices",
          })

          return {
            success: false,
            message: error.response?.data?.message || error.message || "Failed to fetch invoices",
          }
        }
      },
    }),
    {
      name: "invoice-store",
      storage: createJSONStorage(() => ({
        getItem: (key) => {
          const value = storage.getString(key)
          return value ? JSON.parse(value) : null
        },
        setItem: (key, value) => storage.set(key, JSON.stringify(value)),
        removeItem: (key) => storage.delete(key),
      })),
    },
  ),
)

/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable import/no-unresolved */
import { create } from "zustand"
import { persist, createJSONStorage } from "zustand/middleware"
// import * as z from "zod"
import axiosInstance from "@/config/axiosInstance"
import { PochiWallet, walletSchema } from "@/services/api"
import { storage } from "@/utils/storage"
import { useAppSettings } from "./AppSettingsStore"

// Define the transaction type based on the API response
type Transaction = {
  id: string
  transaction: string
  credit: string
  debit: string
  fees: string
  balance: string
  currency: string
  created_at: string
  wallet: string
  topup: any
  other_currencies: any[]
  // Added fields after transformation
  currencyCode?: string
  currencyName?: string
}

// Define the pagination info type
type PaginationInfo = {
  count: number
  next: string | null
  previous: string | null
  total_credited: number
  total_debited: number
  weekly_total_credited: number
  weekly_total_debited: number
  other_currencies_total: Array<{
    code: string
    total_credit: number
    total_debit: number
    weekly_total_credited: number
    weekly_total_debited: number
  }>
}

type WalletStore = {
  wallet: PochiWallet | null
  loading: boolean
  error: string | null
  currentBalance: number
  availableCurrencies: string[]
  otherCurrencies: {
    currency: string
    name: string
    amount: number
  }[]
  fetchWallet: () => Promise<void>
  updateCurrentBalance: (currency: string) => void
  updateAvailableCurrencies: () => void

  // Updated report types
  pochiReport: {
    transactions: Transaction[]
    paginationInfo: PaginationInfo | null
  }
  reportLoading: boolean
  reportError: string | null
  fetchPochiReport: (page?: number) => Promise<void>
  fetchNextPage: () => Promise<void>
  hasMorePages: () => boolean
  currentPage: number

  reportOverview: {
    totalIn: number
    totalOut: number
    netBalance: number
  }
  calculateReportOverview: () => void
}

export const useWalletStore = create<WalletStore>()(
  persist(
    (set, get) => ({
      wallet: null,
      loading: false,
      error: null,
      currentBalance: 0,
      availableCurrencies: [],
      otherCurrencies: [],
      pochiReport: {
        transactions: [],
        paginationInfo: null,
      },
      reportLoading: false,
      reportError: null,
      currentPage: 1,
      reportOverview: {
        totalIn: 0,
        totalOut: 0,
        netBalance: 0,
      },

      updateCurrentBalance: (currency: string) => {
        const { wallet, otherCurrencies } = get()
        if (!wallet) return

        if (currency === "FC" || currency === "Congolese Franc") {
          set({ currentBalance: wallet.balance })
        } else {
          const otherCurrency = otherCurrencies.find(
            (curr) => curr.currency === currency || curr.name === currency,
          )
          if (otherCurrency) {
            set({ currentBalance: otherCurrency.amount })
          } else {
            // If no matching currency found, keep the FC balance
            set({ currentBalance: wallet.balance })
          }
        }
      },

      updateAvailableCurrencies: () => {
        const appSettings = useAppSettings.getState()
        const activeCurrencies = appSettings.currencies
          .filter((curr) => curr.is_active)
          .map((curr) => (curr.code === "CDF" ? "FC" : curr.code))

        set({ availableCurrencies: activeCurrencies })
      },

      async fetchWallet() {
        try {
          set({ loading: true, error: null })
          const { data } = await axiosInstance.get("/wallet/my-pochi/")

          // Validate response data
          const validatedWallet = walletSchema.parse(data)

          // Update available currencies when fetching wallet
          get().updateAvailableCurrencies()

          set({
            wallet: validatedWallet.pochi,
            loading: false,
            currentBalance: validatedWallet.pochi.balance || 0,
            otherCurrencies: validatedWallet.pochi.other_currencies || [],
          })
        } catch (error: any) {
          set({
            loading: false,
            error: error.message,
            wallet: null,
            currentBalance: 0,
          })
          console.error("Failed to fetch wallet:", error)
        }
      },

      calculateReportOverview: () => {
        const { pochiReport } = get()
        const { currency } = useAppSettings.getState()

        if (!pochiReport?.paginationInfo) return

        let totalIn = 0
        let totalOut = 0

        // Check if we should use the default FC currency or another currency
        if (currency === "FC") {
          // Use the main weekly totals for FC
          totalIn = pochiReport.paginationInfo.weekly_total_credited || 0
          totalOut = pochiReport.paginationInfo.weekly_total_debited || 0
        } else {
          // For other currencies (like USD), find the matching currency in other_currencies_total
          const currencyData = pochiReport.paginationInfo.other_currencies_total?.find(
            (curr) => curr.code === currency,
          )

          if (currencyData) {
            totalIn = currencyData.weekly_total_credited || 0
            totalOut = currencyData.weekly_total_debited || 0
          }
        }

        // Calculate net balance
        const netBalance = totalIn - totalOut

        set({
          reportOverview: {
            totalIn,
            totalOut,
            netBalance,
          },
        })
      },

      // Check if there are more pages to fetch
      hasMorePages: () => {
        const { pochiReport } = get()
        return !!pochiReport.paginationInfo?.next
      },

      // Fetch the next page of transactions
      fetchNextPage: async () => {
        const { pochiReport, currentPage } = get()
        if (!pochiReport.paginationInfo?.next) return

        // Fetch the next page
        await get().fetchPochiReport(currentPage + 1)
      },

      async fetchPochiReport(page: number = 1) {
        try {
          // Set loading state
          set({ reportLoading: true, reportError: null })

          // Update current page
          set({ currentPage: page })

          // Construct URL with pagination
          const url = `/bank/pochi/activities/?page=${page}`

          // Fetch data
          const response = await axiosInstance.get(url)
          // console.log("Fetching pochi report page", page, response.data)

          if (response.data) {
            const appSettings = useAppSettings.getState()

            // Extract pagination info
            const paginationInfo: PaginationInfo = {
              count: response.data.count,
              next: response.data.next,
              previous: response.data.previous,
              total_credited: response.data.total_credited || 0,
              total_debited: response.data.total_debited || 0,
              weekly_total_credited: response.data.weekly_total_credited || 0,
              weekly_total_debited: response.data.weekly_total_debited || 0,
              other_currencies_total: response.data.other_currencies_total || [],
            }

            // Transform the results to include currency code
            const transformedTransactions = response.data.results.map((item: any) => {
              const currencyDetails = appSettings.getCurrencyById(item.currency)
              return {
                ...item,
                currencyCode: currencyDetails
                  ? appSettings.getStandardizedCurrency(currencyDetails.code)
                  : "FC",
                currencyName: currencyDetails?.name || "Franc Congolais",
              }
            })

            // If this is the first page, replace all data
            // If this is a subsequent page, append to existing data
            if (page === 1) {
              set({
                pochiReport: {
                  transactions: transformedTransactions,
                  paginationInfo,
                },
                reportLoading: false,
              })
            } else {
              // Append to existing transactions
              const { pochiReport } = get()
              set({
                pochiReport: {
                  transactions: [...pochiReport.transactions, ...transformedTransactions],
                  paginationInfo,
                },
                reportLoading: false,
              })
            }

            // Calculate overview after setting the report data
            get().calculateReportOverview()

            // Log the weekly totals for debugging
            if (__DEV__) {
              console.log("Weekly totals (FC):", {
                credited: paginationInfo.weekly_total_credited,
                debited: paginationInfo.weekly_total_debited,
              })

              if (paginationInfo.other_currencies_total?.length > 0) {
                console.log(
                  "Other currencies weekly totals:",
                  paginationInfo.other_currencies_total,
                )
              }
            }
          }
        } catch (error: any) {
          // More detailed error handling
          const errorMessage =
            error.response?.status === 404
              ? "Le rapport Pochi n'est pas disponible pour le moment"
              : error.response?.data?.message || "Impossible de récupérer le rapport Pochi"

          console.error("Failed to fetch Pochi report:", {
            status: error.response?.status,
            message: errorMessage,
            error,
          })

          // If this is the first page, set an empty report
          // If this is a subsequent page, keep the existing data
          if (page === 1) {
            set({
              reportLoading: false,
              reportError: errorMessage,
              pochiReport: {
                transactions: [],
                paginationInfo: null,
              },
            })
          } else {
            set({
              reportLoading: false,
              reportError: errorMessage,
            })
          }
        }
      },
    }),
    {
      name: "wallet-store",
      storage: createJSONStorage(() => ({
        getItem: (key) => {
          const value = storage.getString(key)
          return value ? JSON.parse(value) : null
        },
        setItem: (key, value) => storage.set(key, JSON.stringify(value)),
        removeItem: (key) => storage.delete(key),
      })),
    },
  ),
)

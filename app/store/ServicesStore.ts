/* eslint-disable import/no-unresolved */
import { create } from "zustand"
import { persist, createJSONStorage } from "zustand/middleware"
import { storage } from "@/utils/storage"
import { getSNELHistory } from "@/services/api"

export interface SNELRequest {
  amount: string
  name: string
  created_at: string
  currency: {
    code: string
    name: string
    symbol: string
  }
  id: string
  note: string
  response: string | null
  saved_meter: string
  status: "pending" | "completed" | "failed"
  updated_at: string | null
  updated_by: string | null
}

interface SNELMeterData {
  meter: string
  name: string
  paid_wallet: string
  requests: SNELRequest[]
}

interface ServicesState {
  snelHistory: SNELMeterData[]
  isLoading: boolean
  error: string | null
  lastUpdated: Date | null

  // Core actions
  fetchSNELHistory: () => Promise<void>
  refreshSNELHistory: () => Promise<void>

  // Helper methods for data access
  getLatestRequest: () => SNELRequest | null
  getRequestsByStatus: (status: SNELRequest["status"]) => SNELRequest[]
  getRequestsByMeter: (meterNumber: string) => SNELRequest[]
  getMeterDataByRequest: (request: SNELRequest) => SNELMeterData | null
  getMeterByNumber: (meterNumber: string) => SNELMeterData | null
  getAllMeters: () => string[]
  getUniqueMeterData: () => SNELMeterData[]
}

export const useServicesStore = create<ServicesState>()(
  persist(
    (set, get) => ({
      snelHistory: [], // Initialize as empty array
      isLoading: false,
      error: null,
      lastUpdated: null,

      fetchSNELHistory: async () => {
        set({ isLoading: true, error: null })
        try {
          const response = await getSNELHistory()
          if (response.success && Array.isArray(response.data)) {
            // Ensure each meter data has a requests array
            const normalizedData = response.data.map((meter: SNELMeterData) => ({
              ...meter,
              requests: Array.isArray(meter.requests) ? meter.requests : [],
            }))

            set({
              snelHistory: normalizedData,
              lastUpdated: new Date(),
              isLoading: false,
            })
          } else {
            throw new Error("Invalid data format received")
          }
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : "Failed to fetch SNEL history",
            isLoading: false,
            snelHistory: [], // Reset to empty array on error
          })
        }
      },

      refreshSNELHistory: async () => {
        await get().fetchSNELHistory()
      },

      getLatestRequest: () => {
        const allRequests = get()
          .snelHistory.flatMap((meter) => meter.requests)
          .filter(Boolean)

        return allRequests.length > 0
          ? [...allRequests].sort(
              (a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime(),
            )[0]
          : null
      },

      getRequestsByStatus: (status) => {
        return get()
          .snelHistory.flatMap((meter) => meter.requests)
          .filter((request) => request.status === status)
          .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
      },

      getRequestsByMeter: (meterNumber) => {
        const meterData = get().getMeterByNumber(meterNumber)
        return meterData?.requests || []
      },

      getMeterDataByRequest: (request) => {
        return (
          get().snelHistory.find((meter) => meter.requests.some((r) => r.id === request.id)) || null
        )
      },

      getMeterByNumber: (meterNumber) => {
        return get().snelHistory.find((meter) => meter.meter === meterNumber) || null
      },

      getAllMeters: () => {
        return get().snelHistory.map((meter) => meter.meter)
      },

      getUniqueMeterData: () => {
        const meters = new Map<string, SNELMeterData>()
        get().snelHistory.forEach((meter) => {
          if (!meters.has(meter.meter)) {
            meters.set(meter.meter, meter)
          }
        })
        return Array.from(meters.values())
      },
    }),
    {
      name: "services-store",
      storage: createJSONStorage(() => ({
        getItem: (key) => {
          const value = storage.getString(key)
          return value ? JSON.parse(value) : null
        },
        setItem: (key, value) => storage.set(key, JSON.stringify(value)),
        removeItem: (key) => storage.delete(key),
      })),
    },
  ),
)

/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable import/no-unresolved */
import { create } from "zustand"
import { persist, createJSONStorage } from "zustand/middleware"
import { storage } from "@/utils/storage"
import { SERVICE_Partner } from "@/services/api"
import axiosInstance from "@/config/axiosInstance"

interface ServicesState {
  Services: SERVICE_Partner[]
  fectchServices: () => Promise<void>
  isLoading: boolean
  error: string | null
  // lastUpdated: Date | null
  // // Core actions
  // fetchSNELHistory: () => Promise<void>
  // refreshSNELHistory: () => Promise<void>
  // // Helper methods for data access
  // getLatestRequest: () => SNELRequest | null
  // getRequestsByStatus: (status: SNELRequest["status"]) => SNELRequest[]
  // getRequestsByMeter: (meterNumber: string) => SNELRequest[]
  // getMeterDataByRequest: (request: SNELRequest) => SNELMeterData | null
  // getMeterByNumber: (meterNumber: string) => SNELMeterData | null
  // getAllMeters: () => string[]
  // getUniqueMeterData: () => SNELMeterData[]
}

export const useServicesStore = create<ServicesState>()(
  persist(
    (set, get) => ({
      Services: [],
      isLoading: false,
      error: null,
      lastUpdated: null,

      async fectchServices() {
        try {
          set({ isLoading: true, error: null })
          const { data } = await axiosInstance.get("/service/partners/")

          set({
            Services: data?.data,
            isLoading: false,
            error: null,
          })
        } catch (error: any) {
          set({
            isLoading: false,
            error: error.message,
          })
          console.error("Failed to fetch service_partner:", error)
        }
      },
    }),
    {
      name: "services-store",
      storage: createJSONStorage(() => ({
        getItem: (key) => {
          const value = storage.getString(key)
          return value ? JSON.parse(value) : null
        },
        setItem: (key, value) => storage.set(key, JSON.stringify(value)),
        removeItem: (key) => storage.delete(key),
      })),
    },
  ),
)

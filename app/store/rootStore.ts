import { useAppSettings } from "./AppSettingsStore"
import { useAuthStore } from "./AuthenticationStore"
import { useWalletStore } from "./FedhaPochiStore"
import { useServicesStore } from "./ServicesStore"
import { useTransactionStore } from "./TransactionStore"
import { useBeneficiariesStore } from "./BeneficiariesStore"
import { useCartStore } from "./CartStore"
import { useBusinessStore } from "./BusinessStore"
import { useBusinessTransactionStore } from "./BusinessTransactionStore"
import { useInvoiceStore } from "./InvoiceStore"
import { useStaffStore } from "./StaffStore"
import { usePushNotificationStore } from "./NotificationStore"
import { useElimuStore } from "./ElimuStore"

export const useStores = () => ({
  auth: useAuthStore(),
  mycart: useCartStore(),
  invoices: useInvoiceStore(),
  fedhapochi: useWalletStore(),
  appsettings: useAppSettings(),
  transactions: useTransactionStore(),
  services: useServicesStore(),
  beneficiary: useBeneficiariesStore(),
  business: useBusinessStore(),
  businessTransactions: useBusinessTransactionStore(),
  invoice: useInvoiceStore(),
  elimu: useElimuStore(),
  staff: useStaffStore(),
  notif: usePushNotificationStore(),
})

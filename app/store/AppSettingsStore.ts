/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable import/no-unresolved */
import { create } from "zustand"
import { persist, createJSONStorage } from "zustand/middleware"
import { storage } from "@/utils/storage"
import { useWalletStore } from "./FedhaPochiStore"
import { getCurrencies, getFAQData } from "@/services/api"
import axiosInstance from "@/config/axiosInstance"

type Currency = "FC" | "USD"

interface CurrencyDetails {
  id: number
  code: string
  name: string
  exchange_rate: string
  is_active: boolean
  updated_at: string | null
  updated_by: string | null
}

// Define FAQ data interface
interface FAQItem {
  id: string
  question: string
  answer: string
  category: string
}

// Define Fedha business account details interface

interface AppSettings {
  walletId: string
  currency: Currency
  defaultCurrency: Currency
  currencies: CurrencyDetails[] // Make sure this is initialized
  isLoadingCurrencies: boolean
  error: string | null
  lastCurrencyFetch: number | null // Timestamp of last successful fetch
  isFetchingCurrencies: boolean // Flag to prevent concurrent fetches
  fedhaBusinessAccount: [] | null
  isFetchingFedhaDetails: boolean

  exchangeRates: { [key: string]: number }
  convertAmount: (amount: number, fromCurrency: Currency, toCurrency: Currency) => number
  getExchangeRate: (fromCurrency: Currency, toCurrency: Currency) => number

  setWalletId: (id: string) => void
  setCurrency: (currency: Currency) => void
  setDefaultCurrency: (currency: Currency) => void
  resetCurrency: () => void
  fetchCurrencies: () => Promise<void>
  fetchFedhaDetails: () => Promise<void>
  getCurrencyById: (id: number) => CurrencyDetails | undefined
  getCurrencyByCode: (code: string) => CurrencyDetails | undefined
  getCurrencyByName: (name: string) => Currency // Add this new method
  getServerCurrencyCode: (displayCurrency: Currency) => string
  getStandardizedCurrency: (currencyCode: string) => Currency
  fetchFAQacontent: FAQItem[]
  isFetchingFAQ: boolean
  faqError: string | null
  fetchFAQData: () => Promise<void>
}

export const useAppSettings = create<AppSettings>()(
  persist(
    (set, get) => ({
      // Initialize with default values
      walletId: "",
      currency: "FC",
      defaultCurrency: "FC",
      currencies: [], // Initialize with empty array
      isLoadingCurrencies: false,
      error: null,
      lastCurrencyFetch: null,
      isFetchingCurrencies: false,
      fedhaBusinessAccount: null,
      isFetchingFedhaDetails: false,
      fetchFAQacontent: [],
      isFetchingFAQ: false,
      faqError: null,

      setWalletId: (id: string) => set({ walletId: id }),
      setCurrency: (currency: Currency) => {
        set({ currency })
        const walletStore = useWalletStore.getState()
        walletStore.updateCurrentBalance(currency)
      },
      setDefaultCurrency: (currency: Currency) => set({ defaultCurrency: currency }),
      resetCurrency: () => {
        const defaultCurrency = get().defaultCurrency
        set({ currency: defaultCurrency })
        useWalletStore.getState().updateCurrentBalance(defaultCurrency)
      },

      exchangeRates: {},

      getExchangeRate: (fromCurrency: Currency, toCurrency: Currency) => {
        const currencies = get().currencies

        // Get the exchange rates (all rates are relative to CDF)
        const fromCurrencyDetails = currencies.find(
          (curr) => curr.code === get().getServerCurrencyCode(fromCurrency),
        )
        const toCurrencyDetails = currencies.find(
          (curr) => curr.code === get().getServerCurrencyCode(toCurrency),
        )

        // If converting from CDF to another currency
        if (fromCurrency === "FC") {
          const toRate = toCurrencyDetails?.exchange_rate
          return toRate ? 1 / parseFloat(toRate) : 0
        }

        // If converting to CDF
        if (toCurrency === "FC") {
          const fromRate = fromCurrencyDetails?.exchange_rate
          return fromRate ? parseFloat(fromRate) : 0
        }

        // If converting between two non-CDF currencies
        // First convert to CDF, then to target currency
        const fromRate = parseFloat(fromCurrencyDetails?.exchange_rate || "0")
        const toRate = parseFloat(toCurrencyDetails?.exchange_rate || "0")

        if (fromRate && toRate) {
          return fromRate / toRate
        }

        return 0
      },

      convertAmount: (amount: number, fromCurrency: Currency, toCurrency: Currency) => {
        const rate = get().getExchangeRate(fromCurrency, toCurrency)
        return amount * rate
      },

      getServerCurrencyCode: (displayCurrency: string) => {
        const currencies = get().currencies

        // Map display currencies to server codes
        switch (displayCurrency.toUpperCase()) {
          case "FC":
            // First try to find in API currencies
            const congoCurrency = currencies.find(
              (curr) =>
                curr.name.toLowerCase().includes("congolais") || curr.code.toUpperCase() === "CDF",
            )
            return congoCurrency?.code || "CDF"

          case "USD":
            const usdCurrency = currencies.find((curr) => curr.code.toUpperCase() === "USD")
            return usdCurrency?.code || "USD"

          default:
            // Try to find the currency in the API currencies
            const foundCurrency = currencies.find(
              (curr) => curr.code.toUpperCase() === displayCurrency.toUpperCase(),
            )
            return foundCurrency?.code || "CDF" // Default to CDF if not found
        }
      },

      getCurrencyByName: (name: string) => {
        // First try to find the currency in our currencies array
        const currencies = get().currencies
        const foundCurrency = currencies.find(
          (curr) => curr.name.toLowerCase() === name.toLowerCase(),
        )

        if (foundCurrency) {
          return foundCurrency.code as Currency
        }

        // If not found in currencies array, handle common naming patterns
        const normalizedName = name.toLowerCase()

        if (
          normalizedName.includes("American Dollar (USD)") ||
          normalizedName.includes("USD") ||
          normalizedName === "dollar"
        ) {
          return "USD"
        }

        if (
          normalizedName.includes("congolais") ||
          normalizedName.includes("fc") ||
          normalizedName === "franc congolais"
        ) {
          return "FC"
        }
        return "FC"
      },

      fetchCurrencies: async () => {
        // Get current state
        const state = get()
        const now = Date.now()

        // Check if we're already fetching currencies
        if (state.isFetchingCurrencies) {
          // Only log in development mode
          if (__DEV__) {
            // console.log("Currency fetch already in progress, skipping...")
          }
          return
        }

        // Check if we've fetched currencies recently (within the last 30 minutes)
        const CACHE_DURATION = 30 * 60 * 1000 // 30 minutes in milliseconds
        if (state.lastCurrencyFetch && now - state.lastCurrencyFetch < CACHE_DURATION) {
          // Only log in development mode and reduce verbosity
          if (__DEV__ && state.currencies.length > 0) {
            // console.log(
            //   "Using cached currency data, last fetched at:",
            //   new Date(state.lastCurrencyFetch),
            // )
          }
          return
        }

        // Set loading state and prevent concurrent fetches
        set({ isLoadingCurrencies: true, error: null, isFetchingCurrencies: true })

        try {
          // Only log in development mode
          if (__DEV__) {
            console.log("Fetching currency data...")
          }

          const response = await getCurrencies()

          if (response.success && Array.isArray(response.data.data)) {
            // Create exchange rates mapping
            const exchangeRates: { [key: string]: number } = {}
            response.data.data.forEach((curr: CurrencyDetails) => {
              exchangeRates[curr.code] = parseFloat(curr.exchange_rate)
            })

            // Update state with new data and timestamp
            set({
              currencies: response.data.data,
              exchangeRates,
              isLoadingCurrencies: false,
              isFetchingCurrencies: false,
              lastCurrencyFetch: now,
            })

            // Only log in development mode
            if (__DEV__) {
              console.log("Currency data updated successfully")
            }
          } else {
            // Only log detailed error in development mode
            if (__DEV__) {
              console.error("Invalid currency data format:", response)
            } else {
              console.error("Invalid currency data format")
            }

            set({
              isLoadingCurrencies: false,
              isFetchingCurrencies: false,
              error: "Invalid currency data format",
              // Keep existing currencies and exchange rates
            })
          }
        } catch (error) {
          // Only log detailed error in development mode
          if (__DEV__) {
            console.error("Error fetching currencies:", error)
          } else {
            console.error("Error fetching currencies")
          }

          set({
            isLoadingCurrencies: false,
            isFetchingCurrencies: false,
            error: "Failed to fetch currencies",
            // Keep existing currencies and exchange rates
          })
        }
      },

      getCurrencyById: (id: number) => {
        const currencies = get().currencies
        return currencies && Array.isArray(currencies)
          ? currencies.find((currency) => currency.id === id)
          : undefined
      },

      getCurrencyByCode: (code: string) => {
        const currencies = get().currencies
        return currencies && Array.isArray(currencies)
          ? currencies.find((currency) => currency.code.toLowerCase() === code.toLowerCase())
          : undefined
      },

      getStandardizedCurrency: (currencyCode: string): Currency => {
        const normalizedCode = currencyCode.toUpperCase()
        if (
          normalizedCode === "Congolese Franc" ||
          normalizedCode === "FC" ||
          normalizedCode === "CDF"
        ) {
          return "FC"
        }
        if (normalizedCode === "USD" || normalizedCode === "DOLLAR") {
          return "USD"
        }
        return "FC" // default fallback
      },

      // Fetch FAQ data
      fetchFAQData: async () => {
        const state = get()

        // Don't fetch if already fetching
        if (state.isFetchingFAQ) {
          if (__DEV__) {
            console.log("FAQ fetch already in progress, skipping...")
          }
          return
        }

        set({ isFetchingFAQ: true, faqError: null })

        try {
          if (__DEV__) {
            console.log("Fetching FAQ data...")
          }

          const response = await getFAQData()

          if (response.success && response.data) {
            set({
              fetchFAQacontent: response.data,
              isFetchingFAQ: false,
            })

            if (__DEV__) {
              console.log("FAQ data updated successfully")
            }
          } else {
            if (__DEV__) {
              console.error("Invalid FAQ data format:", response)
            } else {
              console.error("Invalid FAQ data format")
            }

            set({
              isFetchingFAQ: false,
              faqError: "Invalid FAQ data format",
            })
          }
        } catch (error: any) {
          if (__DEV__) {
            console.error("Error fetching FAQ data:", error)
          } else {
            console.error("Error fetching FAQ data")
          }

          set({
            isFetchingFAQ: false,
            faqError: error.response?.data?.message || "Failed to fetch FAQ data",
          })
        }
      },

      // Fetch Fedha business account details
      fetchFedhaDetails: async () => {
        const state = get()

        // Don't fetch if already fetching
        if (state.isFetchingFedhaDetails) {
          // console.log("Fedha details fetch already in progress, skipping...")
          return
        }

        set({ isFetchingFedhaDetails: true, error: null })

        try {
          // console.log("Fetching Fedha business account details...")

          const response = await axiosInstance.get("/management/fedha/business/account/")

          // Call the dedicated FAQ API function instead of directly calling the endpoint
          await get().fetchFAQData()

          if (response.data) {
            set({
              fedhaBusinessAccount: response.data,
              isFetchingFedhaDetails: false,
            })
          } else {
            set({
              error: "Invalid Fedha business account data format",
              isFetchingFedhaDetails: false,
            })
          }
        } catch (error: any) {
          console.error("Error fetching Fedha business account details:", error)

          set({
            error:
              error.response?.data?.message || "Failed to fetch Fedha business account details",
            isFetchingFedhaDetails: false,
          })
        }
      },
    }),
    {
      name: "app-settings",
      storage: createJSONStorage(() => ({
        getItem: (key) => {
          const value = storage.getString(key)
          return value ? JSON.parse(value) : null
        },
        setItem: (key, value) => storage.set(key, JSON.stringify(value)),
        removeItem: (key) => storage.delete(key),
      })),
    },
  ),
)

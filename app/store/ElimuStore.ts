// /elimu/courses/
import { create } from "zustand"
import { persist, createJSONStorage } from "zustand/middleware"
import { storage } from "@/utils/storage"
import axiosInstance from "@/config/axiosInstance"

export interface ElimuCourse {
  id: number
  fullname: string
  shortname: string
  displayname: string
  summary: string
  summaryformat: number
  startdate: number
  enddate: number
  visible: number
  showgrades: number
  newsitems: number
  categoryid: number
  categorysortorder: number
  format: string
  showreports: number
  maxbytes: number
  groupmode: number
  groupmodeforce: number
  defaultgroupingid: number
  timecreated: number
  timemodified: number
  enablecompletion: number
  completionnotify: number
  lang: string
  forcetheme: string
  hiddensections: number
  numsections: number
  showactivitydates: boolean
  showcompletionconditions: boolean
  idnumber: string
  courseformatoptions: any[]
}

interface ElimuCoursesState {
  courses: ElimuCourse[]
  loading: boolean
  error: string | null
  // Actions
  fetchCourses: () => Promise<void>
}

export const useElimuStore = create<ElimuCoursesState>()(
  persist(
    (set) => ({
      courses: [],
      loading: false,
      error: null,

      async fetchCourses() {
        try {
          set({ loading: true, error: null })
          const response = await axiosInstance.get("/elimu/courses/")
          set({ courses: response.data, loading: false })
        } catch (error) {
          set({ loading: false, error: error as string })
        }
      },
    }),
    {
      name: "elimu-courses-store",
      storage: createJSONStorage(() => ({
        getItem: (key) => {
          const value = storage.getString(key)
          return value ? JSON.parse(value) : null
        },
        setItem: (key, value) => {
          storage.set(key, JSON.stringify(value))
        },
        removeItem: (key) => {
          storage.delete(key)
        },
      })),
    },
  ),
)

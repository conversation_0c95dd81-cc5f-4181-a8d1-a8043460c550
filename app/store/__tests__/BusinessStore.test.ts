import { renderHook, act } from '@testing-library/react-hooks'
import { useBusinessStore } from '../BusinessStore'
import axiosInstance from '@/config/axiosInstance'

// Mock axios instance
jest.mock('@/config/axiosInstance', () => ({
  get: jest.fn(),
}))

describe('BusinessStore', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks()
    
    // Reset the store state
    const { result } = renderHook(() => useBusinessStore())
    act(() => {
      result.current.businesses = []
      result.current.currentBusiness = null
      result.current.categories = []
      result.current.loading = false
      result.current.error = null
    })
  })

  describe('fetchBusinessCategories', () => {
    it('should fetch business categories successfully', async () => {
      // Mock API response
      const mockCategories = [
        {
          id: '123e4567-e89b-12d3-a456-426614174000',
          name: 'Retail',
          description: 'Retail businesses',
          icon: 'shop',
          status: 'active',
          created_at: '2023-01-01T00:00:00Z',
          updated_at: null,
        },
        {
          id: '223e4567-e89b-12d3-a456-426614174001',
          name: 'Food & Beverage',
          description: 'Restaurants and cafes',
          icon: 'food',
          status: 'active',
          created_at: '2023-01-01T00:00:00Z',
          updated_at: null,
        },
      ]
      
      // Setup the mock
      ;(axiosInstance.get as jest.Mock).mockResolvedValueOnce({
        data: mockCategories,
      })
      
      // Get the store hook
      const { result, waitForNextUpdate } = renderHook(() => useBusinessStore())
      
      // Initial state should be empty
      expect(result.current.categories).toEqual([])
      expect(result.current.loading).toBe(false)
      expect(result.current.error).toBe(null)
      
      // Call the fetch function
      act(() => {
        result.current.fetchBusinessCategories()
      })
      
      // Should be loading
      expect(result.current.loading).toBe(true)
      
      // Wait for the async operation to complete
      await waitForNextUpdate()
      
      // Check that the API was called correctly
      expect(axiosInstance.get).toHaveBeenCalledWith('/business/categories/')
      
      // Check that the state was updated correctly
      expect(result.current.categories).toEqual(mockCategories)
      expect(result.current.loading).toBe(false)
      expect(result.current.error).toBe(null)
    })
    
    it('should handle errors when fetching business categories', async () => {
      // Setup the mock to reject
      const errorMessage = 'Network error'
      ;(axiosInstance.get as jest.Mock).mockRejectedValueOnce({
        response: {
          data: {
            message: errorMessage,
          },
        },
      })
      
      // Get the store hook
      const { result, waitForNextUpdate } = renderHook(() => useBusinessStore())
      
      // Call the fetch function
      act(() => {
        result.current.fetchBusinessCategories()
      })
      
      // Wait for the async operation to complete
      await waitForNextUpdate()
      
      // Check that the API was called correctly
      expect(axiosInstance.get).toHaveBeenCalledWith('/business/categories/')
      
      // Check that the error state was updated correctly
      expect(result.current.categories).toEqual([])
      expect(result.current.loading).toBe(false)
      expect(result.current.error).toBe(errorMessage)
    })
  })
})

/* eslint-disable import/no-unresolved */
import { create } from "zustand"
import { persist, createJSONStorage } from "zustand/middleware"
import { storage } from "@/utils/storage"

// Define the cart item type
export interface CartItem {
  id: string // Unique identifier for the cart item
  name: string
  description: string
  price: number
  currency: string
  quantity: number
}

// Define the cart store state
interface CartState {
  items: CartItem[]
  loading: boolean
  error: string | null

  // Actions
  addItem: (item: Omit<CartItem, "id" | "quantity">) => void
  removeItem: (id: string) => void
  updateQuantity: (id: string, quantity: number) => void
  updateItem: (id: string, updatedProperties: Partial<CartItem>) => boolean
  clearCart: () => void
  getItemCount: () => number
  getTotalPrice: () => { [currency: string]: number }
}

export const useCartStore = create<CartState>()(
  persist(
    (set, get) => ({
      items: [],
      loading: false,
      error: null,

      // Add an item to the cart
      addItem: (item) => {
        const { items } = get()

        // Generate a unique ID for the item
        const id = `${Date.now()}_${Math.random().toString(36).substring(2, 9)}`

        // Handle generic "Article" naming with numbering
        let itemName = item.name

        if (itemName === "Article") {
          // Count existing generic articles
          const genericArticles = items.filter(
            (cartItem) => cartItem.name.startsWith("Article ") || cartItem.name === "Article",
          )

          // Set the name to "Article X" where X is the next number
          itemName = `Article ${genericArticles.length + 1}`
        }

        // Check if the item already exists in the cart (same name, price, and currency)
        const existingItemIndex = items.findIndex(
          (cartItem) =>
            cartItem.name === itemName &&
            cartItem.price === item.price &&
            cartItem.currency === item.currency &&
            cartItem.description === item.description,
        )

        if (existingItemIndex !== -1) {
          // If the item exists, update its quantity
          const updatedItems = [...items]
          updatedItems[existingItemIndex].quantity += 1

          set({ items: updatedItems })
        } else {
          // If the item doesn't exist, add it to the cart
          set({ items: [...items, { ...item, name: itemName, id, quantity: 1 }] })
        }
      },

      // Remove an item from the cart
      removeItem: (id) => {
        const { items } = get()
        set({ items: items.filter((item) => item.id !== id) })
      },

      // Update the quantity of an item in the cart
      updateQuantity: (id, quantity) => {
        const { items } = get()

        if (quantity <= 0) {
          // If the quantity is 0 or negative, remove the item
          set({ items: items.filter((item) => item.id !== id) })
        } else {
          // Otherwise, update the quantity
          const updatedItems = items.map((item) => (item.id === id ? { ...item, quantity } : item))

          set({ items: updatedItems })
        }
      },

      // Update all properties of an item in the cart
      updateItem: (id, updatedProperties) => {
        const { items } = get()

        // Find the item to update
        const itemIndex = items.findIndex((item) => item.id === id)

        if (itemIndex === -1) {
          // Item not found
          return false
        }

        // Create a new array with the updated item
        const updatedItems = [...items]
        updatedItems[itemIndex] = {
          ...updatedItems[itemIndex],
          ...updatedProperties,
          // Ensure quantity is at least 1
          quantity:
            updatedProperties.quantity && updatedProperties.quantity > 0
              ? updatedProperties.quantity
              : updatedItems[itemIndex].quantity,
        }

        set({ items: updatedItems })
        return true
      },

      // Clear the cart
      clearCart: () => {
        set({ items: [] })
      },

      // Get the total number of items in the cart
      getItemCount: () => {
        const { items } = get()
        return items.reduce((total, item) => total + item.quantity, 0)
      },

      // Get the total price of items in the cart, grouped by currency
      getTotalPrice: () => {
        const { items } = get()

        return items.reduce(
          (totals, item) => {
            const { currency, price, quantity } = item

            if (!totals[currency]) {
              totals[currency] = 0
            }

            totals[currency] += price * quantity

            return totals
          },
          {} as { [currency: string]: number },
        )
      },
    }),
    {
      name: "cart-store",
      storage: createJSONStorage(() => ({
        getItem: (key) => {
          const value = storage.getString(key)
          return value ? JSON.parse(value) : null
        },
        setItem: (key, value) => {
          storage.set(key, JSON.stringify(value))
        },
        removeItem: (key) => {
          storage.delete(key)
        },
      })),
    },
  ),
)

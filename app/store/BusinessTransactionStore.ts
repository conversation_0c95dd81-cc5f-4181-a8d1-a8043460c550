import { create } from "zustand"
import { persist, createJSONStorage } from "zustand/middleware"
import { storage } from "@/utils/storage"
import { Transaction } from "@/services/api/api.types"
import axiosInstance from "@/config/axiosInstance"
import { useBusinessStore } from "./BusinessStore"

// Define the business transaction interface
export interface BusinessTransaction extends Transaction {
  // Add any business-specific fields here
  // business_id?: string
}

// Define the daily summary interface
export interface DailySummary {
  date: string
  totalIn: number
  totalOut: number
  lastCashupAmount: number
  currency: string
}

interface BusinessTransactionState {
  transactions: BusinessTransaction[]
  loading: boolean
  error: string | null
  dailySummary: DailySummary | null

  // Actions
  fetchBusinessTransactions: () => Promise<void>
  fetchDailySummary: () => Promise<void>
  getTransactionById: (id: string) => BusinessTransaction | undefined
}

// No mock data needed - using real API data

export const useBusinessTransactionStore = create<BusinessTransactionState>()(
  persist(
    (set, get) => ({
      transactions: [],
      loading: true, // Start with loading state
      error: null,
      dailySummary: null,

      async fetchBusinessTransactions() {
        try {
          set({ loading: true, error: null })

          // Get the main business from the BusinessStore
          const mainBusiness = useBusinessStore.getState().getMainBusiness()

          if (!mainBusiness) {
            throw new Error("No main business found")
          }

          const businessId = mainBusiness.business_no
          // console.log("busineID", businessId)
          // Use the real API endpoint with the business ID
          const response = await axiosInstance.get<any>(
            `/transaction/business/history/?business=${businessId}`,
          )

          // console.log(response.data)

          set({
            transactions: response.data,
            loading: false,
            error: null,
          })
        } catch (error: any) {
          console.error("Failed to fetch business transactions:", error.response || error)

          // Keep existing transactions if available
          const currentTransactions = get().transactions

          set({
            error: "Impossible de mettre à jour l'historique des transactions",
            loading: false,
            transactions: currentTransactions.length ? currentTransactions : [],
          })
        }
      },

      async fetchDailySummary() {
        try {
          set({ loading: true, error: null })

          const transactions = get().transactions

          if (transactions.length > 0) {
            // Get today's date in YYYY-MM-DD format
            const today = new Date().toISOString().split("T")[0]

            // Filter transactions for today
            const todayTransactions = transactions.filter((t) => t.created_at.startsWith(today))

            // Calculate totals
            let totalIn = 0
            let totalOut = 0

            todayTransactions.forEach((t) => {
              const amount = parseFloat(t.amount)
              if (t.transaction_action === "deposit") {
                totalIn += amount
              } else if (
                t.transaction_action === "withdraw" ||
                t.transaction_action === "transfer"
              ) {
                totalOut += amount
              }
            })

            // Set the daily summary
            set({
              dailySummary: {
                date: today,
                totalIn,
                totalOut,
                lastCashupAmount: totalIn - totalOut, // Simplified calculation
                currency: "FC", // Always use FC for business transactions
              },
              loading: false,
            })
          } else {
            // No transactions available
            set({
              dailySummary: {
                date: new Date().toISOString().split("T")[0],
                totalIn: 0,
                totalOut: 0,
                lastCashupAmount: 0,
                currency: "FC",
              },
              loading: false,
            })
          }
        } catch (error: any) {
          console.error("Failed to calculate daily summary:", error)
          set({
            error: "Impossible de calculer le résumé quotidien",
            loading: false,
          })
        }
      },

      getTransactionById(id: string) {
        return get().transactions.find((t) => t.id === id)
      },
      
    }),
    {
      name: "business-transaction-store",
      storage: createJSONStorage(() => ({
        getItem: (key) => {
          const value = storage.getString(key)
          return value ? JSON.parse(value) : null
        },
        setItem: (key, value) => {
          storage.set(key, JSON.stringify(value))
        },
        removeItem: (key) => {
          storage.delete(key)
        },
      })),
    },
  ),
)

/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable import/no-unresolved */
import { create } from "zustand"
import { persist, createJSONStorage } from "zustand/middleware"
import { storage } from "@/utils/storage"
import { getPersonnelList, addPersonnel } from "@/services/api/api"

// Define the personnel type from API
export interface Personnel {
  id: string
  user: {
    id: string
    first_name: string
    last_name: string
    email: string
    phone_number: string
    profile_picture: string | null
    last_login: string | null
  }
  permissions: string[]
  business: string
  created_at: string
  status: "active" | "inactive" | "suspended"
}

// Define the staff member type (for UI compatibility)
export interface StaffMember {
  id: string
  name: string
  staffId: string
  email: string
  phone: string
  role: string
  profileImage: string | null
  lastLogin: string | null
  status: "active" | "inactive" | "suspended"
  permissions: string[]
  createdAt: string
  businessId: string
}

// Define the staff store state
interface StaffState {
  staff: StaffMember[]
  personnel: Personnel[]
  selectedStaff: StaffMember | null
  loading: boolean
  error: string | null

  // Actions
  fetchStaff: (businessId: string) => Promise<void>
  fetchPersonnel: (businessId: string) => Promise<void>
  addStaffMember: (staffMember: Omit<StaffMember, "id" | "createdAt">) => Promise<boolean>
  addPersonnelMember: (data: {
    business: string
    personnel: string
    permissions: string[]
  }) => Promise<boolean>
  updateStaffMember: (id: string, updates: Partial<StaffMember>) => Promise<boolean>
  removeStaffMember: (id: string) => Promise<boolean>
  selectStaffMember: (id: string) => void
  clearSelectedStaff: () => void
  searchStaff: (query: string) => StaffMember[]
  convertPersonnelToStaffMember: (personnel: Personnel) => StaffMember
}

export const useStaffStore = create<StaffState>()(
  persist(
    (set, get) => ({
      staff: [],
      personnel: [],
      selectedStaff: null,
      loading: false,
      error: null,

      // Convert Personnel to StaffMember format for UI compatibility
      convertPersonnelToStaffMember: (personnel: Personnel): StaffMember => {
        // Handle potential missing data gracefully
        if (!personnel || !personnel.user) {
          console.error("Invalid personnel data:", personnel)
          return {
            id: "unknown",
            name: "Unknown User",
            staffId: "unknown",
            email: "",
            phone: "",
            role: "Unknown",
            profileImage: null,
            lastLogin: null,
            status: "inactive",
            permissions: [],
            createdAt: new Date().toISOString(),
            businessId: "",
          }
        }

        // Determine role based on permissions
        let role = "Staff"
        if (personnel.permissions) {
          if (personnel.permissions.includes("manage_staff")) {
            role = "Manager"
          } else if (personnel.permissions.includes("process_orders")) {
            role = "Cashier"
          }
        }
        // GOD is that which brings order out of chaos formless pontential, generates the world out of formless potential, and that we're somehow made in that image
        //  acting in that image is to act in relationship to the potential that confronts you with truth and courage with careful articilation that's the logos and if you
        // if you do that then what you bring forth is good
        return {
          id: personnel.id || "unknown",
          name:
            `${personnel.user.first_name || ""} ${personnel.user.last_name || ""}`.trim() ||
            "Unknown User",
          staffId: personnel.user.id || "unknown",
          email: personnel.user.email || "",
          phone: personnel.user.phone_number || "",
          role: role,
          profileImage: personnel.user.profile_picture,
          lastLogin: personnel.user.last_login,
          status: personnel.status || "inactive",
          permissions: personnel.permissions || [],
          createdAt: personnel.created_at || new Date().toISOString(),
          businessId: personnel.business || "",
        }
      },

      // Fetch personnel from the API
      fetchPersonnel: async (businessId: string) => {
        try {
          set({ loading: true, error: null })

          const response = await getPersonnelList(businessId)

          if (response.success && response.data) {
            // Store the raw personnel data
            set({ personnel: response.data })

            // Convert API data to StaffMember format for UI compatibility
            if (Array.isArray(response.data)) {
              const staffMembers = response.data.map((p) => get().convertPersonnelToStaffMember(p))
              set({
                staff: staffMembers,
                loading: false,
              })
            } else {
              console.error("Personnel data is not an array:", response.data)
              set({
                staff: [],
                loading: false,
                error: "Invalid personnel data format",
              })
            }

            return response.data
          } else {
            set({
              error: response.message || "Failed to fetch personnel",
              loading: false,
              staff: [], // Clear any existing staff data
            })
            return []
          }
        } catch (error: any) {
          console.error("Failed to fetch personnel:", error)
          set({
            error: error.response?.data?.message || "Failed to fetch personnel",
            loading: false,
            staff: [], // Clear any existing staff data
          })
          return []
        }
      },

      // Fetch staff members for a business
      fetchStaff: async (businessId: string) => {
        // Use the fetchPersonnel method instead of mock data
        return get().fetchPersonnel(businessId)
      },

      // Add a new personnel member
      addPersonnelMember: async (data: {
        business: string
        personnel: string
        permissions: string[]
      }) => {
        try {
          set({ loading: true, error: null })

          const response = await addPersonnel(data)

          if (response.success) {
            // Refresh the personnel list after adding
            await get().fetchPersonnel(data.business)
            return true
          } else {
            set({
              error: response.message || "Failed to add personnel",
              loading: false,
            })
            return false
          }
        } catch (error: any) {
          console.error("Failed to add personnel:", error)
          set({
            error: error.response?.data?.message || "Failed to add personnel",
            loading: false,
          })
          return false
        }
      },

      // Add a new staff member (uses addPersonnelMember under the hood)
      addStaffMember: async (_staffMember) => {
        try {
          set({ loading: true, error: null })

          // In a real implementation, we would convert staffMember to the format expected by the API
          // For now, we'll just return false as this method is being replaced by addPersonnelMember
          set({ loading: false })
          return false
        } catch (error: any) {
          console.error("Failed to add staff member:", error)
          set({
            error: error.response?.data?.message || "Failed to add staff member",
            loading: false,
          })
          return false
        }
      },

      // Update a staff member
      updateStaffMember: async (id, updates) => {
        try {
          set({ loading: true, error: null })

          // In a real implementation, this would be an API call
          // const response = await axiosInstance.put(`/business/staff/${id}/`, updates)

          // For now, we'll update the local state
          set((state) => ({
            staff: state.staff.map((member) =>
              member.id === id ? { ...member, ...updates } : member,
            ),
            selectedStaff:
              state.selectedStaff?.id === id
                ? { ...state.selectedStaff, ...updates }
                : state.selectedStaff,
            loading: false,
          }))

          return true
        } catch (error: any) {
          console.error("Failed to update staff member:", error)
          set({
            error: error.response?.data?.message || "Failed to update staff member",
            loading: false,
          })
          return false
        }
      },

      // Remove a staff member
      removeStaffMember: async (id) => {
        try {
          set({ loading: true, error: null })

          // In a real implementation, this would be an API call
          // const response = await axiosInstance.delete(`/business/staff/${id}/`)

          // For now, we'll update the local state
          set((state) => ({
            staff: state.staff.filter((member) => member.id !== id),
            selectedStaff: state.selectedStaff?.id === id ? null : state.selectedStaff,
            loading: false,
          }))

          return true
        } catch (error: any) {
          console.error("Failed to remove staff member:", error)
          set({
            error: error.response?.data?.message || "Failed to remove staff member",
            loading: false,
          })
          return false
        }
      },

      // Select a staff member
      selectStaffMember: (id) => {
        const { staff } = get()
        const selectedMember = staff.find((member) => member.id === id) || null
        set({ selectedStaff: selectedMember })
      },

      // Clear selected staff member
      clearSelectedStaff: () => {
        set({ selectedStaff: null })
      },

      // Search staff members
      searchStaff: (query) => {
        const { staff } = get()
        if (!staff || staff.length === 0) return []
        if (!query) return staff

        const lowerQuery = query.toLowerCase()
        return staff.filter(
          (member) =>
            (member.name && member.name.toLowerCase().includes(lowerQuery)) ||
            (member.email && member.email.toLowerCase().includes(lowerQuery)) ||
            (member.staffId && member.staffId.toLowerCase().includes(lowerQuery)) ||
            (member.role && member.role.toLowerCase().includes(lowerQuery)),
        )
      },
    }),
    {
      name: "staff-store",
      storage: createJSONStorage(() => ({
        getItem: (key) => {
          const value = storage.getString(key)
          return value ? JSON.parse(value) : null
        },
        setItem: (key, value) => {
          storage.set(key, JSON.stringify(value))
        },
        removeItem: (key) => {
          storage.delete(key)
        },
      })),
    },
  ),
)

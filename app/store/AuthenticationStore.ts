import axiosInstance from "@/config/axiosInstance"
import { create } from "zustand"
import { persist, createJSONStorage } from "zustand/middleware"
import { storage } from "@/utils/storage" // MMKV storage
import { useWalletStore } from "./FedhaPochiStore"
import { useCartStore } from "./CartStore"
import * as SecureStore from "expo-secure-store"
// import { z } from "zod"
import { UserResponseSchema } from "@/services/api/api.types"
import type { UserType } from "@/services/api/api.types"
import { setSentryUser, clearSentryUser } from "@/utils/sentryUtils"
import { usrlogout } from "@/services/api"
import { navigationRef } from "@/navigators/navigationUtilities"

interface AuthState {
  isAuthenticated: boolean
  user: UserType | null
  otpExpiration: number | null
  otpStartTime: number | null
  isVerified: boolean
  isPhoneVerified: boolean
  isInfoVerified: boolean
  isDocumentVerified: boolean
  user_type: string
  accessToken: string | null
  has_business: false | null
  refreshToken: string | null
  failedTokenAttempts: number
  isLoggingOut: boolean
  fetchUserData: () => Promise<void>
  login: (phone_number: string, password: string) => Promise<void>
  register: (phone_number: string, password: string) => Promise<{ success: boolean; data: any }>
  registerBusiness: (
    phone_number: string,
    password: string,
  ) => Promise<{ success: boolean; data: any }>
  verifyOtp: (otp: number) => Promise<{ success: boolean; message: string }>
  resendOtp: () => Promise<{ success: boolean; message: string }>
  logout: () => void
  refreshAuthToken: () => Promise<string>
  clearLocalData: () => void
  resetFailedAttempts: () => void
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      isAuthenticated: false,
      user: null,
      failedTokenAttempts: 0, // Add this line to track failed attempts
      accessToken: null,
      refreshToken: null,
      isPhoneVerified: false,
      isDocumentVerified: false,
      has_business: false,
      isInfoVerified: false,
      isVerified: false,
      user_type: "",
      otpExpiration: null,
      otpStartTime: null,
      isLoggingOut: false,

      async fetchUserData() {
        try {
          const { data } = await axiosInstance.get("/profile/")
          // console.log("🔄 Fetching user profile:", data)

          // ✅ Validate response using Zod
          const parsedData = UserResponseSchema.parse(data)

          set({
            user: parsedData.user, // ✅ Correct: Assign parsed data, not a type
            isAuthenticated: true,
            isInfoVerified: parsedData.user.is_info_verified,
            isPhoneVerified: parsedData.user.is_phone_verified,
            isDocumentVerified: parsedData.user.is_documents_verified,
          })

          // Set user information in Sentry for error tracking
          setSentryUser({
            id: parsedData.user.id.toString(),
            email: parsedData.user.email || undefined,
            username: `${parsedData.user.first_name} ${parsedData.user.last_name}`,
            phone: parsedData.user.phone_number,
          })
        } catch (error: any) {
          console.error(
            "❌ Failed to fetch user data:",
            error.response?.data?.message || error.message,
          )
        }
      },

      async login(phone_number: string, password: string) {
        try {
          const { data } = await axiosInstance.post("/login/", { phone_number, password })
          // Save tokens to storage immediately
          storage.set("accessToken", data.access)
          storage.set("refreshToken", data.refresh)

          await SecureStore.setItemAsync("userPassword", password)

          set({
            isAuthenticated: true,
            accessToken: data.access,
            has_business: data.has_business,
            isInfoVerified: data.is_info_verified,
            isDocumentVerified: data.is_documents_verified,
            refreshToken: data.refresh,
            otpExpiration: null,
            isPhoneVerified: data.is_phone_verified,
            user_type: data.user_type.toLowerCase(),
          })

          // Set authorization header
          axiosInstance.defaults.headers.common.Authorization = `Bearer ${data.access}`

          await get().fetchUserData()

          // Set user information in Sentry for error tracking
          const user = get().user
          if (user) {
            setSentryUser({
              id: user.id.toString(),
              email: user.email || undefined,
              username: `${user.first_name} ${user.last_name}`,
              phone: user.phone_number,
            })
          }

          // Note: Push notification registration will happen automatically
          // via the NotificationContext when it detects that isAuthenticated has changed to true
        } catch (error: any) {
          console.error("❌ Login failed:", error.response?.data?.message || error.message)
          throw error
        }
      },

      async register(phone_number: string, password: string) {
        try {
          const { data } = await axiosInstance.post("/register/standard/", {
            phone_number,
            password,
          })

          // ✅ Automatically log in after registration
          await get().login(phone_number, password)

          const startTime = Date.now()
          const expirationTime = startTime + 5 * 60 * 1000 // 5 minutes in milliseconds

          set({
            otpStartTime: startTime,
            otpExpiration: expirationTime,
          })

          return { success: true, data }
        } catch (error: any) {
          console.error("❌ Registration failed:", error.response?.data?.message || error.message)
          throw error
        }
      },

      async registerBusiness(phone_number: string, password: string) {
        try {
          const { data } = await axiosInstance.post("/register/business/", {
            phone_number,
            password,
          })

          // ✅ Automatically log in after registration
          await get().login(phone_number, password)

          const startTime = Date.now()
          const expirationTime = startTime + 5 * 60 * 1000 // 5 minutes in milliseconds

          set({
            otpStartTime: startTime,
            otpExpiration: expirationTime,
          })

          return { success: true, data }
        } catch (error: any) {
          console.error("❌ Registration failed:", error.response?.data?.message || error.message)
          throw error
        }
      },

      async verifyOtp(otp: number) {
        try {
          const response = await axiosInstance.post("/otp/verify/", { otp })

          set({
            isVerified: true,
            otpExpiration: null,
            otpStartTime: null,
          })

          // ✅ Fetch latest user profile after OTP verification
          await get().fetchUserData()

          return { success: true, message: response.data.detail }
        } catch (error: any) {
          const currentTime = Date.now()
          const expiration = get().otpExpiration

          if (expiration && currentTime > expiration) {
            return {
              success: false,
              message: "Le code OTP a expiré. Veuillez demander un nouveau code.",
            }
          }

          return {
            success: false,
            message: error.response?.data?.detail || "Échec de la vérification de l'OTP.",
          }
        }
      },

      async resendOtp() {
        try {
          const currentTime = Date.now()
          const expiration = get().otpExpiration

          if (expiration && currentTime < expiration) {
            const remainingTime = Math.ceil((expiration - currentTime) / 1000)
            return {
              success: false,
              message: `Veuillez attendre ${remainingTime} secondes avant de demander un nouveau code.`,
            }
          }

          await axiosInstance.post("/otp/resend/")

          const startTime = Date.now()
          const newExpiration = startTime + 5 * 60 * 1000

          set({
            otpStartTime: startTime,
            otpExpiration: newExpiration,
          })

          return { success: true, message: "Nouveau OTP envoyé avec succès." }
        } catch (error: any) {
          return {
            success: false,
            message: error.response,
          }
        }
      },

      async refreshAuthToken(): Promise<string> {
        // Check if logout is in progress
        if (get().isLoggingOut) {
          throw new Error("Logout in progress")
        }

        const refreshToken = storage.getString("refreshToken")
        if (!refreshToken) {
          console.warn("No refresh token found")
          // Don't call logout here to avoid infinite loops
          // Just clear local data and throw error
          get().clearLocalData()
          throw new Error("No refresh token found")
        }

        try {
          // Make the refresh request without using the interceptor to avoid loops
          const response = await fetch(
            `${process.env.DEV_API_URL || "https://stageapi.fedha.link/api"}/refresh/`,
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({ refresh: refreshToken }),
            },
          )

          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`)
          }

          const data = await response.json()

          // Save new access token
          storage.set("accessToken", data.access)

          set({ accessToken: data.access })
          axiosInstance.defaults.headers.common.Authorization = `Bearer ${data.access}`

          console.log("✅ Token refreshed successfully")
          return data.access
        } catch (error) {
          console.error("❌ Token refresh failed:", error)
          // Don't call logout here to avoid infinite loops
          // Just clear local data and throw error
          get().clearLocalData()
          throw new Error("Token refresh failed")
        }
      },

      // Helper function to clear all local data
      clearLocalData() {
        console.log("🧹 Clearing all local data...")

        // Clear storage
        storage.delete("accessToken")
        storage.delete("refreshToken")
        storage.delete("auth-store")
        SecureStore.deleteItemAsync("userPassword")

        // Reset wallet store
        useWalletStore.setState({
          wallet: null,
          currentBalance: 0,
          otherCurrencies: [],
          loading: false,
          error: null,
        })

        // Clear the cart
        useCartStore.getState().clearCart()

        // Reset auth store state
        set({
          isAuthenticated: false,
          user: null,
          accessToken: null,
          refreshToken: null,
          isVerified: false,
          isPhoneVerified: false,
          isInfoVerified: false,
          isDocumentVerified: false,
          otpExpiration: null,
          otpStartTime: null,
          failedTokenAttempts: 0,
          user_type: "",
          has_business: false,
          isLoggingOut: false,
        })

        // Clear authorization header
        delete axiosInstance.defaults.headers.common.Authorization

        // Clear Sentry user information
        clearSentryUser()

        console.log("✅ Local data cleared successfully")
      },

      // Helper function to reset failed token attempts
      resetFailedAttempts() {
        set({ failedTokenAttempts: 0 })
      },

      async logout() {
        // Prevent multiple simultaneous logout calls
        if (get().isLoggingOut) {
          console.log("Logout already in progress, skipping...")
          return
        }

        console.log("🔴 Starting logout process...")
        set({ isLoggingOut: true })

        try {
          const refreshToken = storage.getString("refreshToken")

          // Only attempt API logout if we have a refresh token
          if (refreshToken) {
            try {
              // Try the logout API directly without refreshing
              const logoutData = await usrlogout()
              if (!logoutData.success) {
                throw new Error("Logout API call failed")
              }
              console.log("✅ API logout successful")
            } catch (error) {
              console.error("❌ Error during logout API call:", error)
              // If API call fails, just clear local data
            }
          } else {
            // If no refresh token, just clear local data
            console.log("No refresh token found, clearing local data only")
          }

          // Always clear local data first
          get().clearLocalData()

          // Navigate to Welcome screen with a small delay to ensure state is cleared
          setTimeout(() => {
            if (navigationRef.isReady()) {
              navigationRef.reset({
                index: 0,
                routes: [{ name: "Welcome" }],
              })
              console.log("✅ Successfully logged out and navigated to Welcome")
            }
          }, 100)
        } catch (error: any) {
          console.error("❌ Error during logout:", error)
          get().clearLocalData()

          // Ensure navigation happens even if there's an error
          setTimeout(() => {
            if (navigationRef.isReady()) {
              navigationRef.reset({
                index: 0,
                routes: [{ name: "Welcome" }],
              })
            }
          }, 100)
        } finally {
          // Reset the logout flag after a delay to prevent race conditions
          setTimeout(() => {
            set({ isLoggingOut: false })
          }, 500)
        }
      },

      async resetPassword(
        username: string,
        otp: string,
        new_password: string,
        confirm_password: string,
      ) {
        try {
          const { data } = await axiosInstance.post("/password/reset/", {
            username,
            otp,
            new_password,
            confirm_password,
          })
          return { success: true, message: data.detail }
        } catch (error: any) {
          console.error("❌ Error during reset password:", error)
          return {
            success: false,
            message:
              error.response?.data?.detail || "Échec de la réinitialisation du mot de passe.",
          }
        }
      },
    }),
    {
      name: "auth-store",
      storage: createJSONStorage(() => ({
        getItem: (key) => {
          const value = storage.getString(key)
          return value ? JSON.parse(value) : null
        },
        setItem: (key, value) => storage.set(key, JSON.stringify(value)),
        removeItem: (key) => storage.delete(key),
      })),
    },
  ),
)

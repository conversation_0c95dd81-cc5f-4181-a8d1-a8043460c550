/* eslint-disable @typescript-eslint/no-unused-vars */
import { create } from "zustand"
import NetInfo from "@react-native-community/netinfo"
// import { useAuthStore } from "@/store/auth"
import { Alert } from "react-native"
import { useAuthStore } from "./AuthenticationStore"
import { storage } from "@/utils/storage"
import axiosInstance from "@/config/axiosInstance"

interface NetworkState {
  isConnected: boolean
  checkInternet: () => void
  retryFailedRequests: () => Promise<void>
}

export const useNetworkStore = create<NetworkState>((set, get) => ({
  isConnected: true,

  checkInternet: async () => {
    const netInfo = await NetInfo.fetch()
    const isConnected = netInfo.isConnected ?? false
    set({ isConnected })

    if (!isConnected) {
      Alert.alert(
        "No Internet Connection",
        "Please check your internet connection and try again.",
        [{ text: "OK" }],
      )
    } else {
      // Retry failed requests when network is back
      get().retryFailedRequests()
    }
  },

  retryFailedRequests: async () => {
    try {
      const failedRequests = storage.getString("failedRequests")
      if (!failedRequests) return

      const requests = JSON.parse(failedRequests)
      if (!requests.length) return

      // Clear failed requests immediately to prevent duplicate retries
      storage.delete("failedRequests")

      // Retry each failed request
      for (const request of requests) {
        try {
          await axiosInstance({
            url: request.url,
            method: request.method,
            data: request.data,
            headers: request.headers,
          })
        } catch (error) {
          console.error("Failed to retry request:", error)
          // If retry fails, add back to failed requests
          const currentFailed = storage.getString("failedRequests") || "[]"
          const currentRequests = JSON.parse(currentFailed)
          currentRequests.push(request)
          storage.set("failedRequests", JSON.stringify(currentRequests))
        }
      }
    } catch (error) {
      console.error("Error retrying failed requests:", error)
    }
  },
}))

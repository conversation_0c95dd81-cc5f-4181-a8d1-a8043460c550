import { useAuthStore } from "@/store/AuthenticationStore"
import NetInfo from "@react-native-community/netinfo"
import { useNetworkStore } from "@/store/useNetworkStore"
import { storage } from "@/utils/storage"
import axios from "axios"

const axiosInstance = axios.create({
  baseURL: process.env.DEV_API_URL || "https://stageapi.fedha.link/api",
  withCredentials: false,
  headers: {
    "Content-Type": "application/json",
  },
})

// Initialize auth header if token exists
const token = storage.getString("accessToken")
if (token) {
  axiosInstance.defaults.headers.common.Authorization = `Bearer ${token}`
}

axiosInstance.interceptors.request.use(
  async (config) => {
    const netInfo = await NetInfo.fetch()
    if (!netInfo.isConnected) {
      useNetworkStore.getState().checkInternet()
      // Store the failed request for retry when network is back
      const failedRequests = storage.getString("failedRequests") || "[]"
      const requests = JSON.parse(failedRequests)
      requests.push({
        url: config.url,
        method: config.method,
        data: config.data,
        headers: config.headers,
        timestamp: Date.now(),
      })
      storage.set("failedRequests", JSON.stringify(requests))
      return Promise.reject({ message: "No internet connection" })
    }

    // Get fresh token from storage
    const token = storage.getString("accessToken")
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => Promise.reject(error),
)

const MAX_TOKEN_ATTEMPTS = 3
let tokenRefreshInProgress = false
let lastTokenRefreshTime = 0
const TOKEN_REFRESH_COOLDOWN = 1000 // 1 second cooldown between refresh attempts
let logoutInProgress = false // Add flag to prevent multiple logout calls

axiosInstance.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config
    const authStore = useAuthStore.getState()

    // Handle network errors
    if (!error.response && error.message === "Network Error") {
      console.warn("🌐 Network error detected")
      useNetworkStore.getState().checkInternet()
      return Promise.reject(error)
    }

    // Handle 401 Unauthorized errors
    if (error.response?.status === 401) {
      console.warn("🔒 Unauthorized request detected, checking token...")

      // If logout is already in progress, don't try to refresh
      if (logoutInProgress || authStore.isLoggingOut) {
        console.log("🔒 Logout in progress, skipping token refresh...")
        return Promise.reject(new Error("Logout in progress"))
      }

      // Increment failed attempts
      const currentAttempts = authStore.failedTokenAttempts + 1
      useAuthStore.setState({ failedTokenAttempts: currentAttempts })

      // Force logout if too many failed attempts
      if (currentAttempts >= MAX_TOKEN_ATTEMPTS) {
        console.error("🔴 Too many failed token refresh attempts, forcing logout...")
        handleLogout(authStore)
        return Promise.reject(new Error("Too many failed token attempts"))
      }

      // Prevent concurrent token refresh requests
      if (tokenRefreshInProgress) {
        console.warn("🔒 Token refresh already in progress, waiting...")
        return Promise.reject(new Error("Token refresh in progress"))
      }

      // Prevent rapid successive refresh attempts
      const now = Date.now()
      if (now - lastTokenRefreshTime < TOKEN_REFRESH_COOLDOWN) {
        console.warn("🔒 Token refresh cooldown active, skipping...")
        return Promise.reject(new Error("Token refresh cooldown active"))
      }

      // Prevent infinite refresh loops
      if (originalRequest._retry) {
        console.warn("🔒 Request already retried, giving up...")
        return Promise.reject(new Error("Request already retried"))
      }

      // Check if we have a refresh token before attempting refresh
      const refreshToken = storage.getString("refreshToken")
      if (!refreshToken) {
        console.warn("🔒 No refresh token found, logging out...")
        handleLogout(authStore)
        return Promise.reject(new Error("No refresh token available"))
      }

      originalRequest._retry = true
      tokenRefreshInProgress = true
      lastTokenRefreshTime = Date.now()

      try {
        const newToken = await authStore.refreshAuthToken()
        tokenRefreshInProgress = false

        if (newToken) {
          // Reset failed attempts on successful token refresh
          useAuthStore.setState({ failedTokenAttempts: 0 })

          originalRequest.headers.Authorization = `Bearer ${newToken}`
          return axiosInstance(originalRequest)
        } else {
          // If no token returned, logout and reject
          console.error("🔴 No token returned from refresh, logging out...")
          handleLogout(authStore)
          return Promise.reject(new Error("Token refresh returned no token"))
        }
      } catch (refreshError) {
        tokenRefreshInProgress = false
        console.error("🔴 Token refresh failed:", refreshError)

        // Handle specific error cases
        if (refreshError instanceof Error) {
          if (refreshError.message === "No refresh token found") {
            console.log("🔴 No refresh token available, ending session...")
            handleLogout(authStore)
            return Promise.reject(new Error("Session expired - no refresh token"))
          }
        }

        // For other refresh errors, also logout
        handleLogout(authStore)
        return Promise.reject(
          new Error(
            "Failed to retry request: " +
              (refreshError instanceof Error ? refreshError.message : String(refreshError)),
          ),
        )
      }
    }

    return Promise.reject(error)
  },
)

// Helper function to handle logout with proper flags
function handleLogout(authStore: any) {
  if (logoutInProgress) {
    console.log("🔒 Logout already in progress, skipping...")
    return
  }

  logoutInProgress = true

  // Use setTimeout to avoid blocking the current execution and prevent loops
  setTimeout(() => {
    try {
      authStore.logout()
    } finally {
      // Reset the flag after a delay to allow logout to complete
      setTimeout(() => {
        logoutInProgress = false
      }, 2000)
    }
  }, 0)
}

export default axiosInstance

import axiosInstance from "./axiosInstance"

/**
 * Generic fetcher function for SWR.
 * Supports headers, params, and different HTTP methods.
 */
const fetcher = async (url: string, method = "GET", body = null, headers = {}, params = {}) => {
  try {
    const response = await axiosInstance({
      url,
      method,
      data: body,
      headers,
      params,
    })
    return response.data
  } catch (error) {
    console.error("SWR Fetcher Error:", error)
    throw error // Allows SWR to properly handle errors
  }
}

export default fetcher

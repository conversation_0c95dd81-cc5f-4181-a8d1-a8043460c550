import { loadString, saveString } from "./storage"

// Storage key for first-time launch detection
const FIRST_TIME_LAUNCH_KEY = "hasSeenOnboarding"

/**
 * Check if this is the first time the app is being launched
 * @returns true if it's the first time, false otherwise
 */
export function isFirstTimeLaunch(): boolean {
  const hasSeenOnboarding = loadString(FIRST_TIME_LAUNCH_KEY)
  return hasSeenOnboarding === null || hasSeenOnboarding !== "true"
}

/**
 * Mark onboarding as completed
 * This should be called when the user completes the onboarding flow
 */
export function markOnboardingCompleted(): boolean {
  return saveString(FIRST_TIME_LAUNCH_KEY, "true")
}

/**
 * Reset onboarding state (useful for testing or debugging)
 * This will make the app show onboarding again on next launch
 */
export function resetOnboardingState(): void {
  try {
    const { remove } = require("./storage")
    remove(FIRST_TIME_LAUNCH_KEY)
  } catch (error) {
    console.warn("Failed to reset onboarding state:", error)
  }
}

/**
 * Get the current onboarding state
 * @returns "completed" | "pending"
 */
export function getOnboardingState(): "completed" | "pending" {
  return isFirstTimeLaunch() ? "pending" : "completed"
}

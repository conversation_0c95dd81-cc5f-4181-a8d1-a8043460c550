// export const showTransferNotification = async (message: string, type: "success" | "failed") => {
//   try {
//     await notifee.displayNotification({
//       title: type === "success" ? "Transaction Réussie" : "Échec de la Transaction",
//       body: message,
//       android: {
//         channelId: "transfers",
//         smallIcon: type === "success" ? "ic_success" : "ic_error",
//         color: type === "success" ? "#4CAF50" : "#F44336",
//         pressAction: {
//           id: "default",
//         },
//       },
//       ios: {
//         categoryId: "transfers",
//       },
//     })
//   } catch (error) {
//     console.error("Failed to show notification:", error)
//   }
// }

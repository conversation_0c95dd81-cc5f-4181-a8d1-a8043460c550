import { Alert } from "react-native"

interface ApiError {
  response?: {
    status?: number
    data?: {
      message?: string
      errors?: Record<string, string[]>
      detail?: string
    }
  }
  request?: any
  message?: string
}

interface ErrorHandlerOptions {
  title?: string
  onClose?: () => void
  onUnauthorized?: () => void
}

// Helper function to check if an error message is user-friendly
const isUserFriendlyMessage = (message: string): boolean => {
  if (!message) return false

  // Check for technical error patterns that shouldn't be shown to users
  const technicalPatterns = [
    /internal server error/i,
    /database error/i,
    /connection timeout/i,
    /null pointer/i,
    /undefined/i,
    /stack trace/i,
    /exception/i,
    /error code/i,
    /sql/i,
    /server error/i,
    /500/,
    /502/,
    /503/,
    /504/,
  ]

  // Check for user-friendly patterns that are safe to show
  const userFriendlyPatterns = [
    /solde insuffisant/i,
    /montant invalide/i,
    /numéro invalide/i,
    /compte introuvable/i,
    /limite dépassée/i,
    /mot de passe incorrect/i,
    /utilisateur non trouvé/i,
    /transaction échouée/i,
    /service temporairement/i,
    /vérifiez/i,
    /invalide/i,
    /incorrect/i,
    /expiré/i,
    /bloqué/i,
  ]

  // If message contains technical patterns, it's not user-friendly
  if (technicalPatterns.some((pattern) => pattern.test(message))) {
    return false
  }

  // If message contains user-friendly patterns, it's safe to show
  if (userFriendlyPatterns.some((pattern) => pattern.test(message))) {
    return true
  }

  // If message is short and doesn't contain technical terms, it's probably safe
  return message.length < 100 && !message.includes("Error") && !message.includes("error")
}

export const handleApiError = (error: ApiError, options: ErrorHandlerOptions = {}): string => {
  const { title = "Erreur", onClose, onUnauthorized } = options

  let errorMessage = "Une erreur s'est produite"

  if (error.response) {
    // Server responded with error
    const { status, data } = error.response

    switch (status) {
      case 400:
        // For 400 errors, be selective about which messages to show
        if (data?.message && isUserFriendlyMessage(data.message)) {
          errorMessage = data.message
        } else if (data?.errors) {
          const errorMessages = Object.values(data.errors).flat()
          const userFriendlyErrors = errorMessages.filter((msg) => isUserFriendlyMessage(msg))
          if (userFriendlyErrors.length > 0) {
            errorMessage = userFriendlyErrors.join("\n")
          } else {
            errorMessage =
              "Les informations fournies sont invalides. Veuillez vérifier vos données."
          }
        } else if (data?.detail && isUserFriendlyMessage(data.detail)) {
          errorMessage = data.detail
        } else {
          errorMessage = "Les informations fournies sont invalides. Veuillez vérifier vos données."
        }
        // Log technical details for debugging
        console.error(`Bad request (400):`, data)
        break

      case 401:
        errorMessage = "Session expirée. Veuillez vous reconnecter."
        if (onUnauthorized) {
          onUnauthorized()
        }
        break

      case 403:
        errorMessage = "Vous n'êtes pas autorisé à effectuer cette opération."
        break

      case 404:
        errorMessage = "La ressource demandée n'a pas été trouvée."
        break

      case 422:
        // For validation errors, check if the message is user-friendly
        if (data?.message && isUserFriendlyMessage(data.message)) {
          errorMessage = data.message
        } else if (data?.detail && isUserFriendlyMessage(data.detail)) {
          errorMessage = data.detail
        } else {
          errorMessage =
            "Les données fournies ne sont pas valides. Veuillez vérifier vos informations."
        }
        console.error(`Validation error (422):`, data)
        break

      case 429:
        errorMessage = "Trop de tentatives. Veuillez patienter quelques minutes."
        break

      case 500:
      case 502:
      case 503:
      case 504:
        // Never show server error details to users
        errorMessage = "Le service est momentanément indisponible. Veuillez réessayer plus tard."
        console.error(`Server error (${status}):`, data)
        break

      default:
        // For other status codes, be very careful about showing messages
        if (data?.message && isUserFriendlyMessage(data.message)) {
          errorMessage = data.message
        } else if (data?.detail && isUserFriendlyMessage(data.detail)) {
          errorMessage = data.detail
        } else {
          errorMessage = "Une erreur inattendue s'est produite. Veuillez réessayer."
        }
        console.error(`HTTP error (${status}):`, data)
    }
  } else if (error.request) {
    // Network error
    errorMessage = "Impossible de joindre le serveur. Vérifiez votre connexion internet."
    console.error("Network error:", error.request)
  } else if (error.message) {
    // Other errors - never expose raw error messages to users
    if (isUserFriendlyMessage(error.message)) {
      errorMessage = error.message
    } else {
      errorMessage = "Une erreur inattendue s'est produite. Veuillez réessayer."
    }
    console.error("Unexpected error:", error.message)
  }

  Alert.alert(
    title,
    errorMessage,
    [
      {
        text: "OK",
        onPress: onClose,
      },
    ],
    { cancelable: false },
  )

  return errorMessage
}

// Helper function to get a user-friendly error message without showing alerts
export const getErrorMessage = (error: ApiError): string => {
  if (error.response) {
    const { status, data } = error.response

    switch (status) {
      case 400:
        if (data?.message && isUserFriendlyMessage(data.message)) {
          return data.message
        } else if (data?.detail && isUserFriendlyMessage(data.detail)) {
          return data.detail
        }
        return "Les informations fournies sont invalides. Veuillez vérifier vos données."

      case 401:
        return "Session expirée. Veuillez vous reconnecter."

      case 403:
        return "Vous n'êtes pas autorisé à effectuer cette opération."

      case 404:
        return "La ressource demandée n'a pas été trouvée."

      case 422:
        if (data?.message && isUserFriendlyMessage(data.message)) {
          return data.message
        } else if (data?.detail && isUserFriendlyMessage(data.detail)) {
          return data.detail
        }
        return "Les données fournies ne sont pas valides. Veuillez vérifier vos informations."

      case 429:
        return "Trop de tentatives. Veuillez patienter quelques minutes."

      case 500:
      case 502:
      case 503:
      case 504:
        return "Le service est momentanément indisponible. Veuillez réessayer plus tard."

      default:
        if (data?.message && isUserFriendlyMessage(data.message)) {
          return data.message
        } else if (data?.detail && isUserFriendlyMessage(data.detail)) {
          return data.detail
        }
        return "Une erreur inattendue s'est produite. Veuillez réessayer."
    }
  } else if (error.request) {
    return "Impossible de joindre le serveur. Vérifiez votre connexion internet."
  } else if (error.message && isUserFriendlyMessage(error.message)) {
    return error.message
  }

  return "Une erreur inattendue s'est produite. Veuillez réessayer."
}

// Custom error messages for specific features
export const ERROR_MESSAGES = {
  TRANSFER: {
    INVALID_AMOUNT: "Le montant saisi est invalide.",
    INSUFFICIENT_FUNDS: "Solde insuffisant pour effectuer cette transaction.",
    INVALID_RECIPIENT: "Le destinataire spécifié est invalide.",
    DAILY_LIMIT_EXCEEDED: "Limite quotidienne de transfert dépassée.",
    NETWORK_ERROR: "Impossible de joindre le serveur. Vérifiez votre connexion internet.",
    SERVER_ERROR: "Le service de transfert est temporairement indisponible.",
  },
  AUTH: {
    INVALID_CREDENTIALS: "Identifiants invalides.",
    ACCOUNT_LOCKED: "Compte temporairement bloqué.",
    SESSION_EXPIRED: "Votre session a expiré.",
    NETWORK_ERROR: "Impossible de joindre le serveur. Vérifiez votre connexion internet.",
    SERVER_ERROR: "Le service d'authentification est temporairement indisponible.",
  },
  PAYMENT: {
    INVALID_AMOUNT: "Le montant saisi est invalide.",
    INSUFFICIENT_FUNDS: "Solde insuffisant pour effectuer ce paiement.",
    PAYMENT_FAILED: "Le paiement a échoué. Veuillez réessayer.",
    SERVICE_UNAVAILABLE: "Le service de paiement est temporairement indisponible.",
    NETWORK_ERROR: "Impossible de joindre le serveur. Vérifiez votre connexion internet.",
  },
  BUSINESS: {
    INVALID_DATA: "Les informations de l'entreprise sont invalides.",
    CREATION_FAILED: "La création de l'entreprise a échoué. Veuillez réessayer.",
    NETWORK_ERROR: "Impossible de joindre le serveur. Vérifiez votre connexion internet.",
    SERVER_ERROR: "Le service entreprise est temporairement indisponible.",
  },
  GENERAL: {
    NETWORK_ERROR: "Erreur de connexion. Vérifiez votre connexion internet.",
    SERVER_ERROR: "Le service est temporairement indisponible.",
    VALIDATION_ERROR: "Veuillez vérifier les informations saisies.",
    UNKNOWN_ERROR: "Une erreur inattendue s'est produite. Veuillez réessayer.",
  },
}

// Helper function to check if an error is a server error (5xx)
export const isServerError = (error: ApiError): boolean => {
  return error.response?.status ? error.response.status >= 500 : false
}

// Helper function to check if an error is a client error (4xx)
export const isClientError = (error: ApiError): boolean => {
  const status = error.response?.status
  return status ? status >= 400 && status < 500 : false
}

// Helper function to check if an error is a network error
export const isNetworkError = (error: ApiError): boolean => {
  return !error.response && !!error.request
}

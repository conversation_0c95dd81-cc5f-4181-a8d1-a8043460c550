import { Alert } from "react-native"

interface ApiError {
  response?: {
    status?: number
    data?: {
      message?: string
      errors?: Record<string, string[]>
      detail?: string
    }
  }
  request?: any
  message?: string
}

interface ErrorHandlerOptions {
  title?: string
  onClose?: () => void
  onUnauthorized?: () => void
}

export const handleApiError = (error: ApiError, options: ErrorHandlerOptions = {}): string => {
  const { title = "Erreur", onClose, onUnauthorized } = options

  let errorMessage = "Une erreur s'est produite"

  if (error.response) {
    // Server responded with error
    const { status, data } = error.response

    switch (status) {
      case 400:
        if (data?.message) {
          errorMessage = data.message
        } else if (data?.errors) {
          errorMessage = Object.values(data.errors).flat().join("\n")
        } else if (data?.detail) {
          errorMessage = data.detail
        } else {
          errorMessage = "Les informations fournies sont invalides. Veuillez vérifier vos données."
        }
        break
      case 401:
        errorMessage = "Session expirée. Veuillez vous reconnecter."
        if (onUnauthorized) {
          onUnauthorized()
        }
        break
      case 403:
        errorMessage = "Vous n'êtes pas autorisé à effectuer cette opération."
        break
      case 404:
        errorMessage = "La ressource demandée n'a pas été trouvée."
        break
      case 422:
        errorMessage = "Montant invalide ou insuffisant."
        break
      case 429:
        errorMessage = "Trop de tentatives. Veuillez patienter quelques minutes."
        break
      case 500:
      case 502:
      case 503:
      case 504:
        // Generic message for all server errors
        errorMessage = "Le service est momentanément indisponible. Veuillez réessayer plus tard."
        // Log the actual error for debugging
        console.error(`Server error (${status}):`, data)
        break
      default:
        // For other status codes, show a generic message if no specific message is provided
        errorMessage = data?.message || "Une erreur inattendue s'est produite."
    }
  } else if (error.request) {
    // Network error
    errorMessage = "Impossible de joindre le serveur. Vérifiez votre connexion internet."
  } else if (error.message) {
    // Other errors - don't expose raw error messages to users
    errorMessage = "Une erreur inattendue s'est produite. Veuillez réessayer."
    // Log the actual error for debugging
    console.error("Unexpected error:", error.message)
  }

  Alert.alert(
    title,
    errorMessage,
    [
      {
        text: "OK",
        onPress: onClose,
      },
    ],
    { cancelable: false },
  )

  return errorMessage
}

// Custom error messages for specific features
export const ERROR_MESSAGES = {
  TRANSFER: {
    INVALID_AMOUNT: "Le montant saisi est invalide.",
    INSUFFICIENT_FUNDS: "Solde insuffisant pour effectuer cette transaction.",
    INVALID_RECIPIENT: "Le destinataire spécifié est invalide.",
    DAILY_LIMIT_EXCEEDED: "Limite quotidienne de transfert dépassée.",
  },
  AUTH: {
    INVALID_CREDENTIALS: "Identifiants invalides.",
    ACCOUNT_LOCKED: "Compte temporairement bloqué.",
    SESSION_EXPIRED: "Votre session a expiré.",
  },
  GENERAL: {
    NETWORK_ERROR: "Erreur de connexion. Vérifiez votre connexion internet.",
    SERVER_ERROR: "Le service est temporairement indisponible.",
    VALIDATION_ERROR: "Veuillez vérifier les informations saisies.",
  },
}

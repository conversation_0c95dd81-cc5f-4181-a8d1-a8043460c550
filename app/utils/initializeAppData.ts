import { useAppSettings } from "@/store/AppSettingsStore"

/**
 * Initialize app data that should be loaded only once
 * This function fetches currencies and other global data
 */
export const initializeAppData = async () => {
  // Get the store methods
  const { fetchCurrencies, currencies } = useAppSettings.getState()
  
  // Only fetch currencies if they haven't been loaded yet
  if (!currencies || currencies.length === 0) {
    await fetchCurrencies()
  }
}

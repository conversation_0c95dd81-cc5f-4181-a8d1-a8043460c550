import * as ImageManipulator from "expo-image-manipulator"
import { Alert } from "react-native"

export interface CompressionOptions {
  maxWidth?: number
  maxHeight?: number
  quality?: number
  format?: ImageManipulator.SaveFormat
}

export interface CompressionResult {
  success: boolean
  uri?: string
  originalSize?: number
  compressedSize?: number
  compressionRatio?: number
  error?: string
}

/**
 * Compress and resize an image to avoid 413 Request Entity Too Large errors
 * 
 * @param imageUri - The URI of the image to compress
 * @param options - Compression options
 * @returns Promise<CompressionResult>
 */
export const compressImage = async (
  imageUri: string,
  options: CompressionOptions = {}
): Promise<CompressionResult> => {
  try {
    console.log("🖼️ Starting image compression...")
    console.log("📁 Original image URI:", imageUri)

    // Default compression options
    const {
      maxWidth = 1024,      // Max width in pixels
      maxHeight = 1024,     // Max height in pixels  
      quality = 0.7,        // Quality (0.1 to 1.0)
      format = ImageManipulator.SaveFormat.JPEG
    } = options

    // Get original image info
    const originalInfo = await ImageManipulator.manipulateAsync(
      imageUri,
      [],
      { format: ImageManipulator.SaveFormat.JPEG }
    )

    console.log("📊 Original image info:", {
      width: originalInfo.width,
      height: originalInfo.height,
      uri: originalInfo.uri
    })

    // Calculate original file size (approximate)
    const originalSize = await getFileSizeFromUri(originalInfo.uri)
    console.log("📏 Original file size:", formatFileSize(originalSize))

    // Determine if resizing is needed
    const needsResize = originalInfo.width > maxWidth || originalInfo.height > maxHeight

    // Calculate new dimensions while maintaining aspect ratio
    let newWidth = originalInfo.width
    let newHeight = originalInfo.height

    if (needsResize) {
      const aspectRatio = originalInfo.width / originalInfo.height
      
      if (originalInfo.width > originalInfo.height) {
        // Landscape orientation
        newWidth = Math.min(maxWidth, originalInfo.width)
        newHeight = newWidth / aspectRatio
      } else {
        // Portrait orientation
        newHeight = Math.min(maxHeight, originalInfo.height)
        newWidth = newHeight * aspectRatio
      }

      // Ensure dimensions don't exceed limits
      if (newWidth > maxWidth) {
        newWidth = maxWidth
        newHeight = newWidth / aspectRatio
      }
      if (newHeight > maxHeight) {
        newHeight = maxHeight
        newWidth = newHeight * aspectRatio
      }
    }

    console.log("🎯 Target dimensions:", {
      width: Math.round(newWidth),
      height: Math.round(newHeight),
      needsResize
    })

    // Prepare manipulation actions
    const actions: ImageManipulator.Action[] = []

    if (needsResize) {
      actions.push({
        resize: {
          width: Math.round(newWidth),
          height: Math.round(newHeight),
        },
      })
    }

    // Compress the image
    const compressedResult = await ImageManipulator.manipulateAsync(
      imageUri,
      actions,
      {
        compress: quality,
        format: format,
      }
    )

    // Get compressed file size
    const compressedSize = await getFileSizeFromUri(compressedResult.uri)
    const compressionRatio = originalSize > 0 ? (originalSize - compressedSize) / originalSize : 0

    console.log("✅ Image compression completed!")
    console.log("📊 Compression results:", {
      originalSize: formatFileSize(originalSize),
      compressedSize: formatFileSize(compressedSize),
      compressionRatio: `${(compressionRatio * 100).toFixed(1)}%`,
      newDimensions: `${compressedResult.width}x${compressedResult.height}`
    })

    return {
      success: true,
      uri: compressedResult.uri,
      originalSize,
      compressedSize,
      compressionRatio,
    }

  } catch (error) {
    console.error("❌ Image compression failed:", error)
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown compression error",
    }
  }
}

/**
 * Get file size from URI (approximate)
 */
const getFileSizeFromUri = async (uri: string): Promise<number> => {
  try {
    // For local files, we can't get exact size easily
    // This is an approximation based on image dimensions and format
    const response = await fetch(uri)
    const blob = await response.blob()
    return blob.size
  } catch (error) {
    console.warn("⚠️ Could not determine file size:", error)
    return 0
  }
}

/**
 * Format file size for display
 */
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "Unknown"
  
  const k = 1024
  const sizes = ["Bytes", "KB", "MB", "GB"]
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${sizes[i]}`
}

/**
 * Compress image with different quality levels based on original size
 */
export const smartCompress = async (imageUri: string): Promise<CompressionResult> => {
  try {
    // First, try to get approximate file size
    const originalSize = await getFileSizeFromUri(imageUri)
    console.log("🧠 Smart compression - Original size:", formatFileSize(originalSize))

    // Determine compression strategy based on file size
    let compressionOptions: CompressionOptions

    if (originalSize > 5 * 1024 * 1024) {
      // > 5MB - Aggressive compression
      compressionOptions = {
        maxWidth: 800,
        maxHeight: 800,
        quality: 0.5,
        format: ImageManipulator.SaveFormat.JPEG
      }
      console.log("🔥 Using aggressive compression (>5MB)")
    } else if (originalSize > 2 * 1024 * 1024) {
      // > 2MB - Medium compression
      compressionOptions = {
        maxWidth: 1024,
        maxHeight: 1024,
        quality: 0.6,
        format: ImageManipulator.SaveFormat.JPEG
      }
      console.log("⚡ Using medium compression (>2MB)")
    } else if (originalSize > 1 * 1024 * 1024) {
      // > 1MB - Light compression
      compressionOptions = {
        maxWidth: 1200,
        maxHeight: 1200,
        quality: 0.7,
        format: ImageManipulator.SaveFormat.JPEG
      }
      console.log("💨 Using light compression (>1MB)")
    } else {
      // < 1MB - Minimal compression
      compressionOptions = {
        maxWidth: 1500,
        maxHeight: 1500,
        quality: 0.8,
        format: ImageManipulator.SaveFormat.JPEG
      }
      console.log("🌟 Using minimal compression (<1MB)")
    }

    return await compressImage(imageUri, compressionOptions)

  } catch (error) {
    console.error("❌ Smart compression failed:", error)
    // Fallback to standard compression
    return await compressImage(imageUri, {
      maxWidth: 1024,
      maxHeight: 1024,
      quality: 0.7
    })
  }
}

/**
 * Show compression results to user
 */
export const showCompressionResults = (result: CompressionResult) => {
  if (result.success && result.originalSize && result.compressedSize) {
    const savedSpace = result.originalSize - result.compressedSize
    const compressionPercent = (result.compressionRatio! * 100).toFixed(1)
    
    Alert.alert(
      "Image Compressed",
      `Original: ${formatFileSize(result.originalSize)}\n` +
      `Compressed: ${formatFileSize(result.compressedSize)}\n` +
      `Saved: ${formatFileSize(savedSpace)} (${compressionPercent}%)`,
      [{ text: "OK" }]
    )
  }
}

/**
 * Validate if image is suitable for upload (under size limit)
 */
export const validateImageSize = async (imageUri: string, maxSizeMB: number = 10): Promise<boolean> => {
  try {
    const fileSize = await getFileSizeFromUri(imageUri)
    const maxSizeBytes = maxSizeMB * 1024 * 1024
    
    console.log("🔍 Image validation:", {
      fileSize: formatFileSize(fileSize),
      maxSize: formatFileSize(maxSizeBytes),
      isValid: fileSize <= maxSizeBytes
    })
    
    return fileSize <= maxSizeBytes
  } catch (error) {
    console.error("❌ Image validation failed:", error)
    return false
  }
}

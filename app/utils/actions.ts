import { colors } from "@/theme"

export const getProgressDetails = (status: string | undefined, userData: any) => {
  let progress = 0
  let color: string = colors.palette.primary500

  switch (status) {
    case "not_started":
      progress = 0.1
      break
    case "pending":
      progress = 0.5
      break
    case "verified":
      progress = 1.0
      break
    case "failed":
      progress = 0.1
      color = colors.palette.angry100
      break
    case "blocked":
      progress = 0.0
      color = colors.palette.angry500
      break
    default:
      progress = 0.0
      break
  }

  // Check if basic info is verified (Add 25%)
  if (userData?.is_info_verified) {
    progress += 0.3
  }

  // Check if IdentityDocument is complete (Add 20%)
  const identityDoc = userData?.IdentityDocument
  if (identityDoc) {
    const requiredFields = [
      "date_expiration",
      "date_issue",
      "file_type",
      "lieu_de_delivrance",
      "numero_de_piece",
      "type_piece",
    ]

    const isDocumentComplete = requiredFields.every((field) => identityDoc[field])
    if (isDocumentComplete) {
      progress += 0.4
    }
  }

  // Ensure progress is within bounds (max 1.0)
  progress = Math.min(progress, 1.0)

  return { progress, color }
}

export const getGreeting = () => {
  const currentHour = new Date().getHours() // Gets the hour in 24-hour format

  // Greetings based on the time of day in Congolese context
  if (currentHour >= 5 && currentHour < 12) {
    return "Bonjour" // Morning greeting (5 AM - 12 PM)
  } else if (currentHour >= 12 && currentHour < 17) {
    return "Bon après-midi" // Afternoon greeting (12 PM - 5 PM)
  } else if (currentHour >= 17 && currentHour < 21) {
    return "Bonsoir" // Evening greeting (5 PM - 9 PM)
  } else {
    return "Bonsoir" // Night greeting (9 PM - 5 AM)
  }
}

export const StatusColor = (icontype: any) => {
  const ico =
    icontype === "completed"
      ? colors.palette.primary600
      : icontype === "pending"
        ? colors.palette.secondary200
        : icontype === "failed"
          ? colors.palette.angry500
          : ""

  return ico
}

export const StatusIcon = (icontype: any) => {
  const ico =
    icontype === "failed"
      ? "faiald"
      : icontype === "pending"
        ? "loading"
        : icontype === "completed"
          ? "Rcheck"
          : ""
  return ico
}

export const statusTranslations: Record<string, { label: string; color: string; icon: string }> = {
  active: { label: "Activé", color: colors.palette.neutral300, icon: "Rcheck" }, // Green for active
  blocked: { label: "Bloqué", color: "#F88383", icon: "x" }, // Red for blocked
  hold: { label: "En attente", color: "#FFD2A6", icon: "loading" }, // Yellow for on hold
}

type StatusType = "completed" | "pending" | "failed" | "default"

export const getStatusConfig = (status: string) => {
  const statusMap = {
    completed: {
      color: colors.palette.primary100,
      backgroundColor: colors.palette.primary200, // 20 is for opacity
      label: "Complété",
    },
    pending: {
      color: colors.palette.secondary100,
      backgroundColor: colors.palette.accent400,
      label: "En cours",
    },
    failed: {
      color: colors.palette.angry500,
      backgroundColor: colors.palette.accent400,
      label: "Échoué",
    },
    default: {
      color: colors.palette.neutral800,
      backgroundColor: colors.palette.neutral200,
      label: "Inconnu",
    },
  } as const

  const normalizedStatus = (status?.toLowerCase() || "default") as StatusType
  return statusMap[normalizedStatus] || statusMap.default
}

// Define valid icon types to ensure type safety
type ValidIconType =
  | "Rcheck" // Completed/Success
  | "loading" // Pending/Processing
  | "x" // Cancel/Reject/Failed
  | "info" // Unknown/Default
  | "fedhapochi" // Fedha Pochi service
  | "cashpower" // SNEL DRC service
  | "airtel" // Airtel service
  | "transfer" // Transfer service
  | "topup" // Top-up service
  | "withdrawal" // Withdrawal service

// Function to get the appropriate icon based on service and status
export const getStatusIcon = (
  status: string | undefined,
  serviceName?: string,
): { icon: ValidIconType; color: string } => {
  // If we have a service name, prioritize showing service-specific icons
  if (serviceName) {
    switch (serviceName.toLowerCase()) {
      case "fedhapochi":
        return { icon: "fedhapochi", color: colors.palette.neutral900 }
      case "sneldrc":
        return { icon: "cashpower", color: colors.palette.neutral900 }
      case "airtel":
        return { icon: "airtel", color: colors.palette.angry500 }
      case "transfer":
        return { icon: "transfer", color: colors.palette.primary500 }
      case "topup":
        return { icon: "topup", color: colors.palette.primary500 }
      case "withdrawal":
        return { icon: "withdrawal", color: colors.palette.primary500 }
    }
  }

  // If no service name or not a recognized service, use status-based icons
  // This will show different icons based on the status (completed, pending, failed)
  if (status) {
    return getStatusOnlyIcon(status)
  }

  // Default fallback for unknown services and statuses
  return { icon: "info", color: colors.palette.neutral400 }
}

// Create a separate function for status-only icons if needed
export const getStatusOnlyIcon = (status: string): { icon: ValidIconType; color: string } => {
  // Normalize the status to lowercase and handle null/undefined
  const normalizedStatus = (status || "").toLowerCase()

  switch (normalizedStatus) {
    // Completed/Success states
    case "completed":
    case "success":
    case "approved":
    case "confirmed":
      return { icon: "Rcheck", color: colors.palette.primary500 }

    // Pending/Processing states
    case "in_progress":
    case "pending":
    case "processing":
    case "waiting":
      return { icon: "loading", color: colors.palette.secondary100 }

    // Failed/Error states
    case "failed":
    case "error":
    case "rejected":
    case "canceled":
    case "cancelled":
      return { icon: "x", color: colors.palette.angry500 }

    // Default case for unknown status
    default:
      console.log(`Unknown status: ${status}`)
      return { icon: "info", color: colors.palette.neutral400 }
  }
}

export const getNameInitials = (firstName?: string, lastName?: string): string => {
  const first = firstName?.[0] || ""
  const last = lastName?.[0] || ""
  return (first + last).toUpperCase()
}

export const capitalizeFirstLetter = (str?: string): string => {
  if (!str) return ""
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase()
}

export const capitalizeAllLetters = (str?: string): string => {
  if (!str) return ""
  return str.toUpperCase()
}

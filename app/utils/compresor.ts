import * as ImageManipulator from "expo-image-manipulator"

export const compressImage = async (
  uri: string,
  options = {
    maxWidth: 1024,
    quality: 0.7,
  },
): Promise<string> => {
  try {
    const result = await ImageManipulator.manipulateAsync(
      uri,
      [{ resize: { width: options.maxWidth } }],
      {
        compress: options.quality,
        format: ImageManipulator.SaveFormat.JPEG,
      },
    )
    return result.uri
  } catch (error) {
    console.error("Image compression failed:", error)
    return uri // Return original URI if compression fails
  }
}

export const createCompressedFormData = async (
  originalUri: string,
  fieldName: string = "piece_identite",
  fileName: string = "document.jpg",
): Promise<FormData> => {
  const compressedUri = await compressImage(originalUri)

  const formData = new FormData()
  formData.append(fieldName, {
    uri: compressedUri,
    type: "image/jpeg",
    name: fileName,
  } as any)

  return formData
}

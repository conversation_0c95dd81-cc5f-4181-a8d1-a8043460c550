/**
 * Utility functions for Sentry integration
 */
import * as Sentry from "@sentry/react-native"

/**
 * Set user information in Sentry
 * @param user User object with id, email, and other optional properties
 */
export const setSentryUser = (user: {
  id: string
  email?: string
  username?: string
  [key: string]: any
}) => {
  if (!user) return

  // Set user context in Sentry
  Sentry.setUser({
    id: user.id,
    email: user.email,
    username: user.username,
    // Add any other user properties you want to track
  })
}

/**
 * Clear user information from Sentry (e.g., on logout)
 */
export const clearSentryUser = () => {
  Sentry.setUser(null)
}

/**
 * Set extra context information in Sentry
 * @param context Object with extra context information
 */
export const setSentryContext = (context: Record<string, any>) => {
  if (!context) return

  // Set extra context in Sentry
  Sentry.setContext("app", context)
}

/**
 * Manually capture an exception in Sentry
 * @param error Error object
 * @param context Additional context information
 */
export const captureException = (error: Error, context?: Record<string, any>) => {
  Sentry.captureException(error, {
    extra: context,
  })
}

/**
 * Manually capture a message in Sentry
 * @param message Message to capture
 * @param level Severity level
 */
export const captureMessage = (
  message: string,
  level: "fatal" | "error" | "warning" | "info" | "debug" = "info"
) => {
  Sentry.captureMessage(message, level)
}

/**
 * Start a performance transaction
 * @param name Transaction name
 * @param operation Operation type
 * @returns Transaction object
 */
export const startTransaction = (name: string, operation: string) => {
  return Sentry.startTransaction({
    name,
    op: operation,
  })
}

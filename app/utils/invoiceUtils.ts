import { Invoice } from "@/store"

// Define the summary interface
export interface InvoiceSummary {
  // Count by status
  pendingCount: number
  completedCount: number
  canceledCount: number
  
  // Total amounts by status
  pendingAmount: number
  completedAmount: number
  canceledAmount: number
  
  // Overall totals
  totalCount: number
  totalAmount: number
  
  // Currency (assuming all invoices use the same currency)
  currency: string
}

/**
 * Get a summary of invoices grouped by status with amount totals
 * @param invoices Array of invoices to summarize
 * @returns Summary object with counts and amounts by status
 */
export const getSummary = (invoices: Invoice[] | Invoice): InvoiceSummary => {
  // Initialize the summary object
  const summary: InvoiceSummary = {
    pendingCount: 0,
    completedCount: 0,
    canceledCount: 0,
    pendingAmount: 0,
    completedAmount: 0,
    canceledAmount: 0,
    totalCount: 0,
    totalAmount: 0,
    currency: "FC", // Default currency
  };
  
  // Handle case where invoices is a single invoice or nested array
  let invoiceArray: Invoice[] = [];
  
  if (Array.isArray(invoices)) {
    // Check if it's a nested array (like in your example)
    if (invoices.length > 0 && Array.isArray(invoices[0])) {
      invoiceArray = invoices[0] as unknown as Invoice[];
    } else {
      invoiceArray = invoices;
    }
  } else if (invoices) {
    // Single invoice object
    invoiceArray = [invoices];
  }
  
  // Set the total count
  summary.totalCount = invoiceArray.length;
  
  // Process each invoice
  invoiceArray.forEach(invoice => {
    // Parse the amount
    let amount = 0;
    if (invoice.amount) {
      // Remove commas and convert to number
      amount = parseFloat(invoice.amount.replace(/,/g, ""));
    } else if (invoice.total_amount) {
      amount = typeof invoice.total_amount === "number" 
        ? invoice.total_amount 
        : parseFloat(String(invoice.total_amount).replace(/,/g, ""));
    }
    
    // Set currency (use the first invoice's currency)
    if (invoice.currency && summary.currency === "FC") {
      summary.currency = typeof invoice.currency === "number"
        ? invoice.currency === 1 ? "USD" : "FC"
        : String(invoice.currency);
    }
    
    // Add to total amount
    summary.totalAmount += amount;
    
    // Categorize by status
    const status = (invoice.status || "").toLowerCase();
    
    if (["completed", "paid", "success"].includes(status)) {
      summary.completedCount++;
      summary.completedAmount += amount;
    } else if (["pending", "processing", "in_progress"].includes(status)) {
      summary.pendingCount++;
      summary.pendingAmount += amount;
    } else if (["canceled", "cancelled", "failed", "rejected"].includes(status)) {
      summary.canceledCount++;
      summary.canceledAmount += amount;
    } else {
      // Default to pending for unknown statuses
      summary.pendingCount++;
      summary.pendingAmount += amount;
    }
  });
  
  return summary;
};

/**
 * Format a number as currency
 * @param amount Number to format
 * @param currency Currency code
 * @returns Formatted currency string
 */
export const formatCurrency = (amount: number, currency: string = "FC"): string => {
  return `${amount.toLocaleString()} ${currency}`;
};

import { Linking } from "react-native"

export const DRCprovinces = [
  { label: "Kinshasa", value: "Kinshasa" },
  { label: "Kongo-Central", value: "Kongo-Central" },
  { label: "Kwilu", value: "Kwilu" },
  { label: "Kwango", value: "Kwango" },
  { label: "Mai-Ndombe", value: "Mai-Ndombe" },
  { label: "Tshuapa", value: "Tshuapa" },
  { label: "Mongala", value: "Mongala" },
  { label: "Nord-Ubangi", value: "Nord-Ubangi" },
  { label: "Sud-Ubangi", value: "Sud-Ubangi" },
  { label: "Équateur", value: "Équateur" },
  { label: "Haut-Uélé", value: "Haut-Uélé" },
  { label: "Bas-Uélé", value: "Bas-Uélé" },
  { label: "Tshopo", value: "Tshopo" },
  { label: "Ituri", value: "Ituri" },
  { label: "Haut-Loma<PERSON>", value: "Haut-Loma<PERSON>" },
  { label: "<PERSON><PERSON><PERSON>", value: "<PERSON><PERSON><PERSON>" },
  { label: "Kasa<PERSON>", value: "Kasa<PERSON>" },
  { label: "Kasaï-Central", value: "Kasaï-Central" },
  { label: "Kasaï-Oriental", value: "Kasaï-Oriental" },
  { label: "Maniema", value: "Maniema" },
  { label: "Tanganyika", value: "Tanganyika" },
  { label: "Haut-Katanga", value: "Haut-Katanga" },
  { label: "Lualaba", value: "Lualaba" },
  { label: "Sud-Kivu", value: "Sud-Kivu" },
  { label: "Nord-Kivu", value: "Nord-Kivu" },
]

export const walletImages: Record<string, any> = {
  FedhaPochi: require("../../assets/icons/fedhapochi.png"),
  MPesa: require("../../assets/icons/mpesa.png"),
  orangemoney: require("../../assets/icons/orangemoney.png"),
  airtelmoney: require("../../assets/icons/airtel.png"),
  default: require("../../assets/icons/fedhapochi.png"),
}

// Dummy data for FAQ categories
export const faqCategories = ["General", "Account", "Payment", "Service", "Security", "Fees"]

// Dummy data for FAQs (Add more relevant questions/answers)
export const faqData = [
  {
    id: "faq1",
    question: "How do I manage my notifications?",
    answer:
      "To manage notifications, go to 'Settings', select 'Notification settings', and customize your preferences.",
    category: "Account",
  },
  {
    id: "faq2",
    question: "How do I start a guided meditation session?",
    answer:
      "This seems like placeholder text from the image template. Replace with actual relevant answers for your app.",
    category: "General",
  },
  {
    id: "faq3",
    question: "How do I join a support group?",
    answer: "Details about joining support groups or communities would go here.",
    category: "General",
  },
  {
    id: "faq4",
    question: "Is my data safe and private?",
    answer:
      "We prioritize your data security and privacy. You can find more details in our Privacy Policy.",
    category: "Security",
  },
  {
    id: "faq5",
    question: "What are the transaction fees?",
    answer: "Information about transaction fees can be found on our pricing page or fees section.",
    category: "Fees",
  },
]

const openWhatsApp = () => {
  // Format the message
  const message = `Bonjour, j'ai besoin d'aide`

  // Replace with your actual WhatsApp number
  const phoneNumber = "+************"

  // Create WhatsApp URL with encoded message
  const url = `https://wa.me/${phoneNumber.replace("+", "")}?text=${encodeURIComponent(message)}`

  Linking.openURL(url)
    .then((supported) => {
      if (supported) {
        return Linking.openURL(url)
      } else {
        alert("WhatsApp n'est pas installé sur votre appareil")
        return Promise.resolve() // Explicitly return a resolved promise
      }
    })
    .catch((err) => console.error("An error occurred", err))
}

// Dummy data for Contact Methods
export const contactMethods = [
  {
    id: "c1",
    icon: "botchat",
    label: "Bot Chat",
    action: () => console.log("Contact CS"),
  },
  {
    id: "c2",
    icon: "whatsapp",
    label: "WhatsApp Chat",
    action: openWhatsApp,
  },
]

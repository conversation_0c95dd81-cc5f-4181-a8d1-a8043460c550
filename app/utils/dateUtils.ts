/**
 * Safely formats a date string to a localized date string
 * @param dateString The date string to format
 * @param options Formatting options
 * @param fallback Fallback string to use if the date is invalid
 * @returns Formatted date string or fallback
 */
export function formatDate(
  dateString?: string,
  options: Intl.DateTimeFormatOptions = {},
  fallback: string = "Date inconnue"
): string {
  if (!dateString) return fallback
  
  try {
    const parsedDate = new Date(dateString)
    if (isNaN(parsedDate.getTime())) {
      // console.warn(`Invalid date format: ${dateString}`)
      return fallback
    }
    
    return parsedDate.toLocaleDateString(undefined, options)
  } catch (error) {
    console.error(`Error formatting date: ${dateString}`, error)
    return fallback
  }
}

/**
 * Safely formats a date string to a localized date and time string
 * @param dateString The date string to format
 * @param options Formatting options
 * @param fallback Fallback string to use if the date is invalid
 * @returns Formatted date and time string or fallback
 */
export function formatDateTime(
  dateString?: string,
  options: Intl.DateTimeFormatOptions = {},
  fallback: string = "Date inconnue"
): string {
  if (!dateString) return fallback
  
  try {
    const parsedDate = new Date(dateString)
    if (isNaN(parsedDate.getTime())) {
      console.warn(`Invalid date format: ${dateString}`)
      return fallback
    }
    
    return parsedDate.toLocaleString(undefined, options)
  } catch (error) {
    console.error(`Error formatting date: ${dateString}`, error)
    return fallback
  }
}

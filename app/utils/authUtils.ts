import { useAuthStore } from "@/store/AuthenticationStore"
import { storage } from "@/utils/storage"

/**
 * Utility function to reset authentication state and clear any problematic data
 * This should be called when the app starts to ensure clean state
 */
export const resetAuthenticationState = () => {
  console.log("🔄 Resetting authentication state...")
  
  const authStore = useAuthStore.getState()
  
  // Reset failed token attempts
  authStore.resetFailedAttempts()
  
  // Check if we have tokens but user is not authenticated
  const accessToken = storage.getString("accessToken")
  const refreshToken = storage.getString("refreshToken")
  
  if ((accessToken || refreshToken) && !authStore.isAuthenticated) {
    console.log("🧹 Found orphaned tokens, clearing them...")
    authStore.clearLocalData()
  }
  
  // Reset logout flag if it's stuck
  if (authStore.isLoggingOut) {
    console.log("🔄 Resetting stuck logout flag...")
    useAuthStore.setState({ isLoggingOut: false })
  }
  
  console.log("✅ Authentication state reset complete")
}

/**
 * Check if the authentication state is in a problematic condition
 */
export const isAuthStateProblematic = (): boolean => {
  const authStore = useAuthStore.getState()
  const accessToken = storage.getString("accessToken")
  const refreshToken = storage.getString("refreshToken")
  
  // Check for inconsistent state
  if (authStore.isAuthenticated && (!accessToken || !refreshToken)) {
    return true
  }
  
  // Check for stuck logout state
  if (authStore.isLoggingOut) {
    return true
  }
  
  // Check for too many failed attempts
  if (authStore.failedTokenAttempts >= 3) {
    return true
  }
  
  return false
}

/**
 * Force clean logout - use this when normal logout fails
 */
export const forceCleanLogout = () => {
  console.log("🚨 Forcing clean logout...")
  
  const authStore = useAuthStore.getState()
  
  // Clear all data immediately
  authStore.clearLocalData()
  
  // Reset all flags
  useAuthStore.setState({
    isLoggingOut: false,
    failedTokenAttempts: 0,
    isAuthenticated: false,
  })
  
  console.log("✅ Force clean logout complete")
}

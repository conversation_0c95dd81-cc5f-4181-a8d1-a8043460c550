import { AlertButton, AlertType } from "@/components/CustomAlert"

export interface ParsedError {
  title: string
  message: string
  type: AlertType
}

/**
 * Parse error response and extract meaningful message
 */
export function parseErrorMessage(error: any): ParsedError {
  // Default error response
  const defaultError: ParsedError = {
    title: "Erreur",
    message: "Une erreur inattendue s'est produite. Veuillez réessayer.",
    type: "error",
  }

  if (!error) return defaultError

  try {
    // Handle different error response formats
    let errorData = error

    // If error has response property (axios error)
    if (error.response?.data) {
      errorData = error.response.data
    }

    // If error has data property
    if (error.data) {
      errorData = error.data
    }

    // Check if it's already a parsed error object
    if (typeof errorData === "object" && errorData.message) {
      // Handle nested message object (like your example)
      if (typeof errorData.message === "object") {
        // Extract specific field errors
        const messageObj = errorData.message

        // Handle serializer view error format
        if (messageObj["serializer view error"]) {
          return {
            title: "Erreur de validation",
            message: messageObj["serializer view error"],
            type: "error",
          }
        }

        // Handle field-specific errors
        const fieldErrors: string[] = []
        Object.keys(messageObj).forEach((field) => {
          if (Array.isArray(messageObj[field])) {
            fieldErrors.push(`${field}: ${messageObj[field].join(", ")}`)
          } else if (typeof messageObj[field] === "string") {
            fieldErrors.push(`${field}: ${messageObj[field]}`)
          }
        })

        if (fieldErrors.length > 0) {
          return {
            title: "Erreur de validation",
            message: fieldErrors.join("\n"),
            type: "error",
          }
        }
      }

      // Handle simple string message
      if (typeof errorData.message === "string") {
        return {
          title: "Erreur",
          message: errorData.message,
          type: "error",
        }
      }
    }

    // Handle direct string error
    if (typeof errorData === "string") {
      return {
        title: "Erreur",
        message: errorData,
        type: "error",
      }
    }

    // Handle specific HTTP status codes
    if (error.status || error.response?.status) {
      const status = error.status || error.response?.status
      switch (status) {
        case 400:
          return {
            title: "Données invalides",
            message: "Les données fournies ne sont pas valides. Veuillez vérifier et réessayer.",
            type: "error",
          }
        case 401:
          return {
            title: "Non autorisé",
            message: "Votre session a expiré. Veuillez vous reconnecter.",
            type: "warning",
          }
        case 403:
          return {
            title: "Accès refusé",
            message: "Vous n'avez pas les permissions nécessaires pour cette action.",
            type: "error",
          }
        case 404:
          return {
            title: "Non trouvé",
            message: "La ressource demandée n'a pas été trouvée.",
            type: "error",
          }
        case 500:
          return {
            title: "Erreur serveur",
            message: "Une erreur s'est produite sur le serveur. Veuillez réessayer plus tard.",
            type: "error",
          }
        case 503:
          return {
            title: "Service indisponible",
            message: "Le service est temporairement indisponible. Veuillez réessayer plus tard.",
            type: "warning",
          }
      }
    }

    return defaultError
  } catch (parseError) {
    console.error("Error parsing error message:", parseError)
    return defaultError
  }
}

/**
 * Create retry button configuration
 */
export function createRetryButtons(onRetry: () => void, onCancel?: () => void): AlertButton[] {
  return [
    {
      text: "Annuler",
      style: "cancel",
      onPress: onCancel,
    },
    {
      text: "Réessayer",
      style: "default",
      onPress: onRetry,
    },
  ]
}

/**
 * Create simple OK button
 */
export function createOkButton(onPress?: () => void): AlertButton[] {
  return [
    {
      text: "OK",
      style: "default",
      onPress,
    },
  ]
}

/**
 * Create success alert configuration
 */
export function createSuccessAlert(
  message: string,
  onPress?: () => void,
): {
  title: string
  message: string
  type: AlertType
  buttons: AlertButton[]
} {
  return {
    title: "Succès",
    message,
    type: "success",
    buttons: createOkButton(onPress),
  }
}

/**
 * Create warning alert configuration
 */
export function createWarningAlert(
  message: string,
  onConfirm: () => void,
  onCancel?: () => void,
): {
  title: string
  message: string
  type: AlertType
  buttons: AlertButton[]
} {
  return {
    title: "Attention",
    message,
    type: "warning",
    buttons: [
      {
        text: "Annuler",
        style: "cancel",
        onPress: onCancel,
      },
      {
        text: "Continuer",
        style: "destructive",
        onPress: onConfirm,
      },
    ],
  }
}

/**
 * Create network alert configuration
 */
export function createNetworkAlert(
  message?: string,
  onRetry?: () => void,
  onCancel?: () => void,
): {
  title: string
  message: string
  type: AlertType
  buttons: AlertButton[]
} {
  return {
    title: "Problème de connexion",
    message: message || "Vérifiez votre connexion internet et réessayez.",
    type: "network",
    buttons: onRetry ? [
      {
        text: "Annuler",
        style: "cancel",
        onPress: onCancel,
      },
      {
        text: "Réessayer",
        style: "default",
        onPress: onRetry,
      },
    ] : [
      {
        text: "OK",
        style: "default",
        onPress: onCancel,
      },
    ],
  }
}

/**
 * Extract user-friendly field names for better error messages
 */
export function getFriendlyFieldName(fieldName: string): string {
  const fieldMap: Record<string, string> = {
    email: "Email",
    password: "Mot de passe",
    phone: "Téléphone",
    name: "Nom",
    first_name: "Prénom",
    last_name: "Nom de famille",
    username: "Nom d'utilisateur",
    address: "Adresse",
    city: "Ville",
    country: "Pays",
    postal_code: "Code postal",
    date_of_birth: "Date de naissance",
    // Add more field mappings as needed
  }

  return fieldMap[fieldName] || fieldName
}

import { AlertButton, AlertType } from "@/components/CustomAlert"
import { getErrorMessage, isServerError, isNetworkError } from "./apiErrorHandler"

export interface ParsedError {
  title: string
  message: string
  type: AlertType
}

/**
 * Parse error response and extract meaningful message using improved error handling
 */
export function parseErrorMessage(error: any): ParsedError {
  // Default error response
  const defaultError: ParsedError = {
    title: "Erreur",
    message: "Une erreur inattendue s'est produite. Veuillez réessayer.",
    type: "error",
  }

  if (!error) return defaultError

  try {
    // Use our improved error message extraction
    const userFriendlyMessage = getErrorMessage(error)

    // Determine the alert type based on error characteristics
    let alertType: AlertType = "error"
    let title = "Erreur"

    // Check for specific error types
    if (isNetworkError(error)) {
      alertType = "network"
      title = "Problème de connexion"
    } else if (isServerError(error)) {
      alertType = "error"
      title = "Service indisponible"
    } else if (error.response?.status === 401) {
      alertType = "warning"
      title = "Session expirée"
    } else if (error.response?.status === 400 || error.response?.status === 422) {
      alertType = "error"
      title = "Données invalides"
    } else if (error.response?.status === 403) {
      alertType = "error"
      title = "Accès refusé"
    } else if (error.response?.status === 404) {
      alertType = "error"
      title = "Non trouvé"
    } else if (error.response?.status === 429) {
      alertType = "warning"
      title = "Trop de tentatives"
    }

    // Log the original error for debugging (but don't show to user)
    console.error("Original error for debugging:", error)

    return {
      title,
      message: userFriendlyMessage,
      type: alertType,
    }
  } catch (parseError) {
    console.error("Error parsing error message:", parseError)
    return defaultError
  }
}

/**
 * Create retry button configuration
 */
export function createRetryButtons(onRetry: () => void, onCancel?: () => void): AlertButton[] {
  return [
    {
      text: "Annuler",
      style: "cancel",
      onPress: onCancel,
    },
    {
      text: "Réessayer",
      style: "default",
      onPress: onRetry,
    },
  ]
}

/**
 * Create simple OK button
 */
export function createOkButton(onPress?: () => void): AlertButton[] {
  return [
    {
      text: "OK",
      style: "default",
      onPress,
    },
  ]
}

/**
 * Create success alert configuration
 */
export function createSuccessAlert(
  message: string,
  onPress?: () => void,
): {
  title: string
  message: string
  type: AlertType
  buttons: AlertButton[]
} {
  return {
    title: "Succès",
    message,
    type: "success",
    buttons: createOkButton(onPress),
  }
}

/**
 * Create warning alert configuration
 */
export function createWarningAlert(
  message: string,
  onConfirm: () => void,
  onCancel?: () => void,
): {
  title: string
  message: string
  type: AlertType
  buttons: AlertButton[]
} {
  return {
    title: "Attention",
    message,
    type: "warning",
    buttons: [
      {
        text: "Annuler",
        style: "cancel",
        onPress: onCancel,
      },
      {
        text: "Continuer",
        style: "destructive",
        onPress: onConfirm,
      },
    ],
  }
}

/**
 * Create network alert configuration
 */
export function createNetworkAlert(
  message?: string,
  onRetry?: () => void,
  onCancel?: () => void,
): {
  title: string
  message: string
  type: AlertType
  buttons: AlertButton[]
} {
  return {
    title: "Problème de connexion",
    message: message || "Vérifiez votre connexion internet et réessayez.",
    type: "network",
    buttons: onRetry ? [
      {
        text: "Annuler",
        style: "cancel",
        onPress: onCancel,
      },
      {
        text: "Réessayer",
        style: "default",
        onPress: onRetry,
      },
    ] : [
      {
        text: "OK",
        style: "default",
        onPress: onCancel,
      },
    ],
  }
}

/**
 * Extract user-friendly field names for better error messages
 */
export function getFriendlyFieldName(fieldName: string): string {
  const fieldMap: Record<string, string> = {
    email: "Email",
    password: "Mot de passe",
    phone: "Téléphone",
    name: "Nom",
    first_name: "Prénom",
    last_name: "Nom de famille",
    username: "Nom d'utilisateur",
    address: "Adresse",
    city: "Ville",
    country: "Pays",
    postal_code: "Code postal",
    date_of_birth: "Date de naissance",
    // Add more field mappings as needed
  }

  return fieldMap[fieldName] || fieldName
}

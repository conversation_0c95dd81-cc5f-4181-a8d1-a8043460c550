export const PayerScreenMenu = [
  {
    serviceName: "Bénéficiaires",
    serviceType: "Beneficiary",
    iconName: "group",
  },
  {
    serviceName: "SNEL",
    serviceType: "ElectricityBillsPayment",
    iconName: "lightbulb",
  },
  // {
  //   serviceName: "Lipa na Scan",
  //   serviceType: "TransferScantoPay",
  //   iconName: "scan",
  // },
  {
    serviceName: "REGIDESO",
    serviceType: "WaterBillsPayment",
    iconName: "water",
  },
  // {
  //   serviceName: "Paramètres",
  //   serviceType: "Paramètres",
  //   iconName: "settings",
  // },

  {
    serviceName: "Transport",
    serviceType: "Lipatransport",
    iconName: "taxi",
  },
]
export const TransferScreenMenu = [
  {
    serviceName: "Transfert",
    serviceType: "TRANSFER",
    iconName: "pay",
  },
  {
    serviceName: "Historique",
    serviceType: "TransferHistory",
    iconName: "history",
  },
  // {
  //   serviceName: "eWallet",
  //   serviceType: "ExternalWallet",
  //   iconName: "ExternalWallet",
  // },

  {
    serviceName: "Comment ça marche",
    serviceType: "FedhaElimu",
    iconName: "video",
  },
]

export const MystoreFeatures = [
  {
    serviceName: "Produits",
    serviceType: "Produits",
    iconName: "products",
  },
  // {
  //   serviceName: "Personnel",
  //   serviceType: "Personnel",
  //   iconName: "staff",
  // },
  {
    serviceName: "Factures",
    serviceType: "Factures",
    iconName: "invoice",
  },
  {
    serviceName: "Rapports",
    serviceType: "Rapports",
    iconName: "reports",
  },
  {
    serviceName: "Message",
    serviceType: "Message",
    iconName: "message",
  },
  {
    serviceName: "Informations",
    serviceType: "Informations",
    iconName: "info",
  },
  {
    serviceName: "Elimu",
    serviceType: "Elimu",
    iconName: "elimu",
  },
]

export const DashboardFeature = [
  {
    serviceName: "Épargne+",
    serviceType: "Épargne+",
    iconName: "invest",
  },
  {
    serviceName: "Achetez",
    serviceType: "Achetez",
    iconName: "shoppingBag",
  },
  {
    serviceName: "Factures",
    serviceType: "Factures",
    iconName: "utilities",
  },
  {
    serviceName: "Coupons",
    serviceType: "Coupons",
    iconName: "giftcard",
  },
  {
    serviceName: "Message",
    serviceType: "Message",
    iconName: "message",
  },

  {
    serviceName: "Elimu",
    serviceType: "Elimu",
    iconName: "elimu",
  },
  {
    serviceName: "Informations",
    serviceType: "Informations",
    iconName: "info",
  },
]

export const infoFeatures = [
  {
    serviceName: "Tarifs",
    serviceType: "Tarifs",
    iconName: "percentage",
  },
  {
    serviceName: "FAQ",
    serviceType: "FAQ",
    iconName: "question",
  },
  {
    serviceName: "Agents",
    serviceType: "Agents",
    iconName: "map",
  },
  {
    serviceName: "Contacts",
    serviceType: "Contacts",
    iconName: "contactus",
  },

  // {
  //   serviceName: "Elimu",
  //   serviceType: "Elimu",
  //   iconName: "elimu",
  // },
  {
    serviceName: "légal",
    serviceType: "legal",
    iconName: "legaldocument",
  },
]

export const ServicesToBuyList = [
  {
    serviceName: "SNEL Cash Power",
    event: "active",
    serviceDescription:
      "Rechargez votre compteur d'électricité SNEL Cash Power dans le confort de votre fauteuil et laissez-nous faire le travail à votre place.",
    serviceBannert: require("../../assets/images/banners/Snel.png"),
  },
  {
    serviceName: "Forfaits & Minutes",
    event: "comingSoon",
    serviceDescription:
      "Achatez des forfaits de mega, de minutes vocales, de SMS est plus auprès de plusieurs fournisseurs",
    serviceBannert: require("../../assets/images/banners/netWORKmUTL.png"),
  },

  // {
  //   serviceName: "REGIDESO",
  //   serviceDescription: "Payer vos Factures de REGIDESO en ligne, sans avoir à vous déplacer.",
  //   serviceBannert: require("../../assets/images/banners/Regi.png"),
  // },
]

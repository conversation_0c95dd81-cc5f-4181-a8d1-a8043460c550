/* eslint-disable react-hooks/rules-of-hooks */
/* eslint-disable import/first */
/**
 * Welcome to the main entry point of the app. In this file, we'll
 * be kicking off our app.
 *
 * Most of this file is boilerplate and you shouldn't need to modify
 * it very often. But take some time to look through and understand
 * what is going on here.
 *
 * The app navigation resides in ./app/navigators, so head over there
 * if you're interested in adding screens and navigators.
 */
if (__DEV__) {
  // Load Reactotron in development only.
  // Note that you must be using metro's `inlineRequires` for this to work.
  // If you turn it off in metro.config.js, you'll have to manually import it.
  require("./devtools/ReactotronConfig.ts")
}
import "./utils/gestureHandler"
import { initI18n } from "./i18n"
import "./utils/ignoreWarnings"
import { useFonts } from "expo-font"
import { useEffect, useState } from "react"
import { initialWindowMetrics, SafeAreaProvider } from "react-native-safe-area-context"
import * as Linking from "expo-linking"
import * as SplashScreen from "expo-splash-screen"
import { AppNavigator, useNavigationPersistence } from "./navigators"
import { ErrorBoundary } from "./screens/ErrorScreen/ErrorBoundary"
import * as storage from "./utils/storage"
import { customFontsToLoad } from "./theme"
import Config from "./config"
import { KeyboardProvider } from "react-native-keyboard-controller"
import { loadDateFnsLocale } from "./utils/formatDate"
import { SWRConfig } from "swr"
import fetcher from "./config/fetcher"
import { useNetworkStore } from "./store/useNetworkStore"
import { NotificationProvider } from "./context/NotificationContext"
import "react-loading-skeleton/dist/skeleton.css"
import { initializeAppData } from "./utils/initializeAppData"
import PostHog, { PostHogProvider } from "posthog-react-native"

import * as Notifications from "expo-notifications"
import * as Sentry from "@sentry/react-native"
import * as TaskManager from "expo-task-manager"
import {
  // scheduleWelcomeNotification,
  showTestNotification,
  testPushNotification,
} from "./utils/notificationHelpers"
// import { UserInactivityProvider } from "./context/UserInactivityContext"
// import { registerForPushNotificationsAsync } from "./utils/registerForPushNotificationsAsync"
// import { usePushNotificationStore } from "./model/NotificationStore"

export const NAVIGATION_PERSISTENCE_KEY = "NAVIGATION_STATE"
// const INACTIVITY_TIMEOUT = 2 * 60 * 1000

// Web linking configuration
const prefix = Linking.createURL("/")
const config = {
  screens: {
    Login: {
      path: "",
    },
    Welcome: "welcome",
    Demo: {
      screens: {
        DemoShowroom: {
          path: "showroom/:queryIndex?/:itemIndex?",
        },
        DemoDebug: "debug",
        DemoPodcastList: "podcast",
        DemoCommunity: "community",
      },
    },
  },
}

/**
 * This is the root component of our app.
 * @returns The rendered `App` component.
 */

// Use the direct API key instead of environment variable
export const posthog = new PostHog("phc_utCjGzZrV3V7i3H54C4l823afYQmd7drAP8ebsEnqYw")
// Initialize Sentry
// Sentry.init({
//   dsn: "https://<EMAIL>/4509063576748032",
//   // Enable auto session tracking to track app crashes
//   enableAutoSessionTracking: true,
//   // Session closes after app is 10 seconds in the background
//   sessionTrackingIntervalMillis: 10000,
//   // Always enable Sentry to send the first event during setup
//   // In production, you might want to disable in development: enabled: !__DEV__
//   enabled: true,
//   // Adds more context data to events (IP address, cookies, user, etc.)
//   sendDefaultPii: true,
//   // Set tracesSampleRate to 1.0 to capture 100% of transactions for tracing
//   // We recommend adjusting this value in production (0.2 = 20% of transactions)
//   tracesSampleRate: 1.0,
//   // profilesSampleRate is relative to tracesSampleRate
//   // Here, we'll capture profiles for 100% of sampled transactions
//   profilesSampleRate: 1.0,
//   // Set the environment tag
//   environment: __DEV__ ? "development" : "production",
//   // Set the release version (ideally this would come from your app.json or similar)
//   release: "fedha_mobileapp@1.0.0",
//   // Enable performance monitoring
//   integrations: [
//     // Add any integrations you need
//   ],
//   debug: true, // Enable debug mode to see Sentry logs during setup
// })

// Send a test event to Sentry to verify the setup
Sentry.captureMessage("Test event from fedha_mobileapp initialization", "info")

// Add some breadcrumbs to help with debugging
Sentry.addBreadcrumb({
  category: "app",
  message: "App initialized",
  level: "info",
})

// You can also capture a test error if needed
// Uncomment this to send a test error
// setTimeout(() => {
//   try {
//     throw new Error("Test error from fedha_mobileapp");
//   } catch (error) {
//     Sentry.captureException(error);
//   }
// }, 2000);

export function App() {
  const {
    initialNavigationState,
    onNavigationStateChange,
    isRestored: isNavigationStateRestored,
  } = useNavigationPersistence(storage, NAVIGATION_PERSISTENCE_KEY)

  const { checkInternet } = useNetworkStore()

  const [areFontsLoaded, fontLoadError] = useFonts(customFontsToLoad)
  const [isI18nInitialized, setIsI18nInitialized] = useState(false)

  useEffect(() => {
    initI18n()
      .then(() => setIsI18nInitialized(true))
      .then(() => loadDateFnsLocale())
  }, [])

  useEffect(() => {
    // If your initialization scripts run very fast, it's good to show the splash screen for just a bit longer to prevent flicker.
    // Slightly delaying splash screen hiding for better UX; can be customized or removed as needed,
    setTimeout(SplashScreen.hideAsync, 500)
  }, [])

  // Initialize app data (currencies, etc.) once when the app starts
  useEffect(() => {
    // Initialize app data (fetch currencies, etc.)
    initializeAppData()
      .then(() => console.log("App data initialized successfully"))
      .catch((error) => console.error("Failed to initialize app data:", error))
  }, [])

  useEffect(() => {
    const interval = setInterval(() => {
      checkInternet() // 🔄 Check network every 5 seconds
    }, 5000)

    return () => clearInterval(interval)
  }, [checkInternet])

  // Now place the conditional return after all hooks
  if (!isNavigationStateRestored || !isI18nInitialized || (!areFontsLoaded && !fontLoadError)) {
    return null
  }

  const linking = {
    prefixes: [prefix],
    config,
  }

  Notifications.setNotificationHandler({
    handleNotification: async () => ({
      shouldShowAlert: true,
      shouldPlaySound: false,
      shouldSetBadge: false,
    }),
  })

  const BACKGROUND_NOTIFICATION_TASK = "BACKGROUND-NOTIFICATION-TASK"
  // const FIRST_TIME_LAUNCH_KEY = "HAS_LAUNCHED_BEFORE"

  // Define background task handler
  TaskManager.defineTask(BACKGROUND_NOTIFICATION_TASK, async ({ data, error, executionInfo }) => {
    console.log("📱 Background notification received:", {
      data,
      error,
      executionInfo,
    })
    // Handle background notification
  })

  // Register background task
  Notifications.registerTaskAsync(BACKGROUND_NOTIFICATION_TASK)

  // Add development testing function
  if (__DEV__) {
    // Existing test notification
    // @ts-ignore
    global.testNotification = async () => {
      await showTestNotification()
    }

    // Enhanced push notification testing
    // @ts-ignore
    global.testPushNotification = async () => {
      try {
        const result = await testPushNotification()
        console.log("✅ Push notification test completed:", result)
      } catch (error) {
        console.error("❌ Push notification test failed:", error)
      }
    }
  }
  // registerNNPushToken(28565, "kpDODP3wgii2ID4v4bD3tP")
  // Setup Sentry user information when user logs in
  // Example: Sentry.setUser({ id: user.id, email: user.email })
  // otherwise, we're ready to render the app
  return (
    <SafeAreaProvider initialMetrics={initialWindowMetrics}>
      {/* <UserInactivityProvider> */}
      <SWRConfig
        value={{
          fetcher,
          errorRetryInterval: 5000, // Retry failed requests after 5 seconds
          revalidateOnFocus: false, // Avoid unnecessary API calls when refocusing
          shouldRetryOnError: (error) => error.response?.status !== 401, // Don't retry if unauthorized
        }}
      >
        <NotificationProvider>
          <ErrorBoundary catchErrors={Config.catchErrors}>
            {/* Sentry TouchEventBoundary captures unhandled touch events and reports them to Sentry */}
            {/* <Sentry.TouchEventBoundary> */}
            <KeyboardProvider>
              <PostHogProvider
                client={posthog}
                autocapture={{
                  captureScreens: false, // Screen events are handled differently for react-native-navigation
                  captureLifecycleEvents: false, // Lifecycle events are handled differently for react-native-navigation
                  captureTouches: true,
                }}
              >
                <AppNavigator
                  linking={linking}
                  initialState={initialNavigationState}
                  onStateChange={onNavigationStateChange}
                />
              </PostHogProvider>
            </KeyboardProvider>
            {/* </Sentry.TouchEventBoundary> */}
          </ErrorBoundary>
        </NotificationProvider>
      </SWRConfig>
      {/* </UserInactivityProvider> */}
    </SafeAreaProvider>
  )
}

/**
 * The app navigator (formerly "AppNavigator" and "MainNavigator") is used for the primary
 * navigation flows of your app.
 * Generally speaking, it will contain an auth flow (registration, login, forgot password)
 * and a "main" flow which the user will use once logged in.
 */
import { NavigationContainer, NavigatorScreenParams } from "@react-navigation/native"
import { createNativeStackNavigator, NativeStackScreenProps } from "@react-navigation/native-stack"
import * as Screens from "@/screens"
import Config from "../config"
import { navigationRef, useBackButtonHandler } from "./navigationUtilities"
import { useAppTheme, useThemeProvider } from "@/utils/useAppTheme"
import { ComponentProps } from "react"
import { useStores } from "@/store/rootStore"
import { TabNavigator, TabParamList } from "./TabNavigator"
import { BussinessTabNavigator, BusinessTabParamList } from "./BusinessTabNavigator"

/**
 * This type allows TypeScript to know what routes are defined in this navigator
 * as well as what properties (if any) they might take when navigating to them.
 *
 * If no params are allowed, pass through `undefined`. Generally speaking, we
 * recommend using your MobX-State-Tree store(s) to keep application state
 * rather than passing state through navigation params.
 *
 * For more information, see this documentation:
 *   https://reactnavigation.org/docs/params/
 *   https://reactnavigation.org/docs/typescript#type-checking-the-navigator
 *   https://reactnavigation.org/docs/typescript/#organizing-types
 */
export type AppStackParamList = {
  Welcome: undefined
  TabNav: NavigatorScreenParams<TabParamList>
  BusinessTabNav: NavigatorScreenParams<BusinessTabParamList>
  Login: undefined
  Register: undefined
  Dashboard: undefined
  OtpVerfication: undefined // Confirm this spelling
  ForgotPassCode: undefined
  UserVerification: undefined
  DashboardBusiness: undefined
  FedhaLoader: { nextScreen?: string }
  HowitWorks: undefined
  Recevoir: undefined
  Pochi: undefined
  Pay: undefined
  Transfer: undefined
  Withdraw: undefined
  Historic: undefined
  LipanaFedha: {
    fromTab?: boolean
    hideTabBar?: boolean
    scannedData?: any
  }
  ElimuHub: {
    serviceName: string
  }
  Account: undefined
  AccountInfo: undefined
  ContactUs: undefined
  ServiceAction: undefined
  Helpcenter: undefined
  Lock: undefined
  FedhaSafe: undefined
  Payme: undefined
  MyCart: undefined
  PaymentMethods: undefined
  BusinessMore: undefined
  BusinessProfile: undefined
  BusinessTransaction: undefined
  BusinessOnboarding: undefined
  AccountType: undefined
  CreateUserBusiness: undefined
  MyStore: undefined
  BussinessActivities: undefined
  MyShelf: undefined
  AddProduct: undefined
  MyStaff: undefined
  Invoice: undefined
  BusinessReports: undefined
  Elimu: undefined
  Scanner: {
    hideTabBar?: boolean
    onScanComplete?: (data: any) => void
    returnScreen?: string
    user_type?: "standard" | "business"
  }
  Buy: undefined
  MesFacture: undefined
  Coupons: undefined
  Message: undefined
  Info: undefined
  Beneficiary: undefined
  Onboarding: undefined
  ResetPassword: undefined
  BuyingService: undefined
	// IGNITE_GENERATOR_ANCHOR_APP_STACK_PARAM_LIST
}

/**
 * This is a list of all the route names that will exit the app if the back button
 * is pressed while in that screen. Only affects Android.
 */
const exitRoutes = Config.exitRoutes

export type AppStackScreenProps<T extends keyof AppStackParamList> = NativeStackScreenProps<
  AppStackParamList,
  T
>

// Documentation: https://reactnavigation.org/docs/stack-navigator/
const Stack = createNativeStackNavigator<AppStackParamList>()

const AppStack = () => {
  const {
    auth: { user_type, isAuthenticated },
  } = useStores()
  // console.log("isAuthenticated", isAuthenticated)
  const {
    theme: { colors },
  } = useAppTheme()

  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        navigationBarColor: colors.background,
        contentStyle: {
          backgroundColor: colors.background,
        },
      }}
      initialRouteName="FedhaLoader"
    >
      {/* {console.log(has_business)} */}
      <Stack.Screen name="FedhaLoader" component={Screens.FedhaLoaderScreen} />
      {isAuthenticated ? (
        <>
          <Stack.Screen
            name={user_type === "standard" ? "TabNav" : "BusinessTabNav"}
            component={user_type === "standard" ? TabNavigator : BussinessTabNavigator}
          />
          <Stack.Screen name="Elimu" component={Screens.ElimuScreen} />
          <Stack.Screen name="Scanner" component={Screens.ScannerScreen} />
          <Stack.Screen name="Buy" component={Screens.BuyScreen} />
          <Stack.Screen name="Coupons" component={Screens.CouponsScreen} />
          <Stack.Screen name="Message" component={Screens.MessageScreen} />
          <Stack.Screen name="Info" component={Screens.InfoScreen} />
          <Stack.Screen name="Beneficiary" component={Screens.BeneficiaryScreen} />
          <Stack.Screen name="BuyingService" component={Screens.BuyingServiceScreen} />
			{/* IGNITE_GENERATOR_ANCHOR_APP_STACK_SCREENS */}
          <Stack.Screen name="OtpVerfication" component={Screens.OtpVerficationScreen} />
          <Stack.Screen name="ElimuHub" component={Screens.ElimuHubScreen} />
          <Stack.Screen name="Account" component={Screens.AccountScreen} />
          <Stack.Screen name="AccountInfo" component={Screens.AccountInfoScreen} />
          <Stack.Screen name="ContactUs" component={Screens.ContactUsScreen} />
          <Stack.Screen name="ServiceAction" component={Screens.ServiceActionScreen} />
          <Stack.Screen name="Helpcenter" component={Screens.HelpcenterScreen} />
          <Stack.Screen name="HowitWorks" component={Screens.HowitWorksScreen} />
          {/* <Stack.Screen name="Lock" component={Screens.LockScreen} />
          <Stack.Screen name="FedhaSafe" component={Screens.FedhaSafeScreen} /> */}

          <Stack.Screen name="UserVerification" component={Screens.UserVerificationScreen} />
          {user_type === "standard" && (
            <>
              <Stack.Screen name="Dashboard" component={Screens.DashboardScreen} />

              <Stack.Screen name="Recevoir" component={Screens.RecevoirScreen} />
              <Stack.Screen name="Pochi" component={Screens.PochiScreen} />
              <Stack.Screen name="Pay" component={Screens.PayScreen} />
              <Stack.Screen name="Transfer" component={Screens.TransferScreen} />
              <Stack.Screen name="Withdraw" component={Screens.WithdrawScreen} />
              <Stack.Screen name="Historic" component={Screens.HistoricScreen} />
              <Stack.Screen
                name="LipanaFedha"
                component={Screens.LipanaFedhaScreen}
                options={{
                  // Ensure the screen is presented as a standalone screen
                  headerShown: false,
                  presentation: "card",
                }}
              />
            </>
          )}

          {user_type === "business" && (
            <>
              <Stack.Screen name="DashboardBusiness" component={Screens.DashboardBusinessScreen} />
              <Stack.Screen name="MyStore" component={Screens.MyStoreScreen} />
              <Stack.Screen name="Payme" component={Screens.PaymeScreen} />
              <Stack.Screen name="MyCart" component={Screens.MyCartScreen} />
              <Stack.Screen name="PaymentMethods" component={Screens.PaymentMethodsScreen} />
              <Stack.Screen name="BusinessMore" component={Screens.BusinessMoreScreen} />
              <Stack.Screen name="BusinessProfile" component={Screens.BusinessProfileScreen} />
              <Stack.Screen
                name="BussinessActivities"
                component={Screens.BussinessActivitiesScreen}
              />
              <Stack.Screen name="MyShelf" component={Screens.MyShelfScreen} />
              <Stack.Screen name="AddProduct" component={Screens.AddProductScreen} />
              <Stack.Screen name="MyStaff" component={Screens.MyStaffScreen} />
              <Stack.Screen name="Invoice" component={Screens.InvoiceScreen} />
              <Stack.Screen name="BusinessReports" component={Screens.BusinessReportsScreen} />
              <Stack.Screen
                name="BusinessTransaction"
                component={Screens.BusinessTransactionScreen}
              />
              <Stack.Screen
                name="BusinessOnboarding"
                component={Screens.BusinessOnboardingScreen}
              />

              {/* <Stack.Screen name="Lock" component={Screens.LockScreen} />
              <Stack.Screen name="FedhaSafe" component={Screens.FedhaSafeScreen} /> */}
            </>
          )}
        </>
      ) : (
        <>
          <Stack.Screen name="Onboarding" component={Screens.OnboardingScreen} />
          <Stack.Screen name="Welcome" component={Screens.WelcomeScreen} />
          <Stack.Screen name="Login" component={Screens.LoginScreen} />
          <Stack.Screen name="AccountType" component={Screens.AccountTypeScreen} />
          <Stack.Screen name="Register" component={Screens.RegisterScreen} />
          <Stack.Screen name="ForgotPassCode" component={Screens.ForgotPassCodeScreen} />
          <Stack.Screen name="ResetPassword" component={Screens.ResetPasswordScreen} />
          <Stack.Screen name="Helpcenter" component={Screens.HelpcenterScreen} />
          <Stack.Screen name="CreateUserBusiness" component={Screens.CreateUserBusinessScreen} />
        </>
      )}
    </Stack.Navigator>
  )
}

export interface NavigationProps extends Partial<ComponentProps<typeof NavigationContainer>> {}

// PostHog analytics is now handled in app.tsx

export const AppNavigator = (props: NavigationProps) => {
  const { themeScheme, navigationTheme, setThemeContextOverride, ThemeProvider } =
    useThemeProvider()

  useBackButtonHandler((routeName) => exitRoutes.includes(routeName))

  return (
    <ThemeProvider value={{ themeScheme, setThemeContextOverride }}>
      <NavigationContainer ref={navigationRef} theme={navigationTheme} {...props}>
        <AppStack />
      </NavigationContainer>
    </ThemeProvider>
  )
}

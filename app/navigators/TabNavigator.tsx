/* eslint-disable react-native/no-inline-styles */
/* eslint-disable @typescript-eslint/no-unused-vars */
import { BottomTabScreenProps, createBottomTabNavigator } from "@react-navigation/bottom-tabs"
import { CompositeScreenProps, useNavigation } from "@react-navigation/native"
import { TextStyle, TouchableOpacity, View, ViewStyle } from "react-native"
import { useSafeAreaInsets } from "react-native-safe-area-context"
import { AppStackParamList, AppStackScreenProps } from "./AppNavigator"
import { useAppTheme } from "@/utils/useAppTheme"
import { ThemedStyle } from "@/theme"
import { Icon, Text } from "@/components"
import {
  AccountScreen,
  DashboardScreen,
  HistoricScreen,
  LipanaFedhaScreen,
  PochiScreen,
  ScannerScreen,
} from "@/screens"
import { translate } from "../i18n"

export type TabParamList = {
  AppNav: any
  Dashboard: undefined
  Pochi: undefined
  Historic: undefined
  Scanner: undefined
  Account: undefined
  LipanaFedha: {
    fromTab?: boolean
    hideTabBar?: boolean
    scannedData?: any
  }
  // IGNITE_GENERATOR_ANCHOR_APP_STACK_PARAM_LIST
}

export type TabScreenProps<T extends keyof TabParamList> = CompositeScreenProps<
  BottomTabScreenProps<TabParamList, T>,
  AppStackScreenProps<keyof AppStackParamList>
>

const Tab = createBottomTabNavigator<TabParamList>()

/**
 * More info: https://reactnavigation.org/docs/bottom-tab-navigator/
 * @returns {JSX.Element} The rendered `TabNavigator`.
 */

export function TabNavigator({ navigation }: any) {
  const { bottom } = useSafeAreaInsets()
  const {
    themed,
    theme: { colors },
  } = useAppTheme()
  //   const navigation = useNavigation()

  function ScantoPay() {
    // Navigate to LipanaFedha with hideTabBar parameter
    // navigation.navigate("Scanner", { hideTabBar: true })
    navigation.navigate("LipanaFedha", { hideTabBar: true })
  }

  const CustomButton = ({ children, onPress }: any) => {
    return (
      <TouchableOpacity
        onPress={onPress}
        style={{
          justifyContent: "center",
          alignItems: "center",
          top: -20,
        }}
      >
        <View
          style={{
            justifyContent: "center",
            alignItems: "center",
            borderRadius: 30,
            backgroundColor: colors.tint,
            width: 55,
            height: 55,
          }}
        >
          {children}
        </View>
      </TouchableOpacity>
    )
  }
  // name="Scanner"
  // component={ScannerScreen}
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        headerShown: false,
        tabBarHideOnKeyboard: true,
        tabBarStyle:
          route.name === "LipanaFedha"
            ? { display: "none" }
            : themed([$tabBar, { height: bottom + 70 }]),
        tabBarActiveTintColor: colors.text,
        tabBarInactiveTintColor: colors.text,
        tabBarLabelStyle: themed($tabBarLabel),
        tabBarIconStyle: themed($tabBarIconStyle),
      })}
    >
      <Tab.Screen
        name="Dashboard"
        component={DashboardScreen}
        options={{
          tabBarLabel: ({ focused }: { focused: boolean }) => (
            <Text
              style={{
                color: focused ? colors.palette.neutral900 : colors.palette.neutral600,
                ...themed($tabBarLabel),
              }}
            >
              {translate("tabNavigator:home")}
            </Text>
          ),
          tabBarIcon: ({ focused }: { focused: boolean }) => (
            <View style={themed($iconContainer)}>
              <Icon
                icon="home"
                color={focused ? colors.palette.neutral900 : colors.palette.neutral600}
                size={30}
              />
            </View>
          ),
        }}
      />
      <Tab.Screen
        name="Pochi"
        component={PochiScreen}
        options={{
          tabBarLabel: ({ focused }: { focused: boolean }) => (
            <Text
              style={{
                color: focused ? colors.palette.neutral900 : colors.palette.neutral600,
                ...themed($tabBarLabel),
              }}
            >
              {translate("tabNavigator:wallets")}
            </Text>
          ),
          tabBarIcon: ({ focused }: { focused: boolean }) => (
            <View style={themed($iconContainer)}>
              <Icon
                icon="wallet"
                color={focused ? colors.palette.neutral900 : colors.palette.neutral600}
                size={30}
              />
            </View>
          ),
        }}
      />
      <Tab.Screen
        name="LipanaFedha"
        component={LipanaFedhaScreen}
        options={{
          tabBarButton: () => (
            <CustomButton onPress={ScantoPay}>
              <Icon icon="scan" color={"#fff"} size={35} />
            </CustomButton>
          ),
          // Tab bar is hidden via screenOptions
        }}
      />
      <Tab.Screen
        name="Historic"
        component={HistoricScreen}
        options={{
          tabBarLabel: ({ focused }: { focused: boolean }) => (
            <Text
              style={{
                color: focused ? colors.palette.neutral900 : colors.palette.neutral600,
                ...themed($tabBarLabel),
              }}
            >
              {translate("tabNavigator:history")}
            </Text>
          ),
          tabBarIcon: ({ focused }: { focused: boolean }) => (
            <View style={themed($iconContainer)}>
              <Icon
                icon="orderHistory"
                color={focused ? colors.palette.neutral900 : colors.palette.neutral600}
                size={24}
              />
            </View>
          ),
        }}
      />
      <Tab.Screen
        name="Account"
        component={AccountScreen}
        options={{
          tabBarLabel: ({ focused }: { focused: boolean }) => (
            <Text
              style={{
                color: focused ? colors.palette.neutral900 : colors.palette.neutral600,
                ...themed($tabBarLabel),
              }}
            >
              {translate("tabNavigator:more")}
            </Text>
          ),
          tabBarIcon: ({ focused }: { focused: boolean }) => (
            <View style={themed($iconContainer)}>
              <Icon
                icon="hmenu"
                color={focused ? colors.palette.neutral900 : colors.palette.neutral600}
                size={24}
              />
            </View>
          ),
        }}
      />
    </Tab.Navigator>
  )
}

const $tabBar: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  backgroundColor: colors.palette.neutral100, // Light background
  borderTopWidth: 1,
  borderTopColor: colors.palette.neutral200, // Subtle top border
  paddingBottom: useSafeAreaInsets().bottom, // Respect safe area
  paddingTop: spacing.xs,
  height: 60 + useSafeAreaInsets().bottom, // Adjust height as needed
  alignItems: "center", // Center items vertically
})

const $tabBarItem: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingTop: spacing.md,
})

const $tabBarLabel: ThemedStyle<TextStyle> = ({ colors, typography, spacing }) => ({
  fontSize: 12, // Readable font size
  fontFamily: typography.primary.medium,
  marginTop: spacing.xxs, // Small space between icon and label
})

const $tabBarIconStyle: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.xxs, // Small space between icon and label
})

const $iconContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  width: 30, // Adjust icon container size
  height: 30,
  justifyContent: "center",
  alignItems: "center",
})

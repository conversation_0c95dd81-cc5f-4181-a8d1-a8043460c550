/* eslint-disable @typescript-eslint/no-unused-vars */
/**
 * These types indicate the shape of the data you expect to receive from your
 * API endpoint, assuming it's a JSON object like we have.
 */

import { z } from "zod"

export interface UserDetails {}

export interface FedhaPochi {}

export interface FedhaWallet {}

export interface FedhaVirtualCard {}

export const loginSchema = z.object({
  phone_number: z
    .string()
    .min(9, "Numéro de téléphone invalide") // Adjust min length based on your country's format
    .max(9, "Numéro de téléphone trop long")
    .regex(/^\d+$/, "Le numéro de téléphone doit contenir uniquement des chiffres"),

  password: z.string().min(4, "Mot de passe trop court"),
})

export const ForgetpasswordSchema = z.object({
  phone_number: z
    .string()
    .min(9, "Numéro de téléphone invalide") // Adjust min length based on your country's format
    .max(9, "Numéro de téléphone trop long")
    .regex(/^\d+$/, "Le numéro de téléphone doit contenir uniquement des chiffres"),
})

// Define the Identity Document schema
const IdentityDocumentSchema = z.object({
  date_expiration: z.string().nullable(),
  date_issue: z.string().nullable(),
  file_type: z.string(),
  lieu_de_delivrance: z.string().nullable(),
  numero_de_piece: z.string().nullable(),
  piece_identite: z.string().nullable(),
  type_piece: z.string().nullable(),
})

// Define the Personal Info schema
const PersonalInfoSchema = z.object({
  addresse: z.string().nullable(),
  date_de_naissance: z.string().nullable(),
  etat_civil: z.string().nullable(),
  nationality: z.string().nullable(),
  pays_des_residence: z.string().nullable(),
  profession: z.string().nullable(),
  ville_de_naissance: z.string().nullable(),
  ville_de_residence: z.string().nullable(),
})

// Define the main User schema
export const UserSchema = z.object({
  id: z.string(),
  IdentityDocument: z.object({
    date_expiration: z.string().nullable(),
    date_issue: z.string().nullable(),
    file_type: z.string(),
    id: z.number(),
    lieu_de_delivrance: z.string().nullable(),
    numero_de_piece: z.string().nullable(),
    piece_identite: z.string().nullable(),
    type_piece: z.string().nullable(),
  }),
  PersonalInfo: z.object({
    addresse: z.string().nullable(),
    date_de_naissance: z.string().nullable(),
    etat_civil: z.string().nullable(),
    nationality: z.string().nullable(),
    pays_des_residence: z.string().nullable(),
    profession: z.string().nullable(),
    ville_de_naissance: z.string().nullable(),
    ville_de_residence: z.string().nullable(),
  }),
  email: z.string().nullable(),
  first_name: z.string().nullable(),
  groups: z.array(z.number()),
  is_active: z.boolean(),
  is_documents_verified: z.boolean(),
  is_info_verified: z.boolean(),
  is_phone_verified: z.boolean(),
  last_login: z.string().nullable(),
  last_name: z.string().nullable(),
  middle_name: z.string().nullable(),
  phone_number: z.string(),
  profile_picture: z.string().nullable(),
  user_permissions: z.array(z.any()),
  verification_status: z.string(),
  user_type: z.string().nullable().optional(),
})

// Define the full response schema
export const UserResponseSchema = z.object({
  user: UserSchema,
})

// Define TypeScript types
export type UserType = z.infer<typeof UserSchema>
export type UserResponse = z.infer<typeof UserResponseSchema>

export const UserBasicSchema = z.object({
  first_name: z.string().optional().nullable(),
  last_name: z.string().optional().nullable(),
  middle_name: z.string().optional().nullable(),
  profile_picture: z.any().optional().nullable(),
  email: z.string().email("Invalid email address").optional().nullable(),
})

export const UserAdvancedSchema = z.object({
  nationality: z.string().optional().nullable(),
  date_de_naissance: z.string().optional().nullable(),
  addresse: z.string().optional().nullable(),
  ville_de_naissance: z.string().optional().nullable(),
  pays_des_residence: z.string().optional().nullable(),
  profession: z.string().optional().nullable(),
  etat_civil: z.string().optional().nullable(),
  ville_de_residence: z.string().optional().nullable(),
})

export const walletSchema = z.object({
  pochi: z.object({
    wallet: z.string(),
    name: z.string(),
    type: z.enum(["private", "business"]),
    status: z.enum(["active", "blocked"]),
    created_at: z.string(),
    qr_image: z.string(),
    balance: z.number(),
    last_update: z.string(),
    currency: z.string(),
    other_currencies: z
      .array(
        z.object({
          currency: z.string(),
          name: z.string(),
          amount: z.number(),
        }),
      )
      .optional()
      .default([]),
  }),
})

// Define TypeScript type from the schema
export type WalletResponse = z.infer<typeof walletSchema>
export type PochiWallet = WalletResponse["pochi"]

export type TransactionStatus = "in_progress" | "completed" | "failed" | "pending"
export type TransactionAction = "withdraw" | "deposit" | "transfer"
export type TransactionType = "withdraw" | "deposit" | "transfer" | "service"

// First, define the transaction object schema
const transactionObjectSchema = z.object({
  amount: z.string(),
  created_at: z.string(),
  currency: z.number(),
  id: z.string(),
  network: z.string(),
  note: z.string(),
  receiver: z.string(),
  receiver_phone: z.string().nullable(),
  sender: z.string(),
  status: z.enum(["in_progress", "completed", "failed", "pending"]),
  transaction_action: z.enum(["withdraw", "deposit", "transfer"]),
  transaction_type: z.enum(["withdraw", "deposit", "transfer", "service"]),
  service_name: z.string().optional(),
})

// Define the schema as an array of transactions
export const transactionSchema = z.array(transactionObjectSchema)

export interface Transaction {
  amount: string
  created_at: string
  currency: number
  to: string | null
  id: string
  receiver_profile: string | null
  network: string
  note: string
  receiver: string
  receiver_phone: string | null
  sender: string
  status: TransactionStatus
  transaction_action: TransactionAction
  transaction_type: TransactionType
  service_name?: string
}

export type TransactionResponse = Transaction[]

export interface SNELRequest {
  amount: string
  name: string
  created_at: string
  currency: number
  id: string
  note: string
  response: string | null
  saved_meter: string
  status: "pending" | "completed" | "failed"
  updated_at: string | null
  updated_by: string | null
}

export interface SERVICE_Partner {
  id: string
  business_name: string
  type_name: string
  status: string
  business: string
  pochi: string
  type: string
  created_at: string
}

export interface SNELMeterData {
  meter: string
  paid_wallet: string
  requests: SNELRequest[]
}

export interface CurrencyDetails {
  id: number
  code: string
  name: string
  exchange_rate: string
  is_active: boolean
  updated_at: string | null
}

export interface TopUpHistory {
  amount: string
  amount_in_default_currency: string
  confirmed_by: string | null
  created_at: string
  currency: number
  note: string | null
  status: string
  topup_no: string
  transaction_id: string
  transaction_image: string
  transaction_vias: string
  updated_at: string | null
  user_wallet: string
}

// First, define the sub-schemas
const SocialSchema = z.object({
  social: z.string(),
  username: z.string(),
})

// Define the wallet schema for business
const BusinessWalletSchema = z.object({
  pochi: z.string(),
  qr_code: z.string().optional(),
})

// Define the owner schema for business
const BusinessOwnerSchema = z.object({
  id: z.string(),
  name: z.string(),
})

export const BusinessSchema = z.object({
  id: z.string().uuid(),
  business_no: z.string(),
  registration_number: z.string().nullable(),
  country: z.string(),
  city: z.string(),
  adresse: z.string(),
  website: z.string().nullable(),
  description: z.string(),
  name: z.string(),
  image_logo: z.string().nullable(),
  image_banner: z.string().nullable(),
  status: z.enum(["active", "inactive", "suspended"]),
  type_business: z.enum(["formal", "informal"]),
  is_verified: z.boolean(),
  is_main: z.boolean(),
  verified_by: z.string().nullable(),
  date_verified: z.string().nullable(),
  documents: z.array(z.any()), // You might want to define a specific document schema
  emails: z.array(z.string()),
  telephones: z.array(z.string()),
  socials: z.array(SocialSchema),
  branches: z.array(z.any()), // You might want to define a specific branch schema
  categories: z.array(z.string()),
  personnel: z.array(z.any()).optional(), // Optional to handle both personnel and personnels
  personnels: z.array(z.any()).optional(), // Optional to handle both personnel and personnels
  products: z.array(z.any()).optional(),
  wallet: BusinessWalletSchema,
  owner: BusinessOwnerSchema,
})

// Define the array schema for multiple businesses
export const BusinessListSchema = z.array(BusinessSchema)

// Define TypeScript types from the schemas
export type Business = z.infer<typeof BusinessSchema>
export type BusinessList = z.infer<typeof BusinessListSchema>
export type BusinessSocial = z.infer<typeof SocialSchema>

// Business Category Schema
export const BusinessCategorySchema = z.object({
  id: z.string().uuid(),
  name: z.string(),
  description: z.string().nullable(),
  icon: z.string().nullable(),
  status: z.enum(["active", "inactive"]),
  created_at: z.string(),
  updated_at: z.string().nullable(),
})

// Define the array schema for multiple categories
export const BusinessCategoryListSchema = z.array(BusinessCategorySchema)

// Product Schema
export const ProductSchema = z.object({
  id: z.string().optional(),
  name: z.string(),
  price: z.number(),
  description: z.string().optional().nullable(),
})

// Define the array schema for multiple products
export const ProductListSchema = z.array(ProductSchema)

// Define TypeScript types from the schemas
export type BusinessCategory = z.infer<typeof BusinessCategorySchema>
export type BusinessCategoryList = z.infer<typeof BusinessCategoryListSchema>
export type Product = z.infer<typeof ProductSchema>
export type ProductList = z.infer<typeof ProductListSchema>

export const ItemSchema = z.object({
  qt: z.number(), // quantity
  amount: z.number(), // amount per item
  product: z.string(), // e.g., "cafee"
  currency: z.string(), // e.g., "CDF"
  product_id: z.string().nullable(), // can be null
  description: z.string(), // e.g., "cafee au lait avec sucre"
})

export const InvoiceSchema = z.object({
  id: z.string().uuid(), // "d8463cb6-3d44-4c0d-9803-38bc18674c8d"
  operator: z.string(), // "545a63b9-89a"
  currency: z.number(), // 2 (maybe ISO code)
  amount: z.string(), // "4800.000"
  referance_id: z.string(), // "d3a00542-33d"
  items: z.array(ItemSchema),
  payment_method: z.enum(["cash", "scan", "share", "bank", "other"]), // expand based on usage
  temp_qr: z.string().nullable(),
  status: z.enum(["pending", "completed", "failed", "cancelled"]), // update as needed
  created_at: z.string().datetime(),
  updated_at: z.string().datetime(),
  business: z.string(),
  client: z.string().nullable(),
})

export type Invoice = z.infer<typeof InvoiceSchema>

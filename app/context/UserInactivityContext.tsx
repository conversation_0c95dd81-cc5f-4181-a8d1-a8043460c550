/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable no-restricted-imports */
import React, { createContext, useEffect, useRef } from "react"
import { AppState, AppStateStatus } from "react-native"
import { navigationRef } from "../navigators"
import { storage } from "@/utils/storage"

const LOCK_TIME = 3000

export const UserInactivityProvider = ({ children }: any) => {
  const appState = useRef(AppState.currentState)

  useEffect(() => {
    const subscription = AppState.addEventListener("change", handleAppStateChange)
    return () => {
      subscription.remove()
    }
  }, [])

  const handleAppStateChange = (nextAppState: AppStateStatus) => {
    console.log("Current appState:", appState.current, "Next appState:", nextAppState)

    if (nextAppState === "background") {
      console.log("Navigating to FedhaSafe")
      if (navigationRef.isReady()) {
        navigationRef.navigate("Lock" as never)
      }
    } else {
      if (navigationRef.isReady() && navigationRef.canGoBack()) {
        navigationRef.goBack()
      }
    }
    if (nextAppState === "background") {
      recordStartTime()
    } else if (nextAppState === "active" && appState.current.match(/background/)) {
      const elapsedTime = Date.now() - (storage.getNumber("startTime") || 0)

      if (elapsedTime > LOCK_TIME) {
        navigationRef.navigate("Lock" as never)
      }
      //   const startTime = storage.getNumber("startTime")
      //   if (startTime) {
      //     const elapsedTime = Date.now() - startTime
      //     if (elapsedTime > LOCK_TIME) {
      //       // Navigate to FedhaSafe
      //       navigationRef.navigate("FedhaSafe" as never)
      //     }
      //   }
    }

    appState.current = nextAppState
  }

  const recordStartTime = () => {
    storage.set("startTime", Date.now())
  }

  return <>{children}</>
}

import { useEffect } from 'react'
import { useBusinessStore } from '@/store/BusinessStore'

/**
 * Hook to fetch and access business categories
 * @returns Object containing categories array, loading state, and error state
 */
export const useBusinessCategories = () => {
  const { 
    categories, 
    loading, 
    error, 
    fetchBusinessCategories 
  } = useBusinessStore()

  useEffect(() => {
    // Only fetch categories if we don't have any yet
    if (categories.length === 0) {
      fetchBusinessCategories()
    }
  }, [categories.length, fetchBusinessCategories])

  return {
    categories,
    loading,
    error,
    refetch: fetchBusinessCategories,
  }
}

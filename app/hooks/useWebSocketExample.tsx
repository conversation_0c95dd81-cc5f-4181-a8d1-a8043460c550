import React, { useState } from "react"
import { <PERSON>, Text, Button } from "react-native"
import { useWebSocket } from "./useWebSocket"
import { useAuthStore } from "@/store/AuthenticationStore"

// Example component showing how to use the WebSocket hook
export const WebSocketExample: React.FC = () => {
  const { user } = useAuthStore()
  const [status, setStatus] = useState<string>("unknown")
  const [messages, setMessages] = useState<string[]>([])

  const {
    isConnected,
    reconnectAttempts,
    lastError,
    sendMessage,
    reconnect,
    disconnect,
  } = useWebSocket({
    userId: user?.id || "default",
    
    // Handle incoming messages
    onMessage: (data) => {
      console.log("Received message:", data)
      setMessages(prev => [...prev, JSON.stringify(data)])
    },
    
    // Handle status changes
    onStatusChange: (newStatus) => {
      console.log("Status changed:", newStatus)
      setStatus(newStatus)
      
      if (newStatus === "activated") {
        console.log("🎉 User activated!")
        // Handle user activation
      }
    },
    
    // Handle connection events
    onConnect: () => {
      console.log("🔗 Connected to WebSocket")
      setMessages(prev => [...prev, "Connected to WebSocket"])
    },
    
    onDisconnect: () => {
      console.log("🔌 Disconnected from WebSocket")
      setMessages(prev => [...prev, "Disconnected from WebSocket"])
    },
    
    onError: (error) => {
      console.error("❌ WebSocket error:", error)
      setMessages(prev => [...prev, `Error: ${error}`])
    },
    
    // Optional configuration
    reconnectInterval: 5000,    // 5 seconds
    pingInterval: 20000,        // 20 seconds
    maxReconnectAttempts: 10,   // Max 10 attempts
  })

  const handleSendTestMessage = () => {
    const success = sendMessage({
      type: "test",
      data: { message: "Hello from client!" }
    })
    
    if (!success) {
      setMessages(prev => [...prev, "Failed to send message - not connected"])
    }
  }

  return (
    <View style={{ padding: 20 }}>
      <Text style={{ fontSize: 18, fontWeight: "bold", marginBottom: 10 }}>
        WebSocket Status
      </Text>
      
      <Text>Connected: {isConnected ? "✅ Yes" : "❌ No"}</Text>
      <Text>Status: {status}</Text>
      <Text>Reconnect Attempts: {reconnectAttempts}</Text>
      {lastError && <Text>Last Error: {lastError}</Text>}
      
      <View style={{ marginTop: 20, gap: 10 }}>
        <Button title="Send Test Message" onPress={handleSendTestMessage} />
        <Button title="Reconnect" onPress={reconnect} />
        <Button title="Disconnect" onPress={disconnect} />
      </View>
      
      <View style={{ marginTop: 20 }}>
        <Text style={{ fontWeight: "bold" }}>Messages:</Text>
        {messages.slice(-5).map((msg, index) => (
          <Text key={index} style={{ fontSize: 12, marginTop: 5 }}>
            {msg}
          </Text>
        ))}
      </View>
    </View>
  )
}

// Simple usage in a component
export const useUserStatusWebSocket = () => {
  const { user } = useAuthStore()
  const [userStatus, setUserStatus] = useState<string>("unknown")

  const { isConnected, sendMessage } = useWebSocket({
    userId: user?.id || "default",
    
    onStatusChange: (status) => {
      setUserStatus(status)
      
      // Handle different status types
      switch (status) {
        case "activated":
          console.log("🎉 User account activated!")
          // Show success notification
          break
        case "suspended":
          console.log("⚠️ User account suspended!")
          // Show warning notification
          break
        case "verified":
          console.log("✅ User account verified!")
          // Update UI accordingly
          break
        default:
          console.log(`📊 User status: ${status}`)
      }
    },
    
    onMessage: (data) => {
      // Handle other message types
      if (data.type === "notification") {
        console.log("🔔 New notification:", data.data)
        // Show notification to user
      }
    },
  })

  return {
    isConnected,
    userStatus,
    sendMessage,
  }
}

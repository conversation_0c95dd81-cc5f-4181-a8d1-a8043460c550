/* eslint-disable import/no-unresolved */
import { useEffect, useRef } from "react"
import { useAuthStore } from "@/store/AuthenticationStore"
import { loadString } from "@/utils/storage"
import { navigationRef } from "@/navigators/navigationUtilities"
import NetInfo from "@react-native-community/netinfo"
import { useNetworkStore } from "@/store/useNetworkStore"

const TOKEN_CHECK_INTERVAL = 30000 // Check every 30 seconds
const MAX_TOKEN_AGE = 24 * 60 * 60 * 1000 // 24 hours in milliseconds

export const useTokenValidation = () => {
  const { logout, refreshAuthToken, isAuthenticated } = useAuthStore()
  const checkingRef = useRef(false)
  const networkStore = useNetworkStore()

  const validateToken = async () => {
    if (checkingRef.current) return
    checkingRef.current = true

    try {
      // Check network connection first
      const netInfo = await NetInfo.fetch()
      if (!netInfo.isConnected) {
        networkStore.checkInternet()
        return
      }

      const accessToken = await loadString("accessToken")
      const refreshToken = await loadString("refreshToken")

      if (!accessToken || !refreshToken) {
        console.warn("❌ No tokens found, logging out...")
        handleLogout()
        return
      }

      // Check token age
      const tokenTimestamp = await loadString("tokenTimestamp")
      if (tokenTimestamp) {
        const tokenAge = Date.now() - parseInt(tokenTimestamp)
        if (tokenAge > MAX_TOKEN_AGE) {
          console.warn("🔒 Token too old, logging out...")
          handleLogout()
          return
        }
      }

      // Try to refresh the token
      await refreshAuthToken()
    } catch (error) {
      console.error("🔴 Token validation failed:", error)

      // Check if it's a "no refresh token" error
      if (error.message === "No refresh token found") {
        console.log("🔴 No refresh token in validation, ending session...")
      }

      handleLogout()
    } finally {
      checkingRef.current = false
    }
  }

  const handleLogout = () => {
    logout()
    // Navigation is now handled in the logout function itself
    // No need to navigate here to avoid double navigation
  }

  useEffect(() => {
    if (!isAuthenticated) {
      handleLogout()
      return
    }

    // Initial check
    validateToken()

    // Set up interval for periodic checks
    const interval = setInterval(validateToken, TOKEN_CHECK_INTERVAL)

    // Set up network listener
    const unsubscribe = NetInfo.addEventListener((state) => {
      if (state.isConnected) {
        validateToken()
      }
    })

    return () => {
      clearInterval(interval)
      unsubscribe()
    }
  }, [isAuthenticated])

  return validateToken
}

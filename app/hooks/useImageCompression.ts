import { useState, useCallback } from "react"
import { Alert } from "react-native"
import { 
  compressImage, 
  smartCompress, 
  CompressionResult, 
  CompressionOptions,
  validateImageSize,
  showCompressionResults
} from "@/utils/imageCompression"

export interface UseImageCompressionReturn {
  isCompressing: boolean
  compressedUri: string | null
  compressionResult: CompressionResult | null
  compressImageAsync: (imageUri: string, options?: CompressionOptions) => Promise<string | null>
  smartCompressAsync: (imageUri: string) => Promise<string | null>
  validateAndCompress: (imageUri: string, showResults?: boolean) => Promise<string | null>
  reset: () => void
}

/**
 * Hook for handling image compression with loading states and error handling
 * 
 * @returns UseImageCompressionReturn
 */
export const useImageCompression = (): UseImageCompressionReturn => {
  const [isCompressing, setIsCompressing] = useState(false)
  const [compressedUri, setCompressedUri] = useState<string | null>(null)
  const [compressionResult, setCompressionResult] = useState<CompressionResult | null>(null)

  /**
   * Compress image with custom options
   */
  const compressImageAsync = useCallback(async (
    imageUri: string, 
    options?: CompressionOptions
  ): Promise<string | null> => {
    try {
      setIsCompressing(true)
      console.log("🔄 Starting image compression...")

      const result = await compressImage(imageUri, options)
      setCompressionResult(result)

      if (result.success && result.uri) {
        setCompressedUri(result.uri)
        console.log("✅ Image compression successful")
        return result.uri
      } else {
        console.error("❌ Image compression failed:", result.error)
        Alert.alert(
          "Compression Failed",
          result.error || "Failed to compress image. Please try again.",
          [{ text: "OK" }]
        )
        return null
      }
    } catch (error) {
      console.error("❌ Image compression error:", error)
      Alert.alert(
        "Error",
        "An unexpected error occurred while compressing the image.",
        [{ text: "OK" }]
      )
      return null
    } finally {
      setIsCompressing(false)
    }
  }, [])

  /**
   * Smart compress with automatic quality selection
   */
  const smartCompressAsync = useCallback(async (imageUri: string): Promise<string | null> => {
    try {
      setIsCompressing(true)
      console.log("🧠 Starting smart compression...")

      const result = await smartCompress(imageUri)
      setCompressionResult(result)

      if (result.success && result.uri) {
        setCompressedUri(result.uri)
        console.log("✅ Smart compression successful")
        return result.uri
      } else {
        console.error("❌ Smart compression failed:", result.error)
        Alert.alert(
          "Compression Failed",
          result.error || "Failed to compress image. Please try again.",
          [{ text: "OK" }]
        )
        return null
      }
    } catch (error) {
      console.error("❌ Smart compression error:", error)
      Alert.alert(
        "Error",
        "An unexpected error occurred while compressing the image.",
        [{ text: "OK" }]
      )
      return null
    } finally {
      setIsCompressing(false)
    }
  }, [])

  /**
   * Validate image size and compress if needed
   */
  const validateAndCompress = useCallback(async (
    imageUri: string,
    showResults: boolean = false
  ): Promise<string | null> => {
    try {
      setIsCompressing(true)
      console.log("🔍 Validating and compressing image...")

      // First validate if compression is needed
      const isValidSize = await validateImageSize(imageUri, 10) // 10MB limit
      
      if (isValidSize) {
        console.log("✅ Image size is acceptable, no compression needed")
        setCompressedUri(imageUri)
        return imageUri
      }

      console.log("⚠️ Image too large, compressing...")
      
      // Compress the image
      const result = await smartCompress(imageUri)
      setCompressionResult(result)

      if (result.success && result.uri) {
        setCompressedUri(result.uri)
        
        // Show results if requested
        if (showResults) {
          showCompressionResults(result)
        }
        
        console.log("✅ Image validated and compressed successfully")
        return result.uri
      } else {
        console.error("❌ Image compression failed:", result.error)
        Alert.alert(
          "Upload Error",
          "The image is too large and could not be compressed. Please try a smaller image.",
          [{ text: "OK" }]
        )
        return null
      }
    } catch (error) {
      console.error("❌ Validate and compress error:", error)
      Alert.alert(
        "Error",
        "An error occurred while processing the image.",
        [{ text: "OK" }]
      )
      return null
    } finally {
      setIsCompressing(false)
    }
  }, [])

  /**
   * Reset compression state
   */
  const reset = useCallback(() => {
    setIsCompressing(false)
    setCompressedUri(null)
    setCompressionResult(null)
  }, [])

  return {
    isCompressing,
    compressedUri,
    compressionResult,
    compressImageAsync,
    smartCompressAsync,
    validateAndCompress,
    reset,
  }
}

/**
 * Simple hook for one-time image compression
 */
export const useSimpleImageCompression = () => {
  const [isCompressing, setIsCompressing] = useState(false)

  const compressForUpload = useCallback(async (imageUri: string): Promise<string | null> => {
    try {
      setIsCompressing(true)
      console.log("📤 Compressing image for upload...")

      // Use smart compression with upload-optimized settings
      const result = await compressImage(imageUri, {
        maxWidth: 1024,
        maxHeight: 1024,
        quality: 0.7,
      })

      if (result.success && result.uri) {
        console.log("✅ Image ready for upload")
        return result.uri
      } else {
        console.error("❌ Upload compression failed:", result.error)
        Alert.alert(
          "Compression Error",
          "Could not prepare image for upload. Please try again.",
          [{ text: "OK" }]
        )
        return null
      }
    } catch (error) {
      console.error("❌ Upload compression error:", error)
      Alert.alert(
        "Error",
        "Failed to process image for upload.",
        [{ text: "OK" }]
      )
      return null
    } finally {
      setIsCompressing(false)
    }
  }, [])

  return {
    isCompressing,
    compressForUpload,
  }
}

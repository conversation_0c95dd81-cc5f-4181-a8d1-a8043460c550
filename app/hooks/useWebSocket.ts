/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect, useRef, useCallback } from "react"
import { storage } from "@/utils/storage"

interface WebSocketMessage {
  type: string
  status?: string
  data?: any
}

interface UseWebSocketOptions {
  userId: string | number
  onMessage?: (data: WebSocketMessage) => void
  onStatusChange?: (status: string) => void
  onConnect?: () => void
  onDisconnect?: () => void
  onError?: (error: Event) => void
  reconnectInterval?: number
  pingInterval?: number
  maxReconnectAttempts?: number
}

interface WebSocketState {
  isConnected: boolean
  reconnectAttempts: number
  lastError: string | null
}

export const useWebSocket = (options: UseWebSocketOptions) => {
  const {
    userId,
    onMessage,
    onStatusChange,
    onConnect,
    onDisconnect,
    onError,
    reconnectInterval = 5000,
    pingInterval = 20000,
    maxReconnectAttempts = 10,
  } = options

  const ws = useRef<WebSocket | null>(null)
  const pingIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const stateRef = useRef<WebSocketState>({
    isConnected: false,
    reconnectAttempts: 0,
    lastError: null,
  })

  // Get WebSocket URL from environment or config
  const getWebSocketUrl = useCallback(() => {
    const baseUrl = process.env.WS_API_URL || "wss://stageapi.fedha.link/ws"
    const token = storage.getString("accessToken")
    return `${baseUrl}/user/status/${userId}/?token=${token}`
  }, [userId])

  // Send ping to keep connection alive
  const sendPing = useCallback(() => {
    if (ws.current && ws.current.readyState === WebSocket.OPEN) {
      try {
        ws.current.send(JSON.stringify({ type: "ping" }))
        console.log("🏓 WebSocket ping sent")
      } catch (error) {
        console.error("❌ Failed to send ping:", error)
      }
    }
  }, [])

  // Start ping interval
  const startPingInterval = useCallback(() => {
    if (pingIntervalRef.current) {
      clearInterval(pingIntervalRef.current)
    }

    pingIntervalRef.current = setInterval(sendPing, pingInterval)
    console.log(`🔄 Ping interval started (${pingInterval}ms)`)
  }, [sendPing, pingInterval])

  // Stop ping interval
  const stopPingInterval = useCallback(() => {
    if (pingIntervalRef.current) {
      clearInterval(pingIntervalRef.current)
      pingIntervalRef.current = null
      console.log("⏹️ Ping interval stopped")
    }
  }, [])

  // Connect to WebSocket
  const connectWebSocket = useCallback(() => {
    // Don't connect if already connected or if max attempts reached
    if (ws.current?.readyState === WebSocket.OPEN) {
      console.log("🔗 WebSocket already connected")
      return
    }

    if (stateRef.current.reconnectAttempts >= maxReconnectAttempts) {
      console.error(`❌ Max reconnection attempts (${maxReconnectAttempts}) reached`)
      return
    }

    try {
      console.log(
        `🔌 Connecting to WebSocket... (Attempt ${stateRef.current.reconnectAttempts + 1})`,
      )

      const wsUrl = getWebSocketUrl()
      ws.current = new WebSocket(wsUrl)

      ws.current.onopen = () => {
        console.log("✅ WebSocket connected successfully")
        stateRef.current.isConnected = true
        stateRef.current.reconnectAttempts = 0
        stateRef.current.lastError = null

        startPingInterval()
        onConnect?.()
      }

      ws.current.onmessage = (event) => {
        try {
          const data: WebSocketMessage = JSON.parse(event.data)
          console.log("📨 WebSocket message received:", data)

          // Handle ping/pong
          if (data.type === "pong") {
            console.log("🏓 Pong received")
            return
          }

          // Handle status updates
          if (data.status) {
            console.log(`📊 Status update: ${data.status}`)
            onStatusChange?.(data.status)
          }

          // Call custom message handler
          onMessage?.(data)
        } catch (error) {
          console.error("❌ Failed to parse WebSocket message:", error)
        }
      }

      ws.current.onclose = (event) => {
        console.log(`🔌 WebSocket disconnected (Code: ${event.code}, Reason: ${event.reason})`)
        stateRef.current.isConnected = false

        stopPingInterval()
        onDisconnect?.()

        // Attempt to reconnect if not a manual close
        if (event.code !== 1000 && stateRef.current.reconnectAttempts < maxReconnectAttempts) {
          scheduleReconnect()
        }
      }

      ws.current.onerror = (error) => {
        console.error("❌ WebSocket error:", error)
        stateRef.current.lastError = "Connection error"
        onError?.(error)

        // Close the connection to trigger reconnection
        if (ws.current) {
          ws.current.close()
        }
      }
    } catch (error) {
      console.error("❌ Failed to create WebSocket connection:", error)
      stateRef.current.lastError = "Failed to create connection"
      scheduleReconnect()
    }
  }, [
    getWebSocketUrl,
    maxReconnectAttempts,
    startPingInterval,
    stopPingInterval,
    onConnect,
    onDisconnect,
    onError,
    onMessage,
    onStatusChange,
  ])

  // Schedule reconnection
  const scheduleReconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
    }

    stateRef.current.reconnectAttempts += 1

    console.log(
      `🔄 Scheduling reconnection in ${reconnectInterval}ms (Attempt ${stateRef.current.reconnectAttempts}/${maxReconnectAttempts})`,
    )

    reconnectTimeoutRef.current = setTimeout(() => {
      connectWebSocket()
    }, reconnectInterval)
  }, [connectWebSocket, reconnectInterval, maxReconnectAttempts])

  // Manual reconnect function
  const reconnect = useCallback(() => {
    console.log("🔄 Manual reconnection triggered")
    stateRef.current.reconnectAttempts = 0

    if (ws.current) {
      ws.current.close()
    }

    connectWebSocket()
  }, [connectWebSocket])

  // Send message function
  const sendMessage = useCallback((message: WebSocketMessage) => {
    if (ws.current && ws.current.readyState === WebSocket.OPEN) {
      try {
        ws.current.send(JSON.stringify(message))
        console.log("📤 Message sent:", message)
        return true
      } catch (error) {
        console.error("❌ Failed to send message:", error)
        return false
      }
    } else {
      console.warn("⚠️ WebSocket not connected, cannot send message")
      return false
    }
  }, [])

  // Disconnect function
  const disconnect = useCallback(() => {
    console.log("🔌 Manually disconnecting WebSocket")

    // Clear intervals and timeouts
    stopPingInterval()
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
      reconnectTimeoutRef.current = null
    }

    // Close connection
    if (ws.current) {
      ws.current.close(1000, "Manual disconnect")
      ws.current = null
    }

    stateRef.current.isConnected = false
    stateRef.current.reconnectAttempts = 0
  }, [stopPingInterval])

  // Initialize WebSocket connection
  useEffect(() => {
    if (userId) {
      connectWebSocket()
    }

    // Cleanup on unmount
    return () => {
      disconnect()
    }
  }, [userId, connectWebSocket, disconnect])

  return {
    isConnected: stateRef.current.isConnected,
    reconnectAttempts: stateRef.current.reconnectAttempts,
    lastError: stateRef.current.lastError,
    sendMessage,
    reconnect,
    disconnect,
  }
}

/* eslint-disable no-restricted-imports */
/* eslint-disable react-native/no-inline-styles */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable import/no-unresolved */
import { FC, useState, useEffect, useMemo, useCallback } from "react"
import {
  ViewStyle,
  View,
  Alert,
  FlatList,
  TextStyle,
  TouchableOpacity,
  Image,
  // TextInput,
  ImageStyle,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Modal,
  Keyboard,
} from "react-native"
import { PayInvoice } from "@/components"
import { AppStackScreenProps } from "@/navigators"
import {
  AmountInput,
  Button,
  ChangeCurency,
  Header,
  ReceiverWidget,
  Screen,
  TextField,
  Text,
  Icon,
  NumberPad,
} from "@/components"
import { useStores } from "@/store/rootStore"
import { colors, spacing } from "@/theme"
import { TransferCash } from "@/services/api"
import { zodResolver } from "@hookform/resolvers/zod"
import { Controller, useForm } from "react-hook-form"
import { z } from "zod"
import { capitalizeFirstLetter, getNameInitials } from "@/utils/actions"
import { Avatar } from "react-native-paper"
import { useTransactionStore } from "@/store/TransactionStore"
import React from "react"

interface LipanaFedhaScreenProps extends AppStackScreenProps<"LipanaFedha"> {}

interface UserDetails {
  firstName: string
  lastName: string
  walletId: string
  avatar?: string | null
}

interface RecentRecipient {
  id: string
  name: string
  walletId: string
  avatar?: string
  lastAmount: number
  lastDate: string
  recipientProfile?: string | null
}

const paymentSchema = z.object({
  amount: z.string().min(1, "Le montant est obligatoire"),
  note: z.string().optional(),
  receiver: z.string().optional(),
  currency: z.string().optional(),
})

type PaymentFormData = z.infer<typeof paymentSchema>

export const LipanaFedhaScreen: FC<LipanaFedhaScreenProps> = ({ navigation, route }) => {
  // Check if we should hide the tab bar
  const hideTabBar = route.params?.hideTabBar === true

  // State for receiver details
  const [receiverDetails, setReceiverDetails] = useState<UserDetails | null>(null)

  // State for invoice modal
  const [showInvoiceModal, setShowInvoiceModal] = useState(false)
  const [invoiceData, setInvoiceData] = useState<any>(null)

  // Handle scan success with different data formats
  const handleScanSuccess = useCallback((data: any) => {
    // console.log("LipanaFedha:", data)
    const scannedData = data?.scannedData
    const dataType = data?.dataType

    // Handle invoice payment data
    if (dataType === "invoice_payment") {
      setInvoiceData(scannedData)
      setShowInvoiceModal(true)
      return
    }

    // Handle business account data
    if (dataType === "business_account") {
      setReceiverDetails({
        firstName: scannedData.name || "",
        lastName: "",
        walletId: scannedData.wallet_id,
        avatar: scannedData.profile_picture,
      })
      return
    }

    // Handle personal account data
    if (dataType === "personal_account") {
      setReceiverDetails({
        firstName: scannedData.first_name || "",
        lastName: scannedData.last_name || "",
        walletId: scannedData.wallet_id,
        avatar: scannedData.profile_picture,
      })
      return
    }

    // Handle other data formats
    // if (scannedData?.wallet_id) {
    //   setReceiverDetails({
    //     firstName: scannedData.name || scannedData.first_name || "",
    //     lastName: scannedData.last_name || "",
    //     walletId: scannedData.wallet_id,
    //     avatar: scannedData.profile_picture,
    //   })
    // } else {
    //   // If we can't parse the data, show an error
    //   console.error("Unrecognized QR code format:", data)
    //   Alert.alert("Format non reconnu", "Le code QR scanné n'est pas dans un format reconnu.")
    // }
    // console.log("Scanned data:", ScnDat)

    // Handle different data formats that might come from the scanner
    // if (typeof data === "string") {
    //   // Handle string case (probably just the wallet ID)
    //   setReceiverDetails({
    //     firstName: "",
    //     lastName: "",
    //     walletId: data,
    //   })
    // } else if (data?.scannedData) {
    //   // Handle data coming from the ScannerScreen via route params
    //   const scannedData = data.scannedData

    //   if (typeof scannedData === "string") {
    //     setReceiverDetails({
    //       firstName: "",
    //       lastName: "",
    //       walletId: scannedData,
    //     })
    //   } else if (scannedData?.userDetails) {
    //     setReceiverDetails({
    //       firstName: scannedData.userDetails.firstName || "",
    //       lastName: scannedData.userDetails.lastName || "",
    //       walletId: scannedData.userDetails.walletId || scannedData.walletId || "",
    //       avatar: scannedData.userDetails.avatar || null,
    //     })
    //   } else if (scannedData?.walletId) {
    //     setReceiverDetails({
    //       firstName: "",
    //       lastName: "",
    //       walletId: scannedData.walletId,
    //     })
    //   }
    // } else if (data?.userDetails) {
    //   // Handle object case with user details directly
    //   setReceiverDetails({
    //     firstName: data.userDetails.firstName || "",
    //     lastName: data.userDetails.lastName || "",
    //     walletId: data.userDetails.walletId || data.walletId || "",
    //     avatar: data.userDetails.avatar || null,
    //   })
    // } else if (data?.walletId) {
    //   // Handle simple object with walletId
    //   setReceiverDetails({
    //     firstName: "",
    //     lastName: "",
    //     walletId: data.walletId,
    //   })
    // } else {
    //   // If we can't parse the data, show an error
    //   console.error("Unrecognized QR code format:", data)
    //   Alert.alert("Format non reconnu", "Le code QR scanné n'est pas dans un format reconnu.")
    // }
  }, [])

  // Check if we have scanned data from the ScannerScreen
  useEffect(() => {
    if (route.params?.scannedData) {
      // Process the scanned data
      handleScanSuccess(route.params)
    }
  }, [route.params, handleScanSuccess])

  // Hide the tab bar if needed
  useEffect(() => {
    if (hideTabBar) {
      // Get the parent navigator (which should be the tab navigator)
      const parent = navigation.getParent()
      if (parent) {
        // Hide the tab bar
        parent.setOptions({
          tabBarStyle: { display: "none" },
        })

        // Restore the tab bar when unmounting
        return () => {
          parent.setOptions({
            tabBarStyle: undefined,
          })
        }
      }
    }

    return undefined
  }, [hideTabBar, navigation])
  const {
    appsettings: { currency, getExchangeRate, currencies, fetchCurrencies, getServerCurrencyCode },
    fedhapochi: { currentBalance },
    beneficiary: { beneficiaries, fetchBeneficiaries },
    transactions: { fetchRecentPayments, recentPayments },
  } = useStores()

  const [searchQuery, setSearchQuery] = useState("")
  // No need to hide the tab bar anymore since we're using a different navigation approach

  useEffect(() => {
    fetchBeneficiaries()
    fetchRecentPayments()
  }, [fetchBeneficiaries, fetchRecentPayments])

  const filteredBeneficiaries = useMemo(() => {
    if (!Array.isArray(beneficiaries)) return []

    if (!searchQuery.trim()) return beneficiaries

    const query = searchQuery.toLowerCase().trim()
    return beneficiaries.filter((b) => {
      const beneficiaryName = (b?.beneficiary_name || b?.name || "").toLowerCase()
      const walletId = (b?.wallet || b?.walletId || "").toLowerCase()

      return beneficiaryName.includes(query) || walletId.includes(query)
    })
  }, [beneficiaries, searchQuery])

  const [showCurrencyModal, setShowCurrencyModal] = useState(false)
  // const [isSubmitting, setIsSubmitting] = useState(false)
  const [allRecentPayments, setAllRecentPayments] = useState<any>([])
  const [displayedRecipients, setDisplayedRecipients] = useState<any>([])
  const [showConfirmModal, setShowConfirmModal] = useState(false)
  const [transferMessage, setTransferMessage] = useState("")
  const [isSubmittingPayment, setIsSubmittingPayment] = useState(false)
  const [showKeyboard, setShowKeyboard] = useState(false)

  // Add keyboard listeners
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener("keyboardDidShow", () => {
      setShowKeyboard(true)
    })
    const keyboardDidHideListener = Keyboard.addListener("keyboardDidHide", () => {
      setShowKeyboard(false)
    })

    return () => {
      keyboardDidShowListener.remove()
      keyboardDidHideListener.remove()
    }
  }, [])

  // console.log(recentPayments)

  // Define loadRecentRecipients function
  const loadRecentRecipients = useCallback(() => {
    // Check if recentPayments is an array before trying to map over it
    if (!recentPayments || !Array.isArray(recentPayments)) {
      console.log("Recent payments is not an array:", recentPayments)
      setAllRecentPayments([])
      setDisplayedRecipients([])
      return
    }

    const formattedRecipients = recentPayments.map((payment: any) => ({
      id: payment.id || String(Math.random()),
      name: payment.receiver_name || "Unknown",
      walletId: payment.receiver,
      lastAmount: payment.amount,
      recipientProfile: payment.receiver_profile,
      lastDate: new Date(payment.created_at).toLocaleDateString(),
    }))

    setAllRecentPayments(formattedRecipients) // Store all payments
    setDisplayedRecipients(formattedRecipients.slice(0, 5)) // Show only 5
  }, [recentPayments, setAllRecentPayments, setDisplayedRecipients])

  // Load recent recipients when recentPayments changes
  useEffect(() => {
    // Only load recent recipients when recentPayments is available
    if (recentPayments && Array.isArray(recentPayments)) {
      loadRecentRecipients()
    }
  }, [recentPayments, loadRecentRecipients])

  const {
    control,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    watch,
    setValue,
  } = useForm<PaymentFormData>({
    resolver: zodResolver(paymentSchema),
    defaultValues: {
      amount: "",
      note: "",
      currency: currency,
      receiver: "",
    },
  })

  // Update receiver when receiverDetails changes
  useEffect(() => {
    if (receiverDetails?.walletId) {
      setValue("receiver", receiverDetails.walletId)
    }
  }, [receiverDetails, setValue])

  useEffect(() => {
    if (searchQuery) {
      const filtered = allRecentPayments.filter(
        (r: { name: string; walletId: string }) =>
          r.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          r.walletId.toLowerCase().includes(searchQuery.toLowerCase()),
      )
      setDisplayedRecipients(filtered)
    } else {
      setDisplayedRecipients(allRecentPayments.slice(0, 5))
    }
  }, [searchQuery, allRecentPayments])

  const renderRecipientItem = ({ item }: { item: RecentRecipient }) => (
    <TouchableOpacity
      style={$recipientItem}
      onPress={() => {
        setReceiverDetails({
          firstName: item.name.split(" ")[0],
          lastName: item.name.split(" ")[1] || "",
          walletId: item.walletId,
          avatar: item.recipientProfile,
        })
      }}
    >
      <View style={$recipientInfo}>
        {item.recipientProfile ? (
          <Avatar.Image
            size={50}
            source={{ uri: item.recipientProfile }}
            style={{ backgroundColor: colors.palette.neutral900 }}
          />
        ) : (
          <Avatar.Text
            size={50}
            label={getNameInitials(item.name)}
            style={{ backgroundColor: colors.palette.neutral900 }}
            labelStyle={$avatarLabel}
          />
        )}
        <View>
          <Text style={$recipientName}>{item.name}</Text>
          <Text style={$lastTransaction}>
            Dernier: {item.lastAmount} {currency} • {item.lastDate}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  )

  // const howitworks = () => {
  //   console.log("comment ca marche")
  // }

  const handlePay = async (formData: PaymentFormData) => {
    // Validate receiver
    if (!receiverDetails) {
      Alert.alert("Erreur", "Veuillez sélectionner un bénéficiaire")
      return
    }

    // Validate amount
    const amount = Number(formData.amount)
    if (!amount || amount <= 0) {
      Alert.alert("Erreur", "Veuillez saisir un montant valide")
      return
    }

    // Validate minimum amounts based on currency
    if (currency === "USD" && amount < 2) {
      Alert.alert("Erreur", "Le montant minimum est de 2 USD")
      return
    }
    if (currency === "FC" && amount < 1000) {
      Alert.alert("Erreur", "Le montant minimum est de 1000 FC")
      return
    }

    // Show confirmation modal
    setShowConfirmModal(true)
  }

  const handleConfirmTransfer = async () => {
    try {
      setIsSubmittingPayment(true)
      setShowConfirmModal(false)

      const requestData = {
        receiver: receiverDetails?.walletId ?? "",
        amount: Number(watch("amount")),
        currency: getServerCurrencyCode(currency),
        note: transferMessage || "Paiement via Lipa na Fedha",
      }

      const response = await TransferCash(requestData)

      if (response.success) {
        Alert.alert("Succès", "Paiement effectué avec succès", [
          {
            text: "OK",
            onPress: () => {
              reset()
              setReceiverDetails(null)
              navigation.goBack()
            },
          },
        ])
      } else {
        throw new Error(response.message || "Le paiement a échoué")
      }
    } catch (error: any) {
      console.error("Payment Error:", error)
      Alert.alert(
        "Erreur",
        error.message || "Une erreur s'est produite lors du paiement. Veuillez réessayer.",
      )
    } finally {
      setIsSubmittingPayment(false)
    }
  }

  return (
    <>
      <Header
        leftIcon="backicon"
        onLeftPress={navigation.goBack}
        // onRightPress={howitworks}
        // rightIcon="question"
        title="Lipa na Fedha"
      />
      <Screen preset="fixed" style={$root} safeAreaEdges={["bottom"]} statusBarStyle="dark">
        {!receiverDetails && (
          <>
            <View style={$searchContainer}>
              <TextField
                value={searchQuery}
                onChangeText={setSearchQuery}
                placeholder="Rechercher un Nom ou Pochi ..."
                containerStyle={$searchInput}
                numberOfLines={1}
                RightAccessory={() => (
                  <Icon
                    icon="search"
                    size={25}
                    color={colors.palette.neutral400}
                    containerStyle={{ padding: spacing.md }}
                  />
                )}
              />
              {/* <TouchableOpacity
                style={$scannerButton}
                onPress={() => {
                  // Navigate to the ScannerScreen instead of showing the Scanner component
                  navigation.navigate("Scanner", {
                    hideTabBar: true,
                    onScanComplete: handleScanSuccess,
                    returnScreen: "LipanaFedha",
                    user_type: "standard", // or "business" depending on the user type
                  })
                }}
              >
                <Icon icon="scan" size={24} />
                <Text style={$scannerText}>Scanner</Text>
              </TouchableOpacity> */}
            </View>

            <Text style={$sectionTitle}>
              {searchQuery ? "Résultats de recherche" : "Paiements récents"}
            </Text>
            {displayedRecipients.length > 0 ? (
              <FlatList
                data={displayedRecipients}
                renderItem={renderRecipientItem}
                keyExtractor={(item) => item.id}
                style={$recipientList}
                contentContainerStyle={$listContent} // Add padding to avoid content being hidden by footer
                scrollEnabled={true}
              />
            ) : (
              <View style={$emptyStateContainer}>
                <Icon icon="monytrans" size={40} color={colors.palette.neutral400} />
                <Text style={$emptyStateText}>
                  {searchQuery ? "Aucun résultat trouvé" : "Aucun paiement récent"}
                </Text>
              </View>
            )}
            {/* utility and function are what important,  if something doesnt work, it ugly. */}

            {/* SO WILL TRY TO SEE HOW I CAN INCORPORATE THIS IN THE SEARCH BAR only when the user is searching to add result of the beneficire */}
            <Text style={$sectionTitle}>Bénéficiaires</Text>
            {filteredBeneficiaries.length > 0 ? (
              <FlatList
                data={filteredBeneficiaries}
                renderItem={({ item }: { item: any }) => (
                  <TouchableOpacity
                    style={$recipientItem}
                    onPress={() => {
                      const beneficiaryName = item?.beneficiary_name || item?.name || ""
                      const names = beneficiaryName.split(" ")
                      setReceiverDetails({
                        firstName: names[0] || "",
                        lastName: names[1] || "",
                        walletId: item?.wallet || item?.walletId || "",
                        avatar: item?.profile_image || null,
                      })
                    }}
                  >
                    <View style={$recipientInfo}>
                      <View style={$avatarPlaceholder}>
                        {item.profile_image ? (
                          <Avatar.Image
                            size={50}
                            source={{ uri: item.profile_image }}
                            style={{ backgroundColor: colors.palette.neutral900 }}
                          />
                        ) : (
                          <Avatar.Text
                            size={50}
                            label={getNameInitials(item.beneficiary_name || item.name || "")}
                            style={{ backgroundColor: colors.palette.neutral900 }}
                            labelStyle={$avatarLabel}
                          />
                        )}
                      </View>
                      <View>
                        <Text style={$recipientName}>
                          {capitalizeFirstLetter(item?.beneficiary_name || item?.name || "")}
                        </Text>
                        <Text style={$lastTransaction}>{item?.wallet || item?.walletId || ""}</Text>
                      </View>
                    </View>
                  </TouchableOpacity>
                )}
                keyExtractor={(item) => item?.wallet || item?.walletId || String(Math.random())}
                style={$recipientList}
                contentContainerStyle={$listContent}
              />
            ) : (
              <View style={$emptyStateContainer}>
                <Icon icon="group" size={40} color={colors.palette.neutral400} />
                <Text style={$emptyStateText}>
                  {searchQuery ? "Aucun résultat trouvé" : "Aucun bénéficiaire trouvé"}
                </Text>
              </View>
            )}
          </>
        )}

        {receiverDetails && (
          <KeyboardAvoidingView
            behavior={Platform.OS === "ios" ? "padding" : "height"}
            style={$keyboardAvoidingView}
          >
            <ScrollView contentContainerStyle={$scrollContent} keyboardShouldPersistTaps="handled">
              <ReceiverWidget
                username={`${receiverDetails.firstName} ${receiverDetails.lastName}`}
                avatar={receiverDetails.avatar}
              />

              <View style={$formContainer}>
                <Controller
                  control={control}
                  name="amount"
                  rules={{
                    required: "Le montant est obligatoire",
                    validate: (value) => {
                      const amount = parseFloat(value)
                      if (isNaN(amount)) return "Le montant doit être un nombre valide"

                      // Get exchange rate and convert amounts using API rates
                      if (currency === "USD") {
                        if (amount < 1) return "Le montant minimum est de 2 USD"
                        // Convert balance to USD using API exchange rate
                        const balanceInUSD = getExchangeRate("FC", "USD") * currentBalance
                        if (amount > balanceInUSD)
                          return `Solde insuffisant. Votre solde est de ${balanceInUSD.toFixed(2)} USD`
                      } else if (currency === "FC") {
                        if (amount < 2000) return "Le montant minimum est de 2000 FC"
                        // Convert balance to FC using API exchange rate
                        const balanceInFC = getExchangeRate("USD", "FC") * currentBalance
                        if (amount > balanceInFC)
                          return `Solde insuffisant. Votre solde est de ${balanceInFC.toFixed(2)} FC`
                      }
                      return true
                    },
                  }}
                  render={({ field: { onChange, value } }) => (
                    <View style={$formContainer}>
                      <View style={$amountInputContainer}>
                        <TextField
                          value={value || "0.00"}
                          editable={false}
                          containerStyle={$amountInput}
                          style={{
                            fontSize: 32,
                            textAlign: "center",
                            height: 48,
                            textAlignVertical: "center",
                          }}
                          // inputWrapperStyle={$inputWrapper}
                          RightAccessory={() => (
                            <TouchableOpacity
                              style={$currencyButton}
                              onPress={() => setShowCurrencyModal(true)}
                            >
                              <Text style={$currencyText}>{currency}</Text>
                            </TouchableOpacity>
                          )}
                        />
                      </View>
                      <NumberPad
                        onPress={(num) => {
                          if (num === "⌫") {
                            // Handle backspace
                            onChange(value?.slice(0, -1) || "0.00")
                            return
                          }

                          // Get current numeric value
                          const currentValue = value === "0.00" ? "" : value || ""
                          const newValue = currentValue + num
                          const numericValue = parseFloat(newValue)

                          // Skip if not a valid number
                          if (isNaN(numericValue)) return

                          // Only check maximum limits while typing, not minimum limits
                          if (currency === "USD") {
                            if (numericValue > 300) {
                              Alert.alert("Erreur", "Le montant maximum en USD est de 300")
                              return
                            }
                            // Removed minimum check during typing
                          } else if (currency === "FC") {
                            if (numericValue > 861000) {
                              Alert.alert("Erreur", "Le montant maximum en FC est de 861,000")
                              return
                            }
                            // Removed minimum check during typing
                          }

                          // Prevent multiple decimal points
                          if (num === "." && currentValue.includes(".")) {
                            return
                          }

                          // Update the value
                          onChange(newValue)
                        }}
                        disabled={isSubmitting}
                        style={$numberPadStyle}
                      />
                    </View>
                  )}
                />
              </View>
            </ScrollView>
            <View style={$footer}>
              {receiverDetails && (
                <Button
                  text={isSubmittingPayment ? "Traitement..." : "Payer"}
                  preset="reversed"
                  style={[$button, $payButton, isSubmittingPayment && { opacity: 0.5 }]}
                  onPress={handleSubmit(handlePay)}
                  disabled={isSubmittingPayment}
                />
              )}
            </View>
          </KeyboardAvoidingView>
        )}
      </Screen>
      {!showCurrencyModal && !showInvoiceModal && !showConfirmModal && !receiverDetails && (
        <View style={$scanFooter}>
          <TouchableOpacity
            style={$scanContainer}
            onPress={() => {
              // Navigate to the ScannerScreen instead of showing the Scanner component
              navigation.navigate("Scanner", {
                hideTabBar: true,
                onScanComplete: handleScanSuccess,
                returnScreen: "LipanaFedha",
                user_type: "standard", // or "business" depending on the user type
              })
            }}
          >
            <View style={$scanButtonInner}>
              <Icon icon="qrcodewinc" size={24} color={colors.palette.neutral100} />
              <Text style={$scanButtonText}>Scanner un code QR</Text>
            </View>
          </TouchableOpacity>
        </View>
      )}

      <ChangeCurency isVisible={showCurrencyModal} onClose={() => setShowCurrencyModal(false)} />

      {/* Invoice Payment Modal */}
      {invoiceData && (
        <PayInvoice
          visible={showInvoiceModal}
          onClose={() => {
            setShowInvoiceModal(false)
            setInvoiceData(null)
          }}
          invoiceData={invoiceData}
        />
      )}

      <Modal
        visible={showConfirmModal}
        transparent
        animationType="slide"
        onRequestClose={() => setShowConfirmModal(false)}
      >
        <View style={$confirmModalOverlay}>
          <View style={$confirmModalContent}>
            <View style={{ justifyContent: "center", alignItems: "center" }}>
              <Text style={$confirmDetails}>Vous êtes sur le point d&apos;envoyer{"\n"}</Text>
              <Text style={$amountText}>
                {watch("amount")} {currency}
              </Text>

              <Text style={$receiverText}>
                à {receiverDetails?.firstName} {receiverDetails?.lastName}
              </Text>
            </View>

            <TextField
              value={transferMessage}
              onChangeText={setTransferMessage}
              placeholder="Message pour le destinataire (optionnel)"
              multiline
              style={$messageInput}
            />

            <View style={$modalButtons}>
              <Button
                text="Annuler"
                preset="default"
                style={[$modalButton, $cancelButton]}
                onPress={() => setShowConfirmModal(false)}
              />
              <Button
                text={isSubmittingPayment ? "Traitement..." : "Confirmer"}
                preset="reversed"
                style={[$modalButton, $confirmButton]}
                onPress={handleConfirmTransfer}
                disabled={isSubmittingPayment}
              />
            </View>
          </View>
        </View>
      </Modal>
    </>
  )
}

// Footer container that stays at the bottom
const $scanFooter: ViewStyle = {
  position: "absolute",
  bottom: 0,
  left: 0,
  right: 0,
  paddingHorizontal: spacing.md,
  paddingBottom: spacing.xl,
  paddingTop: spacing.sm,
  // backgroundColor: colors.palette.neutral100,
  // borderTopWidth: 1,
  // borderTopColor: colors.palette.neutral200,
  // elevation: 5,
  // shadowColor: colors.palette.neutral900,
  // shadowOffset: { width: 0, height: -2 },
  // shadowOpacity: 0.1,
  // shadowRadius: 4,
}

// The scan button itself
const $scanContainer: ViewStyle = {
  borderRadius: 16,
  backgroundColor: colors.palette.neutral900,
  paddingVertical: spacing.md,
  paddingHorizontal: spacing.lg,
  elevation: 3,
  shadowColor: colors.palette.neutral900,
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.2,
  shadowRadius: 4,
}

// Inner content of the scan button
const $scanButtonInner: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "center",
  gap: spacing.sm,
}

// Text style for the scan button
const $scanButtonText: TextStyle = {
  color: colors.palette.neutral100,
  fontSize: 16,
  fontWeight: "600",
}

const $root: ViewStyle = {
  flex: 1,
}
const $keyboardAvoidingView: ViewStyle = {
  // flex: 1,
}
const $formContainer: ViewStyle = {
  paddingHorizontal: spacing.md,
  // gap: spacing.sm,
}

const $scrollContent: ViewStyle = {
  flexGrow: 1,
}
// const $scanButton: ViewStyle = {
//   marginVertical: spacing.md,
//   marginHorizontal: spacing.lg,
// }

const $noteInput: ViewStyle = {
  marginTop: spacing.xs,
}

const $footer: ViewStyle = {
  // position: "absolute",
  bottom: 0,
  left: 0,
  right: 0,
  flexDirection: "row",
  paddingHorizontal: spacing.lg,
  // paddingVertical: spacing.md,
  backgroundColor: colors.palette.neutral100,
  borderTopWidth: 1,
  borderTopColor: colors.palette.neutral200,
  gap: spacing.sm,
}

const $button: ViewStyle = {
  flex: 1,
  height: 40,
  borderRadius: 24,
}

const $payButton: ViewStyle = {
  backgroundColor: colors.palette.neutral900,
}

const $searchContainer: ViewStyle = {
  flexDirection: "row",
  padding: spacing.md,
  gap: spacing.md,
  // flex: 1,
  // borderBottomWidth: 1,
  // borderBottomColor: colors.palette.neutral300,
}

const $searchInput: ViewStyle = {
  flex: 1,
  borderBottomColor: colors.palette.terciary100,
}

const $scannerButton: ViewStyle = {
  alignItems: "center",
  justifyContent: "center",
  padding: spacing.xs,
  borderRadius: 8,
  backgroundColor: colors.palette.neutral200,
  width: 80,
}

const $scannerText: TextStyle = {
  fontSize: 12,
  marginTop: spacing.xs,
  color: colors.text,
}
const $avatarLabel: TextStyle = {
  fontSize: 16,
  fontWeight: "600",
  color: colors.palette.primary100,
}

const $sectionTitle: TextStyle = {
  fontSize: 16,
  fontWeight: "bold",
  marginHorizontal: spacing.md,
  // marginTop: spacing.md,
}

const $recipientList: ViewStyle = {
  marginTop: spacing.xs,
}

const $listContent: ViewStyle = {
  padding: spacing.sm,
}

const $recipientItem: ViewStyle = {
  padding: spacing.sm,
  backgroundColor: colors.palette.neutral100,
  borderRadius: 8,
  marginBottom: spacing.xs,
}

const $recipientInfo: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  gap: spacing.sm,
}

const $avatar: ImageStyle = {
  width: 50,
  height: 50,
  borderRadius: 25,
}

const $avatarPlaceholder: ViewStyle = {
  width: 50,
  height: 50,
  borderRadius: 25,
  backgroundColor: colors.palette.neutral300,
  alignItems: "center",
  justifyContent: "center",
}

const $avatarText: TextStyle = {
  color: colors.palette.neutral800,
  fontSize: 18,
  fontWeight: "bold",
}

const $recipientName: TextStyle = {
  fontSize: 16,
  fontWeight: "500",
}

const $lastTransaction: TextStyle = {
  fontSize: 12,
  color: colors.palette.neutral600,
  marginTop: 2,
}

const $noDataText: TextStyle = {
  textAlign: "center",
  marginTop: spacing.md,
  color: colors.textDim,
}

const $errorText: TextStyle = {
  color: "red",
  fontSize: 12,
  marginBottom: 5,
}

const $numberPadStyle: ViewStyle = {
  // marginTop: spacing.md,
}

const $amountInputContainer: ViewStyle = {
  flexDirection: "row",
  // alignItems: "center",
  // marginBottom: spacing.sm,
  // backgroundColor: colors.palette.neutral900,
  flex: 1,
}

const $amountInput: ViewStyle = {
  flex: 1,

  // fontSize: 24,
  // textAlign: "right",
}

const $currencyButton: ViewStyle = {
  backgroundColor: colors.palette.primary100,
  paddingHorizontal: spacing.xs,
  top: spacing.sm,
  paddingVertical: spacing.xs,
  borderRadius: 20,
  marginRight: spacing.xs,
}

const $currencyText: TextStyle = {
  color: colors.palette.primary500,
  fontWeight: "bold",
}

const $confirmModalOverlay: ViewStyle = {
  flex: 1,
  backgroundColor: "rgba(0, 0, 0, 0.5)",
  justifyContent: "center",
  alignItems: "center",
}

const $confirmModalContent: ViewStyle = {
  backgroundColor: colors.palette.neutral100,
  borderRadius: 20,
  padding: spacing.lg,
  paddingVertical: spacing.xl,
  width: "85%",
  maxWidth: 400,
  elevation: 5,
  shadowColor: "#000",
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.25,
  shadowRadius: 3.84,
}

const $confirmDetails: TextStyle = {
  fontSize: 18,
  textAlign: "center",
  lineHeight: 24,
  color: colors.textDim,
}

const $amountText: TextStyle = {
  fontSize: 24,
  fontWeight: "bold",
}

const $receiverText: TextStyle = {
  fontSize: 18,
  fontWeight: "bold",
  color: colors.text,
  marginBottom: spacing.md,
  textAlign: "center",
}

const $messageInput: ViewStyle = {
  marginBottom: spacing.lg,
  // borderWidth: 1,
  // borderColor: colors.border,
  borderRadius: 10,
  padding: spacing.md,
}

const $modalButtons: ViewStyle = {
  flexDirection: "row",
  top: spacing.lg,
  justifyContent: "space-between",
  gap: spacing.md,
}

const $modalButton: ViewStyle = {
  flex: 1,
  borderRadius: 10,
}

const $cancelButton: ViewStyle = {
  backgroundColor: colors.palette.neutral200,
}

const $confirmButton: ViewStyle = {
  backgroundColor: colors.palette.neutral900,
}

const $emptyStateContainer: ViewStyle = {
  alignItems: "center",
  justifyContent: "center",
  padding: spacing.lg,
  // backgroundColor: colors.palette.neutral200,
  borderRadius: 8,
  marginHorizontal: spacing.sm,
  marginVertical: spacing.xs,
}

const $emptyStateText: TextStyle = {
  fontSize: 14,
  color: colors.palette.neutral600,
  textAlign: "center",
  marginTop: spacing.sm,
}

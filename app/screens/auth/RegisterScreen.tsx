/* eslint-disable react-native/no-unused-styles */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable react-native/no-color-literals */
/* eslint-disable react-native/no-inline-styles */
import { FC, useState } from "react"
import {
  ViewStyle,
  View,
  StyleSheet,
  ImageBackground,
  Image,
  TextStyle,
  ActivityIndicator,
  TouchableOpacity,
  Alert,
} from "react-native"
import { AppStackScreenProps } from "@/navigators"
import { Button, Icon, Screen, Text, TextField, WatcherAction } from "@/components"
import { Controller, useForm } from "react-hook-form"
import { loginSchema } from "@/services/api"
import { zodResolver } from "@hookform/resolvers/zod"
import { colors, spacing } from "@/theme"
import { useSafeAreaInsetsStyle } from "@/utils/useSafeAreaInsetsStyle"
import { useStores } from "@/store/rootStore"

interface RegisterScreenProps extends AppStackScreenProps<"Register"> {}

export const RegisterScreen: FC<RegisterScreenProps> = ({ navigation }) => {
  const [isPasswordHidden, setIsPasswordHidden] = useState(true)
  const {
    auth: { register },
  } = useStores()
  const [loading, setLoading] = useState(false)
  const [isTyping, setIsTyping] = useState(false)
  const [isPassTyping, setIsPassTyping] = useState(false)
  const $bottomContainerInsets = useSafeAreaInsetsStyle(["bottom"])

  const {
    control,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      phone_number: "",
      password: "",
    },
    mode: "onChange",
  })

  const phoneNumber = watch("phone_number")
  const password = watch("password")
  const isButtonDisabled = loading || !phoneNumber || !password

  async function onSubmit({ phone_number, password }: any) {
    try {
      setLoading(true)
      const response = await register(phone_number, password)
      if (response.success) {
        navigation.navigate("OtpVerfication")
      } else {
        Alert.alert("Erreur", response.data.detail)
      }
    } catch (error) {
      Alert.alert("Erreur", "Échec de l'inscription. Vérifiez vos informations.")
    } finally {
      setLoading(false)
    }
  }

  return (
    <ImageBackground
      source={require("../../../assets/images/bgdeem.png")}
      style={styles.background}
      resizeMode="cover"
    >
      <Screen
        backgroundColor="transparent"
        style={styles.root}
        preset="scroll"
        safeAreaEdges={["top"]}
        StatusBarProps={{ backgroundColor: colors.palette.neutral900 }}
      >
        {/* Header Section */}
        <View style={styles.header}>
          <Image
            style={styles.headerLogo}
            source={require("../../../assets/images/logo.png")}
            resizeMode="contain"
          />
          <TouchableOpacity
            onPress={() => navigation.navigate("Helpcenter")}
            style={styles.helpButton}
          >
            <Icon icon="helpcenter" size={34} color={colors.palette.neutral900} />
          </TouchableOpacity>
        </View>

        <View style={styles.titleContainer}>
          <Text text="Créer un nouveau compte" preset="heading" style={styles.title} />
        </View>

        {/* Main Content */}
        <View style={styles.mainContent}>
          <View style={styles.formContainer}>
            <View style={styles.inputGroup}>
              <Text text="Numéro de téléphone" preset="formLabel" style={styles.inputLabel} />
              <Controller
                control={control}
                name="phone_number"
                render={({ field: { value, onChange }, fieldState }) => (
                  <TextField
                    value={value}
                    onChangeText={(text) => {
                      setIsTyping(true)
                      setTimeout(() => setIsTyping(false), 1000)
                      const formatted = text.replace(/\D/g, "").replace(/^0+/, "")
                      if (formatted.length <= 9) {
                        onChange(formatted)
                      }
                    }}
                    containerStyle={[styles.input, 
                      isTyping && { borderWidth:  1 }
                    ]}
                     onFocus={() => setIsTyping(true)}
                    onBlur={() => setIsTyping(false)}
                    autoCapitalize="none"
                    keyboardType="numeric"
                    placeholderTextColor={colors.palette.neutral400}
                    autoCorrect={false}
                    maxLength={9}
                    placeholder="00 000 0000"
                    LeftAccessory={() => (
                      <View style={styles.phonePrefix}>
                        <Image
                          source={require("../../../assets/images/flags/cd.png")}
                          style={styles.flagIcon}
                          resizeMode="cover"
                        />
                        <Text style={styles.countryCode}>+243</Text>
                      </View>
                    )}
                    status={
                      fieldState.error && fieldState.isDirty && !isTyping ? "error" : undefined
                    }
                    helper={
                      fieldState.error && fieldState.isDirty && !isTyping
                        ? fieldState.error.message
                        : undefined
                    }
                  />
                )}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text text="Mot de passe" preset="formLabel" style={styles.inputLabel} />
              <Controller
                control={control}
                name="password"
                render={({ field: { value, onChange } }) => (
                  <TextField
                    value={value}
                    onChangeText={onChange}
                    containerStyle={[styles.input, 
                      isPassTyping && { borderWidth:  1 }
                    ]}
                     onFocus={() => setIsPassTyping(true)}
                    onBlur={() => setIsPassTyping(false)}
                    autoCapitalize="none"
                    autoComplete="password"
                    placeholderTextColor={colors.palette.neutral400}
                    autoCorrect={false}
                    secureTextEntry={isPasswordHidden}
                    placeholder="Entrez votre mot de passe"
                    onSubmitEditing={handleSubmit(onSubmit)}
                    RightAccessory={() => (
                      <WatcherAction
                        style={styles.watcherIcon}
                        iconSize={24}
                        isHidden={isPasswordHidden}
                        onToggle={() => setIsPasswordHidden(!isPasswordHidden)}
                      />
                    )}
                    status={errors.password ? "error" : undefined}
                    helper={errors.password?.message}
                  />
                )}
              />
            </View>

            <View style={styles.buttonContainer}>
              <Button
                testID="register-button"
                preset="reversed"
                text={loading ? "Chargement..." : "Créer un compte"}
                style={[styles.registerButton, isButtonDisabled && styles.disabledButton]}
                disabled={isButtonDisabled}
                onPress={handleSubmit(onSubmit)}
              >
                {loading && <ActivityIndicator size="small" color="white" />}
              </Button>
            </View>
          </View>
        </View>

        {/* Bottom Section */}
        <View style={[styles.bottomContainer, $bottomContainerInsets]}>
          <View style={styles.loginContainer}>
            <Text style={styles.loginText}>Déjà un compte? </Text>
            <TouchableOpacity onPress={() => navigation.navigate("Login")}>
              <Text style={styles.loginLink}>Se connecter</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Screen>
    </ImageBackground>
  )
}

const styles = StyleSheet.create({
  background: {
    flex: 1,
    width: "100%",
  },
  bottomContainer: {
    paddingBottom: spacing.lg,
  },
  buttonContainer: {
    alignItems: "center",
    marginTop: spacing.lg,
  },
  countryCode: {
    color: colors.palette.neutral900,
    fontSize: 16,
    fontWeight: "600",
  },
  disabledButton: {
    opacity: 0.5,
  },
  flagIcon: {
    borderRadius: 12,
    height: 24,
    width: 24,
  },
  formContainer: {
    backgroundColor: colors.palette.neutral100,
    borderRadius: 24,
    padding: spacing.xs,
  },
  header: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "space-between",
  },
  headerLogo: {
    height: 100,
    width: 170,
  },
  helpButton: {
    borderRadius: 20,
    padding: spacing.xs,
  },
  input: {
    backgroundColor: colors.palette.neutral200,
    borderRadius: 14,
    borderWidth: 0,
  },
  inputGroup: {
    marginBottom: spacing.xl,
  },
  inputLabel: {
    color: colors.palette.neutral900,
    fontSize: 14,
    fontWeight: "600",
    marginBottom: spacing.xs,
  },
  loginContainer: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "center",
    paddingVertical: spacing.lg,
  },
  loginLink: {
    color: colors.palette.primary500,
    fontSize: 14,
    fontWeight: "600",
  },
  loginText: {
    color: colors.palette.neutral900,
    fontSize: 14,
  },
  mainContent: {
    flex: 1,
    paddingTop: spacing.xl * 1.5,
  },
  phonePrefix: {
    alignItems: "center",
    flexDirection: "row",
    gap: spacing.xs,
    paddingLeft: spacing.sm,
    top: spacing.md,
    borderRightColor: colors.palette.neutral300,
    borderRightWidth: 1,
    paddingHorizontal: 10,
  },
  registerButton: {
    backgroundColor: colors.palette.neutral900,
    borderRadius: 16,
    elevation: 3,
    paddingVertical: spacing.md,
    shadowColor: colors.palette.neutral900,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    width: "100%",
  },
  root: {
    flex: 1,
    paddingHorizontal: spacing.lg,
  },
  title: {
    color: colors.palette.neutral900,
    fontSize: 18,
    fontWeight: "bold",
  },
  titleContainer: {
    alignItems: "center",
    marginTop: spacing.xl,
  },
  watcherIcon: {
    right: spacing.sm,
    top: 20,
  },
})

//     <TextField
//       ref={PasswordInput}
//       value={field.value}
//       onChangeText={field.onChange}
//       containerStyle={styles.input}
//       autoCapitalize="none"
//       autoComplete="password"
//       placeholderTextColor={colors.palette.accent400}
//       autoCorrect={false}
//       secureTextEntry={isPasswordHidden}
//       placeholder="Enter your password"
//       onSubmitEditing={handleSubmit(onSubmit)}
//       RightAccessory={(props) => (
//         <WatcherAction
//           style={[props.style, { marginRight: 10 }]} // Merge styles
//           iconSize={24}
//           isHidden={isPasswordHidden}
//           onToggle={() => setIsPasswordHidden(!isPasswordHidden)}
//         />
//       )}
//     />
//   )}
// />

/* eslint-disable react-native/no-unused-styles */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable react-native/no-color-literals */
/* eslint-disable react-native/no-inline-styles */
import { FC, useState } from "react"
import {
  ViewStyle,
  View,
  StyleSheet,
  ImageBackground,
  ImageStyle,
  Image,
  TextStyle,
  ActivityIndicator,
  Alert,
  TouchableOpacity,
} from "react-native"
import { AppStackScreenProps } from "@/navigators"
import {
  Button,
  Icon,
  PhoneNumberInput,
  Screen,
  Text,
  TextField,
  WatcherAction,
} from "@/components"
import { useAuthStore } from "@/store/AuthenticationStore"
import { Controller, useForm } from "react-hook-form"
import { checkProvider, loginSchema } from "@/services/api"
import { zodResolver } from "@hookform/resolvers/zod"
import { useAppTheme } from "@/utils/useAppTheme"
import { colors, spacing, ThemedStyle } from "@/theme"
import { useSafeAreaInsetsStyle } from "@/utils/useSafeAreaInsetsStyle"

interface LoginScreenProps extends AppStackScreenProps<"Login"> {}

interface LoginFormData {
  phone_number: string
  password: string
}

export const LoginScreen: FC<LoginScreenProps> = ({ navigation }) => {
  const { themed } = useAppTheme()
  const [isPasswordHidden, setIsPasswordHidden] = useState(true)
  const { login } = useAuthStore()
  const [loading, setLoading] = useState(false)
  const $bottomContainerInsets = useSafeAreaInsetsStyle(["bottom"])
  const [isTyping, setIsTyping] = useState(false)
  const [isPassTyping, setIsPassTyping] = useState(false)

  const {
    control,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      phone_number: "",
      password: "",
    },
    mode: "onChange", // This will enable real-time validation
  })

  const phoneNumber = watch("phone_number")
  const password = watch("password")
  const isButtonDisabled = loading || !phoneNumber || !password

  const handleLogin = async (data: LoginFormData) => {
    try {
      const formattedPhone = data.phone_number.replace(/^0+/, "") // Remove leading zeros

      if (!isValidPhoneNumber(formattedPhone)) {
        Alert.alert(
          "Numéro invalide",
          "Le numéro doit commencer par 4, 5, 7, 8 ou 9 et contenir 9 chiffres",
        )
        return
      }

      setLoading(true)
      await login(formattedPhone, data.password)
      navigation.navigate("FedhaLoader", { nextScreen: undefined })
    } catch (error) {
      Alert.alert(
        "Échec de la connexion",
        "Numéro de téléphone ou mot de passe incorrect. Veuillez vérifier vos informations et réessayer.",
      )
    } finally {
      setLoading(false)
    }
  }

  const isValidPhoneNumber = (phone: string): boolean => {
    return phone.length === 9 && !phone.startsWith("0") && /^\d{9}$/.test(phone)
  }

  const validatePhoneNumber = (phone: string) => {
    if (!phone) return true

    if (phone.startsWith("0")) {
      return "Ne pas inclure le 0 au début"
    }

    // Only show error for complete numbers
    if (phone.length === 9) {
      if (!/^\d{9}$/.test(phone)) {
        return "Le numéro doit contenir 9 chiffres"
      }
      return true // Explicitly return true for valid 9-digit numbers
    }

    // Don't show errors while typing incomplete numbers
    return true
  }

  return (
    <ImageBackground
      source={require("../../../assets/images/bgdeem.png")}
      style={styles.background}
      resizeMode="cover"
    >
      <Screen
        backgroundColor="transparent"
        style={styles.root}
        preset="scroll"
        safeAreaEdges={["top"]}
        StatusBarProps={{ backgroundColor: colors.palette.neutral900 }}
      >
        {/* Header Section */}
        <View style={styles.header}>
          <Image
            style={styles.headerLogo}
            source={require("../../../assets/images/logo.png")}
            resizeMode="contain"
          />
          <TouchableOpacity
            onPress={() => navigation.navigate("Helpcenter")}
            style={styles.helpButton}
          >
            <Icon icon="helpcenter" size={34} color={colors.palette.neutral900} />
          </TouchableOpacity>
        </View>

        <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
          <Text text="Connectez-vous a votre compte" preset="heading" style={styles.title} />
        </View>
        {/* Main Content */}
        <View style={styles.mainContent}>
          {/* Form Container */}
          <View style={styles.formContainer}>
            <View style={styles.inputGroup}>
              <Text text="Numéro de téléphone" preset="formLabel" style={styles.inputLabel} />
              <Controller
                control={control}
                name="phone_number"
                rules={{
                  validate: validatePhoneNumber,
                }}
                render={({ field: { value, onChange }, fieldState }) => (
                  <TextField
                    value={value}
                    onChangeText={(text) => {
                      setIsTyping(true)
                      setTimeout(() => setIsTyping(false), 1000)

                      // Remove non-digits and leading zeros
                      const formatted = text.replace(/\D/g, "").replace(/^0+/, "")
                      if (formatted.length <= 9) {
                        onChange(formatted)
                      }
                    }}
                    containerStyle={[styles.input, isTyping && { borderWidth: 1 }]}
                    autoCapitalize="none"
                    onFocus={() => setIsTyping(true)}
                    onBlur={async () => {
                      setIsTyping(false)

                      // if (value.length === 9) {
                      //   const carrier = await checkProvider(value)
                      //   console.log("Carrier:", carrier)
                      //   // Optionally show the carrier name somewhere in UI
                      // }
                    }}
                    keyboardType="numeric"
                    placeholderTextColor={colors.palette.neutral400}
                    autoCorrect={false}
                    maxLength={9}
                    placeholder="00 000 0000"
                    LeftAccessory={() => (
                      <View style={styles.phonePrefix}>
                        <Image
                          source={require("../../../assets/images/flags/cd.png")}
                          style={styles.flagIcon}
                          resizeMode="cover"
                        />
                        <Text style={styles.countryCode}>+243</Text>
                      </View>
                    )}
                    status={
                      fieldState.error && fieldState.isDirty && !isTyping ? "error" : undefined
                    }
                    helper={
                      fieldState.error && fieldState.isDirty && !isTyping
                        ? fieldState.error.message
                        : undefined
                    }
                  />
                )}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text text="Mot de passe" preset="formLabel" style={styles.inputLabel} />
              <Controller
                control={control}
                name="password"
                render={({ field: { value, onChange } }) => (
                  <TextField
                    value={value}
                    onChangeText={onChange}
                    containerStyle={[styles.input, isPassTyping && { borderWidth: 1 }]}
                    onFocus={() => setIsPassTyping(true)}
                    onBlur={() => setIsPassTyping(false)}
                    autoCapitalize="none"
                    autoComplete="password"
                    placeholderTextColor={colors.palette.neutral400}
                    autoCorrect={false}
                    secureTextEntry={isPasswordHidden}
                    placeholder="Entrez votre mot de passe"
                    onSubmitEditing={handleSubmit(handleLogin)}
                    RightAccessory={() => (
                      <WatcherAction
                        style={styles.watcherIcon}
                        iconSize={24}
                        isHidden={isPasswordHidden}
                        onToggle={() => setIsPasswordHidden(!isPasswordHidden)}
                      />
                    )}
                    status={errors.password ? "error" : undefined}
                    helper={errors.password?.message}
                  />
                )}
              />
            </View>

            <Text
              text="Mot de passe oublié?"
              size="sm"
              style={styles.forgotPassword}
              onPress={() => navigation.navigate("ForgotPassCode")}
            />

            <View style={styles.buttonContainer}>
              <Button
                testID="login-button"
                preset="reversed"
                text={loading ? "Chargement..." : "Se connecter"}
                style={[styles.loginButton, isButtonDisabled && styles.disabledButton]}
                disabled={isButtonDisabled}
                onPress={handleSubmit(handleLogin)}
              >
                {loading && <ActivityIndicator size="small" color="white" />}
              </Button>
            </View>
          </View>
        </View>

        {/* Bottom Section */}
        <View style={[styles.bottomContainer, $bottomContainerInsets]}>
          <View style={styles.registerContainer}>
            <Text style={styles.registerText}>Nouveau sur Fedha? </Text>
            <TouchableOpacity onPress={() => navigation.navigate("AccountType")}>
              <Text style={styles.registerLink}>Créer un compte</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Screen>
    </ImageBackground>
  )
}

const styles = StyleSheet.create({
  background: {
    flex: 1,
    width: "100%",
  },
  bottomContainer: {
    paddingBottom: spacing.lg,
  },
  buttonContainer: {
    alignItems: "center",
  },
  countryCode: {
    color: colors.palette.neutral900,
    fontSize: 16,
    fontWeight: "600",
  },
  disabledButton: {
    opacity: 0.5,
  },
  flagIcon: {
    borderRadius: 12,
    height: 24,
    width: 24,
  },
  forgotPassword: {
    color: colors.palette.primary600,
    fontSize: 14,
    marginBottom: spacing.xl,
    textAlign: "right",
  },
  formContainer: {
    backgroundColor: colors.palette.neutral100,
    borderRadius: 24,
    // elevation: 5,
    padding: spacing.xs,
    // shadowColor: colors.palette.neutral900,
    // shadowOffset: { width: 0, height: 4 },
    // shadowOpacity: 0.1,
    // shadowRadius: 12,
  },
  header: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "space-between",
    // paddingBottom: spacing.md,
    // paddingTop: spacing.xl,
  },
  headerLogo: {
    height: 100,
    width: 170,
  },
  helpButton: {
    // backgroundColor: colors.palette.neutral500,
    borderRadius: 20,
    padding: spacing.xs,
  },
  input: {
    backgroundColor: colors.palette.neutral200,
    borderRadius: 14,
    borderWidth: 0,
  },
  inputGroup: {
    marginBottom: spacing.xl,
  },
  inputLabel: {
    color: colors.palette.neutral900,
    fontSize: 14,
    fontWeight: "600",
    marginBottom: spacing.xs,
  },
  loginButton: {
    backgroundColor: colors.palette.neutral900,
    borderRadius: 16,
    elevation: 3,
    paddingVertical: spacing.md,
    shadowColor: colors.palette.neutral900,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    width: "100%",
  },
  mainContent: {
    flex: 1,
    paddingTop: spacing.xl * 1.5,
  },
  phonePrefix: {
    alignItems: "center",
    borderRightColor: colors.palette.neutral300,
    borderRightWidth: 1,
    flexDirection: "row",
    gap: spacing.xs,
    paddingHorizontal: 10,
    paddingLeft: spacing.sm,
    top: spacing.md,
  },
  registerContainer: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "center",
    paddingVertical: spacing.lg,
  },
  registerLink: {
    color: colors.palette.primary500,
    fontSize: 14,
    fontWeight: "600",
  },
  registerText: {
    color: colors.palette.neutral900,
    fontSize: 14,
  },
  root: {
    flex: 1,
    paddingHorizontal: spacing.lg,
  },
  subtitle: {
    color: colors.palette.neutral600,
    fontSize: 16,
    lineHeight: 22,
  },
  title: {
    color: colors.palette.neutral900,
    fontSize: 18,
    fontWeight: "bold",
    marginTop: spacing.xl,
  },
  watcherIcon: {
    right: spacing.sm,
    top: 20,
  },
})

/* eslint-disable no-restricted-imports */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable react-native/no-color-literals */
/* eslint-disable react-native/sort-styles */
import React, { FC, useState, useEffect, useRef } from "react" // Import useEffect, useRef
import {
  View,
  ViewStyle,
  StyleSheet,
  TouchableOpacity,
  Image,
  ImageStyle,
  LayoutChangeEvent,
  Animated, // Import Animated
  Easing,
  TextStyle, // Import Easing
  // Dimensions, // Currently unused
} from "react-native"
import { AppStackScreenProps } from "@/navigators" // Assuming this path is correct
import { Button, Header, Icon, Screen, Text } from "@/components" // Assuming this path is correct
import { colors, spacing } from "@/theme" // Use theme colors & spacing

interface AccountTypeScreenProps extends AppStackScreenProps<"AccountType"> {}

// --- Define Account Types ---
type AccountTypeValue = "personal" | "business" | "agent" | null

// --- Load Local Images ---
const isPersonal = require("../../../assets/images/people/happychics.jpg")
const isBusiness = require("../../../assets/images/people/womenEn.jpg")
const isAgent = require("../../../assets/images/people/fedhapayTill.png")

export const AccountTypeScreen: FC<AccountTypeScreenProps> = ({ navigation }) => {
  const [selectedAccount, setSelectedAccount] = useState<AccountTypeValue>(null)
  const [footerHeight, setFooterHeight] = useState(0) // State to store measured footer height

  // --- Animated Setup (Built-in Animated API) ---
  // Use useRef to hold the animated value
  const footerProgress = useRef(new Animated.Value(0)).current // 0 = hidden, 1 = shown

  // --- Animate footer based on selection ---
  useEffect(() => {
    Animated.timing(footerProgress, {
      toValue: selectedAccount !== null ? 1 : 0, // Target value based on selection
      duration: 400, // Animation duration
      easing: Easing.bezier(0.25, 0.1, 0.25, 1), // Smooth easing
      useNativeDriver: true, // Use native driver for opacity and transform
    }).start() // Start the animation
  }, [selectedAccount, footerProgress])

  // --- Interpolate Styles ---
  // Define these before the return statement
  const footerTranslateY = footerProgress.interpolate({
    inputRange: [0, 1],
    // Use measured height if available, otherwise estimate
    outputRange: [footerHeight > 0 ? footerHeight + 20 : 150, 0], // Add buffer if needed
  })

  const footerOpacity = footerProgress.interpolate({
    inputRange: [0, 0.5, 1], // Fade in during the second half of the slide
    outputRange: [0, 0, 1],
  })

  // Combine interpolated styles into an object
  const animatedFooterStyle = {
    opacity: footerOpacity,
    transform: [{ translateY: footerTranslateY }],
  }

  // --- State and Handlers ---
  const handleSelect = (type: AccountTypeValue) => {
    setSelectedAccount(type)
  }

  const handleProceed = () => {
    if (selectedAccount) {
      console.log("Proceed Pressed with:", selectedAccount)
      if (selectedAccount === "business") {
        navigation.navigate("CreateUserBusiness")
      } else {
        navigation.navigate("Register")
      }
      // TODO: Navigate to the next screen based on selectedAccount
      // navigation.navigate('NextScreen', { accountType: selectedAccount });
    }
  }

  // --- Measure Footer Height ---
  const onFooterLayout = (event: LayoutChangeEvent) => {
    const { height } = event.nativeEvent.layout
    // Set height only if it's different and positive
    if (height > 0 && height !== footerHeight) {
      setFooterHeight(height)
    }
  }

  return (
    <>
      <Header
        title="Fedha"
        leftIcon="backicon"
        onLeftPress={navigation.goBack}
        // rightIcon="question"
        titleStyle={$headerTitle}
      />
      {/* Scrollable content area */}
      <Screen style={$root} preset="scroll" safeAreaEdges={["bottom"]} statusBarStyle="dark">
        <View style={$contentContainer}>
          <Text style={$title} text="Quel compte vous convient le mieux ?" />

          {/* --- Account Type Options --- */}
          <View style={$optionsContainer}>
            {/* Personal Account Option */}
            <TouchableOpacity
              style={[$cardContainer, selectedAccount === "personal" && $cardSelected]}
              onPress={() => handleSelect("personal")}
              activeOpacity={0.8}
            >
              <View style={$cardContentVertical}>
                {/* ... icon, text ... */}
                <View style={$iconContainer}>
                  <Icon icon="user" size={32} color={colors.palette.neutral700} />
                </View>
                <View style={$textContainer}>
                  <Text style={$cardTitle} text="Personnel" />
                  <Text
                    preset="subheading"
                    style={$cardDescriptionAccent}
                    text="Envoyez. Recevez. Payez."
                  />
                  <Text
                    style={$cardDescription}
                    text="Gérez votre argent au quotidien en toute simplicité."
                  />
                </View>
              </View>
              <Image source={isPersonal} style={$cardImage} resizeMode="cover" />
            </TouchableOpacity>

            {/* Business Account Option */}
            <TouchableOpacity
              style={[$cardContainer, selectedAccount === "business" && $cardSelected]}
              onPress={() => handleSelect("business")}
              activeOpacity={0.8}
            >
              <View style={$cardContentVertical}>
                {/* ... icon, text ... */}
                <View style={$iconContainer}>
                  <Icon icon="briefcase" size={32} color={colors.palette.neutral700} />
                </View>
                <View style={$textContainer}>
                  <Text style={$cardTitle} text="Business" />
                  <Text
                    preset="subheading"
                    style={$cardDescriptionAccent}
                    text="Boostez votre activité."
                  />
                  <Text
                    style={$cardDescription}
                    text="Acceptez des paiements et gérez vos finances comme un pro."
                  />
                </View>
              </View>
              <Image source={isBusiness} style={$cardImage} resizeMode="cover" />
            </TouchableOpacity>

            {/* Agent Account Option */}
            {/* <TouchableOpacity
              style={[$cardContainer, selectedAccount === "agent" && $cardSelected]}
              onPress={() => handleSelect("agent")}
              activeOpacity={0.8}
            >
              <View style={$cardContentVertical}>
                <View style={$iconContainer}>
                  <Icon icon="realtor" size={32} color={colors.palette.neutral700} />
                </View>
                <View style={$textContainer}>
                  <Text style={$cardTitle} text="Agent" />
                  <Text
                    preset="subheading"
                    style={$cardDescriptionAccent}
                    text="Encaissez. Aidez. Gagnez."
                  />
                  <Text
                    style={$cardDescription}
                    text="Proposez les services Fedha à votre communauté et touchez des commissions."
                  />
                </View>
              </View>
              <Image source={isAgent} style={$cardImage} resizeMode="cover" />
            </TouchableOpacity> */}
          </View>
        </View>
      </Screen>

      {/* --- Fixed Footer Area (Always Rendered, Animated) --- */}
      <Animated.View
        style={[$footerBase, animatedFooterStyle]} // Apply base and animated styles
        onLayout={onFooterLayout} // Measure height
      >
        <Button
          testID="next-screen-button"
          preset="reversed"
          text="Commencer"
          style={$buttonStyle}
          onPress={handleProceed}
          disabled={selectedAccount === null} // Disable button logically when hidden/animating out
        />
      </Animated.View>
    </>
  )
}

// --- Styles ---

// Specific style for the footer Button component
const $buttonStyle: ViewStyle = {
  backgroundColor: colors.palette.neutral900,
  borderRadius: 16,
  // paddingVertical: 16, // Button preset might handle padding
  width: "100%",
}

const styles = StyleSheet.create({
  root: {
    flex: 1,
  },
  contentContainer: {
    flexGrow: 1,
    justifyContent: "center",
    // alignItems: "center",
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.lg,
    paddingBottom: spacing.xl + 100, // Ensure scrollable content clears footer area
  },
  headerTitle: {
    // Styles for header title
  },
  title: {
    fontSize: 22,
    fontWeight: "bold",
    color: colors.text,
    textAlign: "center",
    marginBottom: spacing.lg + spacing.md,
    lineHeight: 28,
  },
  optionsContainer: {
    // Styles for the container holding the cards
  },
  cardContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "stretch",
    backgroundColor: colors.palette.neutral100,
    borderRadius: 16,
    paddingLeft: spacing.lg,
    paddingRight: 0,
    marginBottom: spacing.md,
    borderWidth: 1,
    borderColor: colors.border,
    overflow: "hidden",
  },
  cardSelected: {
    borderColor: colors.palette.neutral900,
    borderWidth: 2,
  },
  cardContentVertical: {
    flex: 1,
    flexDirection: "column",
    alignItems: "flex-start",
    paddingRight: spacing.sm,
    justifyContent: "center",
    paddingVertical: spacing.md,
  },
  iconContainer: {
    marginBottom: spacing.md,
  },
  textContainer: {
    // Takes available width
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: colors.text,
    marginBottom: spacing.xs,
  },
  cardDescriptionAccent: {
    fontSize: 13,
    fontWeight: "bold",
    color: colors.palette.neutral900,
    lineHeight: 18,
    marginBottom: spacing.xs,
  },
  cardDescription: {
    fontSize: 13,
    color: colors.palette.accent400,
    lineHeight: 18,
  },
  cardImage: {
    width: 165,
    height: "100%",
    borderLeftColor: colors.border,
    // marginLeft: spacing.sm,
  },
  // Base style for the footer
  footerBase: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    paddingVertical: spacing.md,
    paddingBottom: spacing.lg, // Adjust for safe area
    paddingHorizontal: spacing.lg,
    // backgroundColor: colors.background,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
})

// Export styles
const $root: ViewStyle = styles.root
const $contentContainer: ViewStyle = styles.contentContainer
const $headerTitle: ViewStyle = styles.headerTitle
const $title: ViewStyle = styles.title
const $optionsContainer: ViewStyle = styles.optionsContainer
const $cardContainer: ViewStyle = styles.cardContainer
const $cardSelected: ViewStyle = styles.cardSelected
const $cardContentVertical: ViewStyle = styles.cardContentVertical
const $iconContainer: ViewStyle = styles.iconContainer
const $textContainer: ViewStyle = styles.textContainer
const $cardTitle: ViewStyle = styles.cardTitle
const $cardDescriptionAccent: ViewStyle = styles.cardDescriptionAccent
const $cardDescription: TextStyle = styles.cardDescription
const $cardImage: ImageStyle = styles.cardImage
const $footerBase: ViewStyle = styles.footerBase
// $buttonStyle defined separately

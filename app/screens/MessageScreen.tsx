/* eslint-disable no-restricted-imports */
/* eslint-disable @typescript-eslint/no-unused-vars */
import { FC, useEffect, useState } from "react"
import { ActivityIndicator, StyleSheet, View, ViewStyle } from "react-native"
import { AppStackScreenProps } from "@/navigators"
import { Header, MessageItem, MessageView, Screen, Text, PortalView } from "@/components"
import { colors, spacing } from "@/theme"
import { useStores } from "@/store/rootStore"

import React from "react"

interface MessageScreenProps extends AppStackScreenProps<"Message"> {}

// Notification type from API
interface Notification {
  business: string | null
  created_at: string
  has_been_assisted: boolean
  id: number
  is_read: boolean
  message: string
  notification_type: string
  user: string
}

// Group of notifications by type
interface NotificationGroup {
  type: string
  notifications: Notification[]
  latestTimestamp: string
  unreadCount: number
}

export const MessageScreen: FC<MessageScreenProps> = ({ navigation }) => {
  const { notif } = useStores()
  const [loading, setLoading] = useState(true)
  const [notificationGroups, setNotificationGroups] = useState<NotificationGroup[]>([])
  const [selectedGroup, setSelectedGroup] = useState<NotificationGroup | null>(null)
  const [modalVisible, setModalVisible] = useState(false)

  // Fetch notifications when screen loads
  useEffect(() => {
    fetchNotifications()
  }, [])

  const fetchNotifications = async () => {
    setLoading(true)
    try {
      await notif.getNotifications()
      groupNotifications(notif.notificationsList)
    } catch (error) {
      console.error("Failed to fetch notifications:", error)
    } finally {
      setLoading(false)
    }
  }
  // console.log(notif)
  // Group notifications by type
  const groupNotifications = (notifications: Notification[]) => {
    if (!notifications || notifications.length === 0) {
      setNotificationGroups([])
      return
    }

    const groups: Record<string, Notification[]> = {}

    // Group by notification_type
    notifications.forEach((notification) => {
      const type = notification.notification_type
      if (!groups[type]) {
        groups[type] = []
      }
      groups[type].push(notification)
    })

    // Convert to array and sort by latest timestamp
    const groupsArray = Object.entries(groups).map(([type, notifications]) => {
      // Sort notifications by date (newest first)
      const sortedNotifications = [...notifications].sort(
        (a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime(),
      )

      const latestTimestamp = sortedNotifications[0].created_at
      const unreadCount = sortedNotifications.filter((n) => !n.is_read).length

      return {
        type,
        notifications: sortedNotifications,
        latestTimestamp,
        unreadCount,
      }
    })

    // Sort groups by latest message date
    groupsArray.sort(
      (a, b) => new Date(b.latestTimestamp).getTime() - new Date(a.latestTimestamp).getTime(),
    )

    setNotificationGroups(groupsArray)
  }

  // Get friendly name for notification type
  const getNotificationTypeName = (type: string): string => {
    const typeNames: Record<string, string> = {
      transaction_notif: "Transactions",
      service_request: "Services",
      system_notification: "Système",
      account_notification: "Compte",
      security_alert: "Sécurité",
      transaction_topup: "Rechargement",
      verification_result: "Compte",
    }
    return typeNames[type] || type
  }

  // Get icon for notification type
  const getNotificationTypeIcon = (type: string): string => {
    const typeIcons: Record<string, string> = {
      transaction_notif: "monytrans",
      service_request: "sel",
      system_notification: "info",
      account_notification: "user",
      security_alert: "lock",
      transaction_topup: "cashmoney",
      verification_result: "user",
    }
    return typeIcons[type] || "user"
  }

  // Format unread count for display
  const formatUnreadCount = (count: number): string => {
    if (count > 0) {
      return `${count} non lu${count > 1 ? "s" : ""}`
    }
    return "Tout lu"
  }

  // Handle message item press
  const handleMessagePress = (group: NotificationGroup) => {
    setSelectedGroup(group)
    setModalVisible(true)
  }

  // Close modal
  const handleCloseModal = () => {
    setModalVisible(false)
    setSelectedGroup(null)
  }

  return (
    <>
      <Header
        leftIcon="backicon"
        onLeftPress={navigation.goBack}
        title="Messages"
        backgroundColor={colors.palette.neutral100}
      />
      <Screen style={$root} preset="scroll" statusBarStyle="dark">
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.palette.primary500} />
            <Text style={styles.loadingText}>Chargement des messages...</Text>
          </View>
        ) : notificationGroups.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>Aucun message</Text>
          </View>
        ) : (
          notificationGroups.map((group, index) => (
            <MessageItem
              key={index}
              title={getNotificationTypeName(group.type)}
              subtext={
                group.notifications[0].message.substring(0, 60) +
                (group.notifications[0].message.length > 60 ? "..." : "")
              }
              icon={getNotificationTypeIcon(group.type)}
              timeStamp={formatUnreadCount(group.unreadCount)}
              unreadCount={group.unreadCount}
              onPress={() => handleMessagePress(group)}
            />
          ))
        )}
      </Screen>

      <PortalView
        visible={modalVisible}
        icon="backicon"
        title={selectedGroup ? getNotificationTypeName(selectedGroup.type) : "Messages"}
        onClose={handleCloseModal}
      >
        {selectedGroup && (
          <MessageView
            notifications={selectedGroup.notifications}
            notificationType={selectedGroup.type}
          />
        )}
      </PortalView>
    </>
  )
}

const $root: ViewStyle = {
  flex: 1,
}

const styles = StyleSheet.create({
  emptyContainer: {
    alignItems: "center",
    flex: 1,
    justifyContent: "center",
    padding: spacing.xl,
  },
  emptyText: {
    color: colors.palette.neutral600,
    textAlign: "center",
  },
  loadingContainer: {
    alignItems: "center",
    flex: 1,
    justifyContent: "center",
    padding: spacing.xl,
  },
  loadingText: {
    color: colors.palette.neutral600,
    marginTop: spacing.md,
    textAlign: "center",
  },
})

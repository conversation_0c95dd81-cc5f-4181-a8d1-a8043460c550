/* eslint-disable react-native/no-inline-styles */
/* eslint-disable react-native/no-color-literals */
/* eslint-disable import/no-unresolved */
import { FC, useEffect, useState } from "react"
import { View, StyleSheet, ScrollView, TouchableOpacity, Modal, Image } from "react-native"
import { AppStackScreenProps } from "@/navigators"
import { Header, Screen, Text, Icon } from "@/components"
import { colors, spacing } from "@/theme"
import { useStores } from "@/store/rootStore"
import { format } from "date-fns"
import { fr } from "date-fns/locale"
import { Avatar } from "react-native-paper"
import { getNameInitials } from "@/utils"
// import { getNameInitials } from "@/utils"

interface AccountInfoScreenProps extends AppStackScreenProps<"AccountInfo"> {}

export const AccountInfoScreen: FC<AccountInfoScreenProps> = ({ navigation }) => {
  const {
    auth: { user },
  } = useStores()
  const [documentModalVisible, setDocumentModalVisible] = useState(false)
  const [profileImage, setProfileImage] = useState<string | null>(null)

  const InfoSection = ({ title, children }: { title: string; children: React.ReactNode }) => (
    <View style={styles.section}>
      <Text preset="subheading" style={styles.sectionTitle}>
        {title}
      </Text>
      <View style={styles.sectionContent}>{children}</View>
    </View>
  )

  const InfoRow = ({ label, value }: { label: string; value: string | null | undefined }) => (
    <View style={styles.infoRow}>
      <Text style={styles.label}>{label}</Text>
      <Text style={styles.value}>{value || "Non renseigné"}</Text>
    </View>
  )

  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return "Non renseigné"
    return format(new Date(dateString), "dd MMMM yyyy", { locale: fr })
  }

  useEffect(() => {
    if (user?.profile_picture) {
      setProfileImage(user.profile_picture)
    }
  }, [user])

  return (
    <>
      <Header
        title="Informations personnelles"
        leftIcon="backicon"
        onLeftPress={navigation.goBack}
        backgroundColor={colors.palette.neutral100}
      />
      <Screen preset="scroll" safeAreaEdges={["bottom"]} statusBarStyle="dark">
        {/* Verification Status */}
        <InfoSection title="">
          <View style={styles.verificationStatus}>
            <View>
              {profileImage ? (
                <Avatar.Image
                  size={100}
                  source={{ uri: profileImage }}
                  style={{ backgroundColor: colors.palette.primary200 }}
                />
              ) : (
                <Avatar.Text
                  size={100}
                  label={getNameInitials(user?.first_name || "", user?.last_name || "")}
                  style={{ backgroundColor: colors.palette.primary200 }}
                />
              )}
            </View>
            <View style={{ flexDirection: "row" }}>
              <Icon
                icon={user?.is_documents_verified ? "check" : "x"}
                size={24}
                color={
                  user?.is_documents_verified ? colors.palette.neutral600 : colors.palette.angry500
                }
              />
              <Text
                style={[
                  styles.verificationText,
                  {
                    color: user?.is_documents_verified
                      ? colors.palette.neutral600
                      : colors.palette.neutral300,
                  },
                ]}
              >
                {user?.is_documents_verified ? "Compte vérifié" : "En attente de vérification"}
              </Text>
            </View>
          </View>
        </InfoSection>
        <ScrollView style={styles.container}>
          {/* Basic Information */}
          <InfoSection title="Informations de base">
            <InfoRow label="Prénom" value={user?.first_name} />
            <InfoRow label="Nom" value={user?.last_name} />
            <InfoRow label="Email" value={user?.email} />
            <InfoRow label="Téléphone" value={user?.phone_number} />
          </InfoSection>

          {/* Personal Information */}
          <InfoSection title="Informations personnelles">
            <InfoRow label="Nationalité" value={user?.PersonalInfo?.nationality} />
            <InfoRow
              label="Date de naissance"
              value={formatDate(user?.PersonalInfo?.date_de_naissance)}
            />
            <InfoRow label="État civil" value={user?.PersonalInfo?.etat_civil} />
            <InfoRow label="Profession" value={user?.PersonalInfo?.profession} />
          </InfoSection>

          {/* Address Information */}
          <InfoSection title="Adresse">
            <InfoRow label="Adresse" value={user?.PersonalInfo?.addresse} />
            <InfoRow label="Ville de résidence" value={user?.PersonalInfo?.ville_de_residence} />
            <InfoRow label="Pays de résidence" value={user?.PersonalInfo?.pays_des_residence} />
            <InfoRow label="Ville de naissance" value={user?.PersonalInfo?.ville_de_naissance} />
          </InfoSection>

          {/* Identity Document */}
          <InfoSection title="Document d'identité">
            <InfoRow label="Type de pièce" value={user?.IdentityDocument?.type_piece} />
            <InfoRow label="Numéro" value={user?.IdentityDocument?.numero_de_piece} />
            <InfoRow
              label="Lieu de délivrance"
              value={user?.IdentityDocument?.lieu_de_delivrance}
            />
            <InfoRow
              label="Date d'émission"
              value={formatDate(user?.IdentityDocument?.date_issue)}
            />
            <InfoRow
              label="Date d'expiration"
              value={formatDate(user?.IdentityDocument?.date_expiration)}
            />

            {user?.IdentityDocument?.piece_identite && (
              <TouchableOpacity
                style={styles.documentButton}
                onPress={() => setDocumentModalVisible(true)}
              >
                <Icon icon="idcardVerif" color={colors.palette.neutral100} size={24} />
                <Text style={styles.documentButtonText}>Voir le document</Text>
              </TouchableOpacity>
            )}
          </InfoSection>
        </ScrollView>

        {/* Document Modal */}
        <Modal
          visible={documentModalVisible}
          transparent={true}
          animationType="fade"
          onRequestClose={() => setDocumentModalVisible(false)}
        >
          <View style={styles.modalContainer}>
            <View style={styles.modalContent}>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setDocumentModalVisible(false)}
              >
                <Icon icon="x" size={24} />
              </TouchableOpacity>
              {user?.IdentityDocument?.piece_identite && (
                <Image
                  source={{ uri: user.IdentityDocument.piece_identite }}
                  style={styles.documentImage}
                  resizeMode="contain"
                />
              )}
            </View>
          </View>
        </Modal>
      </Screen>
    </>
  )
}

const styles = StyleSheet.create({
  closeButton: {
    backgroundColor: colors.palette.neutral200,
    borderRadius: 20,
    padding: spacing.xs,
    position: "absolute",
    right: spacing.sm,
    top: spacing.sm,
    zIndex: 1,
  },
  container: {
    flex: 1,
    padding: spacing.md,
  },
  documentButton: {
    alignItems: "center",
    backgroundColor: colors.palette.neutral900,
    borderRadius: 8,
    flexDirection: "row",
    gap: spacing.xs,
    justifyContent: "center",
    marginTop: spacing.sm,
    padding: spacing.sm,
  },
  documentButtonText: {
    color: colors.palette.neutral100,
    fontWeight: "600",
  },
  documentImage: {
    borderRadius: 8,
    height: "100%",
    width: "100%",
  },
  infoRow: {
    borderBottomColor: colors.palette.neutral200,
    borderBottomWidth: 1,
    flexDirection: "row",
    justifyContent: "space-between",
    paddingVertical: spacing.xs,
  },
  label: {
    color: colors.palette.accent400,
    flex: 1,
  },
  modalContainer: {
    alignItems: "center",
    backgroundColor: "rgba(0,0,0,0.9)",
    flex: 1,
    justifyContent: "center",
  },
  modalContent: {
    backgroundColor: colors.palette.neutral100,
    borderRadius: 12,
    height: "80%",
    padding: spacing.sm,
    position: "relative",
    width: "90%",
  },
  section: {
    backgroundColor: colors.palette.neutral100,
    borderRadius: 12,
    elevation: 3,
    marginBottom: spacing.lg,
    padding: spacing.md,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  sectionContent: {
    gap: spacing.xs,
  },
  sectionTitle: {
    color: colors.palette.neutral900,
    fontSize: 18,
    fontWeight: "600",
    marginBottom: spacing.sm,
  },
  value: {
    color: colors.palette.neutral900,
    flex: 2,
    fontWeight: "500",
    textAlign: "right",
  },
  verificationStatus: {
    alignItems: "center",
    // flexDirection: "row",
    // gap: spacing.xs,
    justifyContent: "center",
    // paddingVertical: spacing.sm,
  },
  verificationText: {
    fontWeight: "600",
  },
})

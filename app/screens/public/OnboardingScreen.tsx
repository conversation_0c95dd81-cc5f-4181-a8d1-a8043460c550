import { FC, useEffect, useRef, useState, useCallback } from "react"
import {
  ViewStyle,
  View,
  Dimensions,
  // TouchableOpacity,
  TouchableWithoutFeedback,
  Animated,
  StatusBar,
  Image,
  ImageStyle,
} from "react-native"
import { AppStackScreenProps } from "@/navigators"
import { colors, spacing } from "@/theme"
import { useSafeAreaInsets } from "react-native-safe-area-context"
import { markOnboardingCompleted } from "@/utils/onboardingUtils"

const { width: screenWidth, height: screenHeight } = Dimensions.get("screen")

interface OnboardingSlide {
  id: string
  content: any // Image source
  duration: number
}

const SLIDES: OnboardingSlide[] = [
  {
    id: "1",
    content: require("../../../assets/images/slides/SlideOne.png"),
    duration: 4000,
  },
  {
    id: "2",
    content: require("../../../assets/images/slides/SlideTow.png"),
    duration: 3500,
  },
  {
    id: "3",
    content: require("../../../assets/images/slides/Slide3.png"),
    duration: 3500,
  },
  {
    id: "4",
    content: require("../../../assets/images/slides/Slide4.png"),
    duration: 3000,
  },
]

interface OnboardingScreenProps extends AppStackScreenProps<"Onboarding"> {}

export const OnboardingScreen: FC<OnboardingScreenProps> = ({ navigation }) => {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isPaused, setIsPaused] = useState(false)
  const progressRef = useRef(new Animated.Value(0)).current
  const timerRef = useRef<NodeJS.Timeout | null>(null)
  const progressAnimationRef = useRef<Animated.CompositeAnimation | null>(null)
  const insets = useSafeAreaInsets()

  const currentSlide = SLIDES[currentIndex]
  const isLastSlide = currentIndex === SLIDES.length - 1

  // Mark onboarding as completed and navigate to Welcome
  const completeOnboarding = useCallback(() => {
    markOnboardingCompleted()
    navigation.replace("Welcome")
  }, [navigation])

  // Clean up timers and animations
  const cleanupTimers = useCallback(() => {
    if (timerRef.current) {
      clearTimeout(timerRef.current)
      timerRef.current = null
    }
    if (progressAnimationRef.current) {
      progressAnimationRef.current.stop()
      progressAnimationRef.current = null
    }
  }, [])

  // Start progress animation and timer
  const startProgress = useCallback(() => {
    if (isPaused) return

    cleanupTimers()
    progressRef.setValue(0)

    // Animate progress bar
    progressAnimationRef.current = Animated.timing(progressRef, {
      toValue: 1,
      duration: currentSlide.duration,
      useNativeDriver: false,
    })

    progressAnimationRef.current.start(({ finished }) => {
      if (finished && !isPaused) {
        if (isLastSlide) {
          completeOnboarding()
        } else {
          setCurrentIndex((prev) => prev + 1)
        }
      }
    })
  }, [currentSlide.duration, isPaused, isLastSlide, progressRef, cleanupTimers, completeOnboarding])

  // Pause progress
  const pauseProgress = useCallback(() => {
    setIsPaused(true)
    if (progressAnimationRef.current) {
      progressAnimationRef.current.stop()
    }
  }, [])

  // Resume progress
  const resumeProgress = useCallback(() => {
    setIsPaused(false)
  }, [])

  // Navigation handlers
  const goToNext = useCallback(() => {
    if (isLastSlide) {
      completeOnboarding()
    } else {
      setCurrentIndex((prev) => prev + 1)
    }
  }, [isLastSlide, completeOnboarding])

  const goToPrevious = useCallback(() => {
    if (currentIndex > 0) {
      setCurrentIndex((prev) => prev - 1)
    }
  }, [currentIndex])

  // const handleSkip = useCallback(() => {
  //   navigation.replace("Welcome")
  // }, [navigation])

  // Handle left tap (previous slide)
  const handleLeftTap = useCallback(() => {
    goToPrevious()
  }, [goToPrevious])

  // Handle right tap (next slide)
  const handleRightTap = useCallback(() => {
    goToNext()
  }, [goToNext])

  // Handle press in (pause)
  const handlePressIn = useCallback(() => {
    pauseProgress()
  }, [pauseProgress])

  // Handle press out (resume)
  const handlePressOut = useCallback(() => {
    resumeProgress()
  }, [resumeProgress])

  // Effects
  useEffect(() => {
    startProgress()
    return cleanupTimers
  }, [currentIndex, startProgress, cleanupTimers])

  useEffect(() => {
    if (!isPaused) {
      startProgress()
    }
  }, [isPaused, startProgress])

  // Cleanup on unmount
  useEffect(() => {
    return cleanupTimers
  }, [cleanupTimers])

  const renderMedia = () => {
    return <Image source={currentSlide.content} style={$fullScreenImage} resizeMode="cover" />
  }

  return (
    <>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent={true} />
      <View style={$root}>
        {/* Full Screen Image */}
        {renderMedia()}

        {/* Progress Bars */}
        <View style={[$progressContainer, { paddingTop: insets.top + spacing.md }]}>
          {SLIDES.map((_, index) => (
            <View key={index} style={$progressBarBackground}>
              <Animated.View
                style={[
                  $progressBarFill,
                  {
                    width:
                      index === currentIndex
                        ? progressRef.interpolate({
                            inputRange: [0, 1],
                            outputRange: ["0%", "100%"],
                          })
                        : index < currentIndex
                          ? "100%"
                          : "0%",
                  },
                ]}
              />
            </View>
          ))}
        </View>

        {/* Touch Areas for Navigation */}
        <TouchableWithoutFeedback
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          onPress={handleLeftTap}
        >
          <View style={$leftTouchArea} />
        </TouchableWithoutFeedback>

        <TouchableWithoutFeedback
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          onPress={handleRightTap}
        >
          <View style={$rightTouchArea} />
        </TouchableWithoutFeedback>

        {/* Pause Indicator */}
        {isPaused && <View style={$pauseIndicator} />}
      </View>
    </>
  )
}

// Styles
const $root: ViewStyle = {
  flex: 1,
  backgroundColor: "#000",
  position: "relative",
}

const $fullScreenImage: ImageStyle = {
  position: "absolute",
  top: 0,
  left: 0,
  width: screenWidth,
  height: screenHeight,
  zIndex: 1,
}

const $progressContainer: ViewStyle = {
  flexDirection: "row",
  paddingHorizontal: spacing.md,
  gap: spacing.xs,
  zIndex: 10,
}

const $progressBarBackground: ViewStyle = {
  flex: 1,
  height: 3,
  backgroundColor: "rgba(255, 255, 255, 0.3)",
  borderRadius: 2,
  overflow: "hidden",
}

const $progressBarFill: ViewStyle = {
  height: "100%",
  backgroundColor: colors.palette.neutral100,
  borderRadius: 2,
}

const $leftTouchArea: ViewStyle = {
  position: "absolute",
  left: 0,
  top: 0,
  bottom: 0,
  width: screenWidth * 0.3,
  zIndex: 5,
}

const $rightTouchArea: ViewStyle = {
  position: "absolute",
  right: 0,
  top: 0,
  bottom: 0,
  width: screenWidth * 0.7,
  zIndex: 5,
}

const $pauseIndicator: ViewStyle = {
  position: "absolute",
  top: "50%",
  left: "50%",
  transform: [{ translateX: -20 }, { translateY: -20 }],
  flexDirection: "row",
  // backgroundColor: "rgba(0, 0, 0, 0.5)",
  // borderRadius: 20,
  // padding: spacing.sm,
  gap: spacing.xs,
}

// const $pauseBar: ViewStyle = {
//   width: 4,
//   height: 20,
//   backgroundColor: colors.palette.neutral100,
//   borderRadius: 2,
// }

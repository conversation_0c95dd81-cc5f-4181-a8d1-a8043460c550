/* eslint-disable @typescript-eslint/no-unused-vars */
import { FC, useEffect } from "react"
import { TextStyle, View, ViewStyle } from "react-native"
import { Text, Screen, WelcomeHeader, Button } from "@/components"
import { isRTL } from "@/i18n"
import { AppStackScreenProps } from "../../navigators"
import { $Gstyles, colors, type ThemedStyle } from "@/theme"
import { useSafeAreaInsetsStyle } from "../../utils/useSafeAreaInsetsStyle"
import { useAppTheme } from "@/utils/useAppTheme"
import { LinearGradient } from "expo-linear-gradient"
import { useAuthStore } from "@/store/AuthenticationStore"
import { isAuthStateProblematic, forceCleanLogout } from "@/utils/authUtils"

interface WelcomeScreenProps extends AppStackScreenProps<"Welcome"> {}

export const WelcomeScreen: FC<WelcomeScreenProps> = ({ navigation }) => {
  const { themed, theme } = useAppTheme()
  const { isLoggingOut } = useAuthStore()

  // Check for problematic auth state when Welcome screen loads
  useEffect(() => {
    if (isAuthStateProblematic()) {
      console.log("🚨 Detected problematic auth state, forcing clean logout...")
      forceCleanLogout()
    }
  }, [])

  function gotoLogin() {
    // Prevent navigation if logout is in progress
    if (isLoggingOut) {
      console.log("🔒 Logout in progress, preventing navigation to login...")
      return
    }
    navigation.navigate("Login")
  }

  function CreateAccount() {
    // Prevent navigation if logout is in progress
    if (isLoggingOut) {
      console.log("🔒 Logout in progress, preventing navigation to account creation...")
      return
    }
    navigation.navigate("AccountType")
  }

  const $bottomContainerInsets = useSafeAreaInsetsStyle(["bottom"])

  return (
    <Screen preset="fixed" contentContainerStyle={$Gstyles.flex1}>
      <LinearGradient
        // Button Linear Gradient
        colors={[colors.palette.neutral900, colors.palette.neutral700]}
        style={$Gstyles.flex1}
      >
        <View style={themed($topContainer)}>
          <WelcomeHeader />
        </View>

        <View style={themed([$bottomContainer, $bottomContainerInsets])}>
          <Text
            testID="welcome-heading"
            style={themed($welcomeHeading)}
            text="Bienvenue à"
            preset="heading"
          />
          <Text
            testID="welcome-heading"
            style={themed($welcomeHeading)}
            text="Fedha"
            preset="heading"
          />
          <Text
            text="Ouvrez un compte personnel ou professionnel en moins de 10 minutes."
            size="sm"
            preset="formLabel"
            style={{ color: colors.palette.neutral300 }}
          />
          <View style={themed($buttonContainer)}>
            <Button
              testID="next-screen-button"
              preset="reversed"
              textStyle={{ color: colors.palette.neutral900 }}
              text="Je suis nouveau sur Fedha"
              style={themed($buttonStyle1)}
              onPress={CreateAccount}
            />
            <View style={$buttonSpacing} />
            <Button
              testID="next-screen-button"
              preset="reversed"
              text="J'ai déjà un compte"
              style={themed($buttonStyle2)}
              onPress={gotoLogin}
            />
          </View>
        </View>
      </LinearGradient>
    </Screen>
  )
}

const $topContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexShrink: 1,
  flexGrow: 1,
  flexBasis: "55%",
  // justifyContent: "center",
  paddingHorizontal: spacing.lg,
})

const $bottomContainer: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  flexShrink: 1,
  flexGrow: 0,
  flexBasis: "42%",
  // backgroundColor: colors.palette.neutral100,
  borderTopLeftRadius: 16,
  borderTopRightRadius: 16,
  paddingHorizontal: spacing.lg,
  // justifyContent: "space-around",
})

const $buttonSpacing: ViewStyle = {
  marginTop: 16, // Adds space between buttons
}

const $buttonContainer: ViewStyle = {
  flexGrow: 1, // Push buttons to bottom
  justifyContent: "flex-end", // Align to bottom
  alignItems: "center", // Center buttons horizontally
  width: "100%",
  paddingBottom: 25,
}

const $buttonStyle1: ViewStyle = {
  backgroundColor: colors.palette.neutral200, // Example: Blue
  borderRadius: 20,
  paddingVertical: 20,

  width: "100%", // Full width
}

const $buttonStyle2: ViewStyle = {
  backgroundColor: colors.palette.primary500, // Example: Green
  borderRadius: 20,
  paddingVertical: 20,
  width: "100%", // Full width
}

const $welcomeHeading: ThemedStyle<TextStyle> = ({ spacing }) => ({
  // marginBottom: spacing.md,
  color: colors.palette.neutral100,
})

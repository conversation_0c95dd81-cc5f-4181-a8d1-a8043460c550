import { FC } from "react"
import { ViewStyle } from "react-native"
import { AppStackScreenProps } from "@/navigators"
import { Screen, Text } from "@/components"
// import { useNavigation } from "@react-navigation/native"

interface HowitWorksScreenProps extends AppStackScreenProps<"HowitWorks"> {}


export const HowitWorksScreen: FC<HowitWorksScreenProps> = () => {

  // Pull in navigation via hook
  // const navigation = useNavigation()
  return (
    <Screen style={$root} preset="scroll">
      <Text text="howitWorks" />
    </Screen>
  )

}

const $root: ViewStyle = {
  flex: 1,
}

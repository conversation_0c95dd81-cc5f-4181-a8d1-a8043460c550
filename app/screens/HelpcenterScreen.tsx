/* eslint-disable no-restricted-imports */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable react-native/no-color-literals */
/* eslint-disable react-native/sort-styles */
import React, { FC, useState, useCallback, useEffect } from "react"
import {
  View,
  ViewStyle,
  StyleSheet,
  TouchableOpacity,
  LayoutAnimation, // For simple accordion animation
  Platform,
  UIManager,
  ScrollView, // For horizontal categories
  FlatList, // For lists
  TextStyle,
  TextInput, // If TextField is not suitable for search
  ActivityIndicator,
} from "react-native"
import { AppStackScreenProps } from "@/navigators"
import { Header, Icon, Screen, Text /*, TextField*/ } from "@/components" // Assuming TextField exists
import { colors, spacing, typography } from "@/theme"
import { contactMethods } from "@/utils/constant"
import { useStores } from "@/store"

// Enable LayoutAnimation on Android
if (Platform.OS === "android" && UIManager.setLayoutAnimationEnabledExperimental) {
  UIManager.setLayoutAnimationEnabledExperimental(true)
}

interface HelpcenterScreenProps extends AppStackScreenProps<"Helpcenter"> {}

type ActiveTab = "faq" | "contact"

// Define FAQ item interface
interface FAQItem {
  id: string
  question: string
  answer: string
  category: string
}

// --- FAQ Item Component ---
// Moved outside for clarity, manages its own expanded state
const FaqItem: FC<{ item: FAQItem }> = ({ item }) => {
  const [isExpanded, setIsExpanded] = useState(false)

  const toggleExpand = () => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut) // Simple animation
    setIsExpanded(!isExpanded)
  }

  return (
    <View style={styles.faqItemContainer}>
      <TouchableOpacity style={styles.faqQuestionRow} onPress={toggleExpand} activeOpacity={0.7}>
        <Text style={styles.faqQuestionText} text={item.question} />
        <Icon
          icon={isExpanded ? "arrwup" : "downarrow"} // Adjust icon names
          size={20}
          color={colors.tint}
        />
      </TouchableOpacity>
      {isExpanded && (
        <View style={styles.faqAnswerContainer}>
          <Text style={styles.faqAnswerText} text={item.answer} />
        </View>
      )}
    </View>
  )
}

// --- Main Screen Component ---
export const HelpcenterScreen: FC<HelpcenterScreenProps> = ({ navigation }) => {
  const {
    appsettings: { fetchFAQacontent, fetchFAQData, isFetchingFAQ },
  } = useStores()

  const [activeTab, setActiveTab] = useState<ActiveTab>("faq")
  const [selectedCategory, setSelectedCategory] = useState<string>("General")
  const [searchQuery, setSearchQuery] = useState("")
  const [faqs, setFaqs] = useState<FAQItem[]>([])
  const [categories, setCategories] = useState<string[]>([])

  // Fetch FAQ data when component mounts
  useEffect(() => {
    fetchFAQData()
  }, [fetchFAQData])

  // Update local state when API data changes
  useEffect(() => {
    if (fetchFAQacontent && Array.isArray(fetchFAQacontent)) {
      setFaqs(fetchFAQacontent)

      // Extract unique categories from the FAQ data
      const uniqueCategories = Array.from(
        new Set(fetchFAQacontent.map((faq: FAQItem) => faq.category)),
      )
      setCategories(uniqueCategories)

      // If the selected category is not in the list, select the first one
      if (uniqueCategories.length > 0 && !uniqueCategories.includes(selectedCategory)) {
        setSelectedCategory(uniqueCategories[0])
      }
    }
  }, [fetchFAQacontent, selectedCategory])

  // Filter FAQs based on search and category
  const filteredFaqs = faqs.filter(
    (faq: FAQItem) =>
      faq.category === selectedCategory &&
      (searchQuery === "" ||
        faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
        faq.answer.toLowerCase().includes(searchQuery.toLowerCase())),
  )

  const renderFaqItem = useCallback(({ item }: { item: FAQItem }) => <FaqItem item={item} />, [])
  const renderContactItem = useCallback(
    ({ item }: { item: (typeof contactMethods)[0] }) => (
      <TouchableOpacity style={styles.contactItem} onPress={item.action} activeOpacity={0.7}>
        <Icon icon={item.icon as any} size={24} color={colors.tint} style={styles.contactIcon} />
        <Text style={styles.contactLabel} text={item.label} />
      </TouchableOpacity>
    ),
    [],
  )

  return (
    <>
      <Header
        title="Help Center"
        titleStyle={$headerTitle}
        leftIcon="backicon"
        onLeftPress={navigation.goBack}
        // rightIcon="dots-vertical" // Or your specific icon name for options
        onRightPress={() => console.log("More options")} // Placeholder action
        // style={$header}
      />
      <Screen style={$root} preset="fixed" safeAreaEdges={["bottom"]} statusBarStyle="dark">
        {/* Tab Bar */}
        <View style={styles.tabBar}>
          <TouchableOpacity
            style={styles.tabButton}
            onPress={() => setActiveTab("faq")}
            activeOpacity={0.7}
          >
            <Text
              style={[styles.tabText, activeTab === "faq" && styles.activeTabText]}
              text="FAQ"
            />
            {activeTab === "faq" && <View style={styles.activeTabIndicator} />}
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.tabButton}
            onPress={() => setActiveTab("contact")}
            activeOpacity={0.7}
          >
            <Text
              style={[styles.tabText, activeTab === "contact" && styles.activeTabText]}
              text="Chat with Us"
            />
            {activeTab === "contact" && <View style={styles.activeTabIndicator} />}
          </TouchableOpacity>
        </View>

        {/* Content Area */}
        {activeTab === "faq" && (
          <View style={styles.contentContainer}>
            <View>
              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={styles.categoriesScrollView}
              >
                {categories.map((category) => (
                  <TouchableOpacity
                    key={category}
                    style={[
                      styles.categoryChip,
                      selectedCategory === category && styles.selectedCategoryChip,
                    ]}
                    onPress={() => setSelectedCategory(category)}
                    activeOpacity={0.7}
                  >
                    <Text
                      style={[
                        styles.categoryChipText,
                        selectedCategory === category && styles.selectedCategoryChipText,
                      ]}
                      text={category}
                    />
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>

            {/* Search Bar */}
            <View style={styles.searchBarContainer}>
              <Icon icon="search" size={20} color={colors.tint} style={styles.searchIcon} />
              <TextInput
                style={styles.searchInput}
                placeholder="Search for help"
                placeholderTextColor={colors.textDim}
                value={searchQuery}
                onChangeText={setSearchQuery}
              />
              {/* <TouchableOpacity style={styles.filterIconContainer}>
                <Icon icon="x" size={22} color={colors.tint} />
              </TouchableOpacity> */}
            </View>

            {isFetchingFAQ ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={colors.tint} />
                <Text text="Loading FAQ data..." style={styles.loadingText} />
              </View>
            ) : (
              <FlatList
                data={filteredFaqs}
                renderItem={renderFaqItem}
                keyExtractor={(item) => item.id}
                contentContainerStyle={styles.faqListContainer}
                ListEmptyComponent={
                  <Text text="No matching FAQs found." style={styles.emptyListText} />
                }
              />
            )}
          </View>
        )}

        {activeTab === "contact" && (
          <View style={[styles.contentContainer, styles.contactContentContainer]}>
            <FlatList
              data={contactMethods}
              renderItem={renderContactItem}
              keyExtractor={(item) => item.id}
              ItemSeparatorComponent={() => <View style={styles.contactSeparator} />}
            />
          </View>
        )}
      </Screen>
    </>
  )
}

// --- Styles ---
const styles = StyleSheet.create({
  root: {
    // backgroundColor: colors.background,
    flex: 1, // Clean background
  },
  // header: {
  //   backgroundColor: colors.background, // Match screen background
  //   borderBottomWidth: 0, // Remove default header border if any
  // },
  headerTitle: {
    // Style if needed
  },
  tabBar: {
    flexDirection: "row",
    // backgroundColor: colors.palette.neutral100, // Optional subtle background
    borderBottomWidth: 1,
    borderBottomColor: colors.separator, // Subtle separator line
  },
  tabButton: {
    alignItems: "center",
    flex: 1,
    justifyContent: "center",
    paddingVertical: spacing.md,
  },
  tabText: {
    color: colors.textDim,
    fontFamily: typography.primary.medium,
    fontSize: 15, // Dim color for inactive tabs
  },
  activeTabText: {
    color: colors.text, // Primary text color for active tab
    fontWeight: "bold",
  },
  activeTabIndicator: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    height: 2.5,
    backgroundColor: colors.tint, // Use theme's tint color
    borderRadius: 2,
  },
  contentContainer: {
    // flex: 1, // Take remaining space
    paddingVertical: spacing.xl,
  },
  contactContentContainer: {
    // paddingTop: spacing.lg, // Add padding for the contact list
    paddingHorizontal: spacing.md,
  },
  // --- FAQ Styles ---
  categoriesScrollView: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md, // Horizontal padding for the whole scroll area
  },
  categoryChip: {
    paddingVertical: spacing.xs + 2,
    paddingHorizontal: spacing.md + 2,
    borderRadius: 20,
    backgroundColor: colors.palette.neutral200, // Inactive chip background
    marginRight: spacing.sm, // Space between chips
    borderWidth: 1,
    borderColor: "transparent",
  },
  selectedCategoryChip: {
    backgroundColor: colors.palette.primary100, // Selected chip background (light primary)
    borderColor: colors.tint, // Selected chip border
  },
  categoryChipText: {
    color: colors.text,
    fontSize: 14,
    fontWeight: "500",
  },
  selectedCategoryChipText: {
    color: colors.tint, // Selected text color
    fontWeight: "bold",
  },
  searchBarContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: colors.palette.neutral100, // Light background for search bar
    borderRadius: 12,
    paddingHorizontal: spacing.md,
    marginHorizontal: spacing.lg,
    marginTop: spacing.sm,
    marginBottom: spacing.lg,
    borderWidth: 1,
    borderColor: colors.border,
  },
  searchIcon: {
    marginRight: spacing.sm,
  },
  searchInput: {
    flex: 1,
    height: 48, // Define height
    fontSize: 15,
    color: colors.text,
    paddingVertical: 0, // Reset default padding if necessary
  },
  // filterIconContainer: {
  //   padding: spacing.xs,
  // },
  faqListContainer: {
    paddingBottom: spacing.xxl,
    paddingHorizontal: spacing.lg, // Ensure scroll space
  },
  faqItemContainer: {
    borderBottomWidth: 1,
    borderBottomColor: colors.separator, // Separator line
    paddingVertical: spacing.sm,
  },
  faqQuestionRow: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "space-between",
    paddingVertical: spacing.sm + 2,
  },
  faqQuestionText: {
    flex: 1, // Allow text to wrap
    fontSize: 15,
    color: colors.text,
    fontWeight: "500",
    paddingRight: spacing.md, // Space before icon
  },
  faqAnswerContainer: {
    paddingBottom: spacing.sm,
    paddingLeft: spacing.xxs,
    paddingTop: spacing.xs, // Slight indent for answer
  },
  faqAnswerText: {
    color: colors.textDim,
    fontSize: 14,
    lineHeight: 20,
  },
  emptyListText: {
    color: colors.textDim,
    marginTop: spacing.xl,
    textAlign: "center",
  },

  // --- Contact Us Styles ---
  contactItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: spacing.lg - 2,
    paddingHorizontal: spacing.md,
    backgroundColor: colors.palette.neutral100, // Light background for items
    borderRadius: 12,
  },
  contactIcon: {
    marginRight: spacing.md,
  },
  contactLabel: {
    flex: 1, // Take available space
    fontSize: 16,
    color: colors.text,
    fontWeight: "500",
  },
  contactSeparator: {
    height: spacing.sm, // Just space between items
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: spacing.xl,
  },
  loadingText: {
    marginTop: spacing.md,
    color: colors.textDim,
  },
})

// --- Export Styles (Optional) ---
const $root: ViewStyle = styles.root
// const $header: ViewStyle = styles.header
const $headerTitle: TextStyle = styles.headerTitle

import { FC } from "react"
import { ViewStyle } from "react-native"
import { AppStackScreenProps } from "@/navigators"
import { Screen, Text } from "@/components"
// import { useNavigation } from "@react-navigation/native"

interface BussinessActivitiesScreenProps extends AppStackScreenProps<"BussinessActivities"> {}

export const BussinessActivitiesScreen: FC<BussinessActivitiesScreenProps> = () => {
  // Pull in navigation via hook
  // const navigation = useNavigation()
  return (
    <Screen style={$root} preset="scroll">
      <Text text="bussinessActivities" />
    </Screen>
  )
}

const $root: ViewStyle = {
  flex: 1,
}

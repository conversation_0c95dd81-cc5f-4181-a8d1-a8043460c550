/* eslint-disable react-native/no-inline-styles */
/* eslint-disable no-restricted-imports */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable import/no-unresolved */
import { FC, useEffect, useState } from "react"
import { TextStyle, View, ViewStyle, Alert, Image, Modal, ImageStyle } from "react-native"
import { AppStackScreenProps } from "@/navigators"
import {
  AmountInput,
  Box,
  Button,
  ChangeCurency,
  CustomSelector,
  FedhaPochiBar,
  FedhaPochiInput,
  FencyTextInput,
  Header,
  Icon,
  Screen,
  Text,
  TextField,
} from "@/components"
import { $Gstyles, colors, spacing, ThemedStyle } from "@/theme"
import { useStores } from "@/store/rootStore"
import { useAppTheme } from "@/utils/useAppTheme"
import { checkProvider, WithdrawRequest } from "@/services/api"
import { useForm, Controller } from "react-hook-form"
import { z } from "zod"
import { zodResolver } from "@hookform/resolvers/zod"
import React from "react"

interface WithdrawScreenProps extends AppStackScreenProps<"Withdraw"> {}

// Define the form schema using Zod
const withdrawFormSchema = z.object({
  amount: z.string().refine((val) => {
    const amount = parseFloat(val)
    return !isNaN(amount) && amount > 0
  }, "Montant invalide"),
  phone_number: z
    .string()
    .min(9, "Le numéro doit contenir 9 chiffres")
    .max(9, "Le numéro doit contenir 9 chiffres")
    .regex(/^\d{9}$/, "Le numéro doit contenir uniquement des chiffres"),
  wallet_type: z.string().min(1, "Sélectionnez un portefeuille"),
  note: z
    .string()
    .min(1, "La note est obligatoire")
    .max(50, "La note ne peut pas dépasser 50 caractères"),
})

type WithdrawFormData = z.infer<typeof withdrawFormSchema>

export const WithdrawScreen: FC<WithdrawScreenProps> = ({ navigation }) => {
  const {
    fedhapochi: { wallet, currentBalance },
    appsettings: { currency, getServerCurrencyCode, getExchangeRate, currencies },
  } = useStores()
  const { themed } = useAppTheme()
  const [isLoading, setIsLoading] = useState(false)
  const [showCurrencyModal, setShowCurrencyModal] = useState(false)
  const [transferMethod, setTransferMethod] = useState<"ExtWallet">("ExtWallet")
  const [showConfirmModal, setShowConfirmModal] = useState(false)
  const [isTyping, setIsTyping] = useState(false)
  const [carrierName, setCarrierName] = useState<string | null>(null)
  const [note, setNote] = useState("")

  const {
    control,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch,
  } = useForm<WithdrawFormData>({
    resolver: zodResolver(withdrawFormSchema),
    defaultValues: {
      amount: "",
      phone_number: "",
      wallet_type: "",
      note: "", // Add default value for note
    },
    mode: "onChange", // Enable real-time validation
  })

  // Add this to track the form values in real-time
  const watchAmount = watch("amount")

  // Currencies are now loaded at app startup
  // No need to fetch currencies here

  const walletOptions = [
    { label: "Vodacom", value: "mpesa" },
    { label: "AIRTEL", value: "airtel" },
    { label: "ORANGE", value: "orange" },
  ]

  const handleConfirmSubmit = async () => {
    try {
      setIsLoading(true)
      const formData = watch()

      // Additional validation for note
      if (!formData.note.trim()) {
        Alert.alert("Erreur", "Veuillez ajouter une note pour cette transaction")
        return
      }

      const response = await WithdrawRequest({
        receiver_phone: formData.phone_number,
        amount: parseFloat(formData.amount),
        currency,
        network: formData.wallet_type,
        note: formData.note.trim(),
      })

      if (response.success) {
        setShowConfirmModal(false)
        Alert.alert("Succès", "Votre demande de retrait a été traitée avec succès", [
          {
            text: "OK",
            onPress: () => {
              reset()
              navigation.goBack()
            },
          },
        ])
      } else {
        throw new Error(response.message)
      }
    } catch (error: any) {
      let errorMessage = "Une erreur est survenue lors du retrait"

      if (error.response?.status === 400) {
        errorMessage = "Les informations fournies sont invalides"
      } else if (error.response?.status === 401) {
        errorMessage = "Session expirée. Veuillez vous reconnecter"
      } else if (error.response?.status === 403) {
        errorMessage = "Vous n'êtes pas autorisé à effectuer cette opération"
      } else if (error.response?.status === 429) {
        errorMessage = "Trop de tentatives. Veuillez réessayer plus tard"
      } else if (error.message) {
        errorMessage = error.message
      }

      Alert.alert("Erreur", errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  const renderWithdrawalOptions = () => (
    <View style={$optionsContainer}>
      <View style={[$optionButton, $optionButtonSelected]}>
        <Icon icon="wallet" size={24} color={colors.palette.primary100} />
        <View style={$optionTextContainer}>
          <Text preset="subheading" style={[$optionTitle, $optionTitleSelected]}>
            Autre Portefeuille
          </Text>
          <Text preset="formHelper" style={[$optionDescription, $optionDescriptionSelected]}>
            M-PESA, Airtel Money, Orange Money
          </Text>
        </View>
        <Icon icon="check" size={20} color={colors.palette.primary100} />
      </View>
    </View>
  )

  const renderWithdrawalForm = () => {
    if (!transferMethod) return null

    return (
      <View style={$formContainer}>
        <View>
          <Controller
            control={control}
            name="amount"
            rules={{
              required: "Le montant est obligatoire",
              validate: (value) => {
                const amount = parseFloat(value)
                if (isNaN(amount)) return "Le montant doit être un nombre valide"

                // Get exchange rate and convert amounts using API rates
                if (currency === "USD") {
                  if (amount < 2) return "Le montant minimum est de 2 USD"
                  // Convert balance to USD using API exchange rate
                  const balanceInUSD = getExchangeRate("FC", "USD") * currentBalance
                  if (amount > balanceInUSD)
                    return `Solde insuffisant. Votre solde est de ${balanceInUSD.toFixed(2)} USD`
                } else if (currency === "FC") {
                  if (amount < 5000) return "Le montant minimum est de 5000 FC"
                  // Convert balance to FC using API exchange rate
                  const balanceInFC = getExchangeRate("USD", "FC") * currentBalance
                  if (amount > balanceInFC)
                    return `Solde insuffisant. Votre solde est de ${balanceInFC.toFixed(2)} FC`
                }
                return true
              },
            }}
            render={({ field: { onChange, value } }) => (
              <FencyTextInput
                value={value}
                onChange={onChange}
                inputname={`Montant à Retirer (${currency})`}
                keyboardType="numeric"
                placeholder="Saisir le montant"
                LeftIcon={currency === "FC" ? "FC" : "dollar"}
                leftonPress={() => setShowCurrencyModal(!showCurrencyModal)}
                helper={errors.amount?.message}
                status={errors.amount ? "error" : undefined}
              />
            )}
          />
          {errors.amount && <Text style={$errorText}>{errors.amount.message}</Text>}
        </View>

        <Controller
          control={control}
          name="wallet_type"
          render={({ field: { onChange, value } }) => (
            <CustomSelector
              inputname="Sélectionnez le portefeuille"
              value={value}
              onValueChange={onChange}
              isitems={walletOptions}
              placeholder={{ label: "Sélectionnez le réseau", value: null }}
              // status={errors.wallet_type ? "error" : undefined}
            />
          )}
        />
        <View style={$inputGroup}>
          <Text text="Numéro de téléphone" preset="formLabel" style={inputLabel} />
          <Controller
            control={control}
            name="phone_number"
            render={({ field: { onChange, value } }) => (
              // <FencyTextInput
              //   value={value}
              //   maxLength={9}
              //   onChange={onChange}
              //   inputname="Numéro de téléphone"
              //   keyboardType="numeric"
              //   placeholder="Entrez le numéro"
              //   helper={errors.phone_number?.message}
              //   status={errors.phone_number ? "error" : undefined}
              // />
              <TextField
                value={value}
                onChangeText={(text) => {
                  setIsTyping(true)
                  setTimeout(() => setIsTyping(false), 1000)

                  // Remove non-digits and leading zeros
                  const formatted = text.replace(/\D/g, "").replace(/^0+/, "")
                  if (formatted.length <= 9) {
                    onChange(formatted)
                  }
                }}
                containerStyle={[$input, isTyping && { borderWidth: 1 }]}
                autoCapitalize="none"
                onFocus={() => setIsTyping(true)}
                onBlur={async () => {
                  setIsTyping(false)

                  //               if (value.length === 9) {
                  //                 const carrier = await checkProvider(value)
                  //                 console.log("Carrier:", carrier)
                  // setCarrierName(carrier)
                  //                 // Optionally show the carrier name somewhere in UI
                  //               }
                }}
                keyboardType="numeric"
                placeholderTextColor={colors.palette.neutral400}
                autoCorrect={false}
                maxLength={9}
                placeholder="00 000 0000"
                LeftAccessory={() => (
                  <View style={phonePrefix}>
                    <Image
                      source={require("../../assets/images/flags/cd.png")}
                      style={$flagIcon}
                      resizeMode="cover"
                    />
                    <Text style={$countryCode}>+243</Text>
                  </View>
                )}
                // status={
                //   fieldState.error && fieldState.isDirty && !isTyping ? "error" : undefined
                // }
                // helper={
                //   fieldState.error && fieldState.isDirty && !isTyping
                //     ? fieldState.error.message
                //     : undefined
                // }
              />
            )}
          />
          {/* {carrierName && (
  <Text style={{ marginTop: 4, color: colors.palette.neutral200 }}>
    Réseau détecté : {carrierName}
  </Text>
)} */}
        </View>

        <Button
          testID="withdraw-button"
          preset="reversed"
          text="Retirer"
          style={themed($button)}
          onPress={() => {
            const formData = watch()
            if (formData.amount && formData.wallet_type && formData.phone_number) {
              setShowConfirmModal(true)
            } else {
              Alert.alert(
                "Informations manquantes",
                "Veuillez saisir le montant, sélectionner le type de réseau, et entrer le numéro du destinataire.",
              )
            }
          }}
          // disabled={}
        />
      </View>
    )
  }

  useEffect(() => {
    console.log("Current Balance:", currentBalance)
    console.log("Current Currency:", currency)
  }, [currentBalance, currency])

  return (
    <>
      <Header
        leftIcon={"backicon"}
        onLeftPress={navigation.goBack}
        // onRightPress={handleRightPress}
        // rightIcon="question"
        title="Retrait d'argent "
        backgroundColor="white"
      />
      <Screen style={$root} preset="scroll" safeAreaEdges={["bottom"]} statusBarStyle="dark">
        <Text text="À partir de" />
        <FedhaPochiBar />
        <Text text="Vers" />
        {renderWithdrawalOptions()}
        {renderWithdrawalForm()}
        <View />
      </Screen>
      <ChangeCurency isVisible={showCurrencyModal} onClose={() => setShowCurrencyModal(false)} />
      <Modal visible={showConfirmModal} transparent animationType="slide">
        <View style={$modalOverlay}>
          <View style={$modalContent}>
            <Text preset="heading" style={$modalTitle}>
              Confirmer le retrait
            </Text>

            <View style={$detailsContainer}>
              <View style={$detailRow}>
                <Text preset="subheading">Montant:</Text>
                <Text preset="bold">
                  {watch("amount")} {currency}
                </Text>
              </View>
              <View style={$detailRow}>
                <Text preset="subheading">Réseau:</Text>
                <Text preset="bold">
                  {walletOptions.find((opt) => opt.value === watch("wallet_type"))?.label}
                </Text>
              </View>
              <View style={$detailRow}>
                <Text preset="subheading">Numéro:</Text>
                <Text preset="bold">{watch("phone_number")}</Text>
              </View>
            </View>

            <Controller
              control={control}
              name="note"
              rules={{
                required: "La note est obligatoire",
                maxLength: {
                  value: 50,
                  message: "La note ne peut pas dépasser 50 caractères",
                },
              }}
              render={({ field: { onChange, value } }) => (
                <View>
                  <FencyTextInput
                    value={value}
                    onChange={onChange}
                    inputname="Note *"
                    placeholder="Ajouter une note (obligatoire)"
                    maxLength={50}
                    status={errors.note ? "error" : undefined}
                  />
                  {errors.note && <Text style={$errorText}>{errors.note.message}</Text>}
                </View>
              )}
            />

            <View style={$modalButtons}>
              <Button
                text="Annuler"
                preset="default"
                style={[$modalButton, $cancelButton]}
                onPress={() => setShowConfirmModal(false)}
              />
              <Button
                text={isLoading ? "Traitement..." : "Confirmer"}
                preset="reversed"
                style={[$modalButton, $confirmButton]}
                onPress={handleSubmit(handleConfirmSubmit)}
                disabled={isLoading || !!errors.note}
              />
            </View>
          </View>
        </View>
      </Modal>
    </>
  )
}

const $root: ViewStyle = {
  flex: 1,
  // top: 15,
  paddingHorizontal: 20,
  paddingBottom: spacing.xl,
}

const $ActionIcon: ViewStyle = {
  padding: 10,

  marginRight: 20,
  alignItems: "center",
  justifyContent: "center",
  alignContent: "center",
  // backgroundColor: colors.palette.accent300,
  borderRadius: 50,
}
const $form: ViewStyle = {
  // marginBottom: spacing.lg,
  // top: spacing.lg,
  flex: 1,
  // justifyContent: "center",
}
const $inputGroup: ViewStyle = {
    marginBottom: spacing.xl,
  },
  inputLabel: TextStyle = {
    color: colors.palette.neutral900,
    fontSize: 14,
    fontWeight: "600",
    marginBottom: spacing.xs,
  }

const $optionsContainer: ViewStyle = {
  marginTop: spacing.md,
  gap: spacing.xs,
}

const $optionButton: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  padding: spacing.sm,
  borderRadius: 12,
  borderWidth: 1,
  borderColor: colors.palette.neutral300,
  backgroundColor: colors.palette.neutral100,
}

const $optionButtonSelected: ViewStyle = {
  borderColor: colors.palette.neutral900,
  backgroundColor: colors.palette.neutral900,
}
const $input: ViewStyle = {
  backgroundColor: colors.palette.neutral200,
  borderRadius: 14,
  borderWidth: 0,
}
const $flagIcon: ImageStyle = {
  borderRadius: 12,
  height: 24,
  width: 24,
}

const phonePrefix: ViewStyle = {
  alignItems: "center",
  flexDirection: "row",
  gap: spacing.xs,
  paddingLeft: spacing.sm,
  top: spacing.md,
  borderRightColor: colors.palette.neutral300,
  borderRightWidth: 1,
  paddingHorizontal: 10,
}

const $countryCode: TextStyle = {
  color: colors.palette.neutral900,
  fontSize: 16,
  fontWeight: "600",
}

const $optionTextContainer: ViewStyle = {
  flex: 1,
  marginLeft: spacing.sm,
}

const $optionTitle: TextStyle = {
  color: colors.palette.neutral800,
  marginBottom: spacing.xxs,
}

const $optionTitleSelected: TextStyle = {
  color: colors.palette.neutral100,
}

const $optionDescription: TextStyle = {
  color: colors.palette.neutral900,
  fontSize: 12,
}

const $optionDescriptionSelected: TextStyle = {
  color: colors.palette.neutral200,
}

const $formContainer: ViewStyle = {
  marginTop: spacing.xl,
  gap: spacing.sm,
}

const $errorText: TextStyle = {
  color: "red",
  fontSize: 12,
  marginBottom: 5,
}

const $button: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginTop: spacing.sm,
  alignItems: "center",
  backgroundColor: colors.palette.neutral900,
  borderRadius: 10,
  paddingVertical: spacing.lg,
})

const $modalOverlay: ViewStyle = {
  flex: 1,
  backgroundColor: "rgba(0, 0, 0, 0.5)",
  justifyContent: "center",
  alignItems: "center",
}

const $modalContent: ViewStyle = {
  backgroundColor: colors.palette.neutral100,
  borderRadius: 12,
  padding: spacing.lg,
  width: "90%",
  maxWidth: 400,
}

const $modalTitle: TextStyle = {
  textAlign: "center",
  marginBottom: spacing.lg,
}

const $detailsContainer: ViewStyle = {
  backgroundColor: colors.palette.neutral200,
  borderRadius: 8,
  padding: spacing.md,
  marginBottom: spacing.lg,
}

const $detailRow: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
  paddingVertical: spacing.xs,
}

const $modalButtons: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
  marginTop: spacing.lg,
}

const $modalButton: ViewStyle = {
  flex: 1,
  marginHorizontal: spacing.xs,
}

const $cancelButton: ViewStyle = {
  backgroundColor: colors.palette.neutral400,
}

const $confirmButton: ViewStyle = {
  backgroundColor: colors.palette.primary500,
}

// Update the validateAmount function to be more strict

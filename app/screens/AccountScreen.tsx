/* eslint-disable no-dupe-keys */
/* eslint-disable react-native/no-unused-styles */
/* eslint-disable react-native/no-color-literals */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable import/no-unresolved */
import { FC, useState, useEffect } from "react"
import {
  View,
  TouchableOpacity,
  StyleSheet,
  Image,
  Alert,
  Modal,
  ActivityIndicator,
  Dimensions,
} from "react-native"
import { AppStackScreenProps } from "@/navigators"
import { Screen, Text, Icon, Header, Button } from "@/components"
import { colors, spacing } from "@/theme"
import {
  launchImageLibrary,
  ImageLibraryOptions,
  ImagePickerResponse,
} from "react-native-image-picker"
import { Avatar } from "react-native-paper"
import Share from "react-native-share"
import { version } from "../../package.json"
import { capitalizeFirstLetter, getNameInitials } from "@/utils/actions"
import { userProfilePictureUpdate } from "@/services/api"
import RNFS from "react-native-fs"
import { useStores } from "@/store/rootStore"
import React from "react"

interface AccountScreenProps extends AppStackScreenProps<"Account"> {}

interface MenuItemProps {
  icon: string
  title: string
  subtitle?: string
  onPress: () => void
  showChevron?: boolean
  tintColor?: string
}

const MenuItem: FC<MenuItemProps> = ({
  icon,
  title,
  subtitle,
  onPress,
  showChevron = true,
  tintColor,
}) => (
  <TouchableOpacity style={styles.menuItem} onPress={onPress}>
    <View style={styles.menuItemLeft}>
      <Icon icon={icon as any} size={24} color={tintColor || colors.palette.neutral800} />
      <View style={styles.menuItemTextContainer}>
        <Text style={styles.menuItemTitle}>{title}</Text>
        {subtitle && <Text style={styles.menuItemSubtitle}>{subtitle}</Text>}
      </View>
    </View>
    {showChevron && <Icon icon="caretRight" size={20} color={colors.palette.neutral400} />}
  </TouchableOpacity>
)

export const AccountScreen: FC<AccountScreenProps> = ({ navigation }) => {
  const {
    auth: { user, logout, fetchUserData, user_type },
    appsettings: { fetchFAQacontent },
  } = useStores()
  const [profileImage, setProfileImage] = useState<string | null>(null)
  const [appInfoModalVisible, setAppInfoModalVisible] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const [selectedImageUri, setSelectedImageUri] = useState<string | null>(null)
  const [showImagePreview, setShowImagePreview] = useState(false)

  // Initialize profile image from user data
  useEffect(() => {
    if (user?.profile_picture) {
      setProfileImage(user.profile_picture)
    }
  }, [user])

  // Fetch Fedha business account details

  const pickImage = async () => {
    const options: ImageLibraryOptions = {
      mediaType: "photo",
      includeBase64: false,
      maxHeight: 500,
      maxWidth: 500,
      selectionLimit: 1,
    }

    try {
      const response = await new Promise<ImagePickerResponse>((resolve) => {
        launchImageLibrary(options, resolve)
      })

      if (response.didCancel) {
        console.log("User cancelled image picker")
        return
      }

      if (response.errorCode) {
        console.error("ImagePicker Error:", response.errorMessage)
        Alert.alert("Erreur", "Une erreur s'est produite lors de la sélection de l'image")
        return
      }

      if (response.assets && response.assets[0]?.uri) {
        setSelectedImageUri(response.assets[0].uri)
        setShowImagePreview(true)
      }
    } catch (error) {
      console.error("Error picking image:", error)
      Alert.alert("Erreur", "Une erreur s'est produite")
    }
  }

  const handleSaveImage = async () => {
    if (!selectedImageUri) return

    // Close modal immediately
    setShowImagePreview(false)
    setIsUploading(true)

    try {
      const formData = new FormData()
      const filePath = selectedImageUri.replace("file://", "")

      // Check if file exists
      await RNFS.stat(filePath)

      // Get file name from path
      const fileName = filePath.split("/").pop() || "profile_picture.jpg"

      // Append file to FormData
      formData.append("profile_picture", {
        uri: selectedImageUri,
        type: "image/jpeg",
        name: fileName,
      } as any)

      // Make API call
      const result = await userProfilePictureUpdate(formData)

      if (result.success) {
        setProfileImage(selectedImageUri)
        await fetchUserData() // Refresh user data
        Alert.alert("Succès", "Photo de profil mise à jour avec succès")
        setSelectedImageUri(null)
      } else {
        Alert.alert("Erreur", "Échec de la mise à jour de la photo de profil")
      }
    } catch (error) {
      console.error("Error updating profile picture:", error)
      Alert.alert("Erreur", "Une erreur s'est produite lors de la mise à jour de la photo")
    } finally {
      setIsUploading(false)
    }
  }

  const handleShare = async () => {
    try {
      await Share.open({
        message: "Rejoignez-moi sur Fedha! L'application qui simplifie vos transactions.",
        title: "Inviter des amis sur Fedha",
      })
    } catch (error) {
      console.log("Error sharing:", error)
    }
  }

  const handleLogout = () => {
    Alert.alert("Déconnexion", "Êtes-vous sûr de vouloir vous déconnecter ?", [
      { text: "Annuler", style: "cancel" },
      { text: "Déconnexion", onPress: () => logout(), style: "destructive" },
    ])
  }

  const getMenuItems = () => {
    const commonMenuItems = [
      {
        icon: "user",
        title: "Informations personnelles",
        onPress: () => navigation.navigate("AccountInfo"),
        tintColor: undefined, // optional tint color
      },
      {
        icon: "question",
        title: "Centre d'aide",
        onPress: () => navigation.navigate("Helpcenter"),
      },
      {
        icon: "share",
        title: "Inviter des amis",
        onPress: handleShare,
      },
    ]

    const businessMenuItems = [
      {
        icon: "settings",
        title: "Paramètres business",
        onPress: () => navigation.navigate("BusinessMore"),
      },
      // {
      //   icon: "fedhapochi",
      //   title: "Fedha",
      //   // subtitle: fedhaBusinessAccount
      //   //   ? `Solde: ${fedhaBusinessAccount.balance} ${fedhaBusinessAccount.currency}`
      //   //   : "Chargement...",
      //   onPress: () => {
      //     if (fedhaBusinessAccount) {
      //       Alert.alert(
      //         "Détails du compte Fedha Business",
      //         `Numéro de compte: ${fedhaBusinessAccount.account_number}\n` +
      //           `Solde: ${fedhaBusinessAccount.balance} ${fedhaBusinessAccount.currency}\n` +
      //           `Statut: ${fedhaBusinessAccount.status}\n` +
      //           `Créé le: ${new Date(fedhaBusinessAccount.created_at).toLocaleDateString()}`,
      //         [{ text: "OK" }],
      //       )
      //     } else {
      //       Alert.alert("Information", "Chargement des détails du compte...")
      //       fetchFedhaDetails()
      //     }
      //   },
      // },
      ...commonMenuItems,
    ]

    const personalMenuItems = [
      ...commonMenuItems,
      // {
      //   icon: "ring",
      //   title: "Messages",
      //   onPress: () => navigation.navigate("Historic"),
      // },
    ]

    return user_type === "business" ? businessMenuItems : personalMenuItems
  }

  const AppInfoModal = () => (
    <Modal
      visible={appInfoModalVisible}
      transparent={true}
      animationType="fade"
      onRequestClose={() => setAppInfoModalVisible(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <TouchableOpacity
            style={styles.modalCloseButton}
            onPress={() => setAppInfoModalVisible(false)}
          >
            <Icon icon="x" size={24} color={colors.palette.neutral800} />
          </TouchableOpacity>

          <Image
            source={require("../../assets/images/logo.png")}
            style={styles.modalLogo}
            resizeMode="contain"
          />
          <View style={styles.infoSection}>
            <Text style={styles.infoLabel}>Version de l&lsquo;application</Text>
            <Text style={styles.infoValue}>{version}</Text>
          </View>

          <View style={styles.divider} />

          <Text style={styles.companyInfo}>
            Fedha, c’est la réponse à l’exclusion financière en RDC. Nous permettons à chaque
            Congolais d’envoyer, recevoir et gérer son argent facilement, en toute sécurité, où
            qu’il soit. Une solution simple, rapide et accessible pour reprendre le contrôle de ses
            finances.
          </Text>
        </View>
      </View>
    </Modal>
  )

  const ImagePreviewModal = () => (
    <Modal
      visible={showImagePreview}
      transparent={true}
      animationType="slide"
      onRequestClose={() => setShowImagePreview(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Aperçu de la photo</Text>
            <TouchableOpacity onPress={() => setShowImagePreview(false)} style={styles.closeButton}>
              <Icon icon="x" size={24} color={colors.palette.neutral800} />
            </TouchableOpacity>
          </View>

          <View style={styles.imagePreviewContainer}>
            {selectedImageUri && (
              <Image
                source={{ uri: selectedImageUri }}
                style={styles.previewImage}
                resizeMode="contain"
              />
            )}
          </View>

          <View style={styles.modalActions}>
            <Button
              text="Annuler"
              preset="default"
              onPress={() => {
                setShowImagePreview(false)
                setSelectedImageUri(null)
              }}
              style={styles.modalButton}
            />
            <Button
              text="Enregistrer"
              preset="default"
              onPress={handleSaveImage}
              // loading={isUploading}
              style={styles.modalButton}
            />
          </View>
        </View>
      </View>
    </Modal>
  )

  return (
    <>
      <Header
        title="Mon Compte"
        leftIcon="backicon"
        onLeftPress={navigation.goBack}
        backgroundColor={colors.palette.neutral100}
      />
      <Screen style={styles.root} preset="scroll" statusBarStyle="dark">
        {/* Profile Section */}
        <View style={styles.profileSection}>
          <TouchableOpacity
            onPress={pickImage}
            style={styles.avatarContainer}
            disabled={isUploading}
          >
            {profileImage ? (
              <Avatar.Image size={100} source={{ uri: profileImage }} style={styles.avatar} />
            ) : (
              <Avatar.Text
                size={100}
                label={getNameInitials(user?.first_name || "", user?.last_name || "")}
                style={styles.avatar}
              />
            )}
            <View style={styles.editIconContainer}>
              {isUploading ? (
                <ActivityIndicator size="small" color={colors.palette.neutral100} />
              ) : (
                <Icon icon="edit" size={16} color={colors.palette.neutral100} />
              )}
            </View>
          </TouchableOpacity>

          <Text style={styles.userName}>
            {capitalizeFirstLetter(user?.first_name || "")}{" "}
            {capitalizeFirstLetter(user?.last_name || "")}
          </Text>
          <Text style={styles.userEmail}>{user?.email}</Text>
        </View>

        {/* Menu Sections */}
        <View style={styles.section}>
          {getMenuItems().map((item, index) => (
            <MenuItem
              key={index}
              icon={item.icon}
              title={item.title}
              // subtitle={item.subtitle}
              onPress={item.onPress}
              showChevron={false}
              tintColor={colors.palette.neutral800}
            />
          ))}
        </View>

        <View style={styles.section}>
          <MenuItem
            icon="info"
            title="Apropos de nous"
            onPress={() => navigation.navigate("ContactUs")}
          />
        </View>

        <View style={styles.section}>
          <MenuItem
            icon="logout"
            title="Déconnexion"
            onPress={handleLogout}
            showChevron={false}
            tintColor={colors.palette.angry500}
          />
        </View>

        {/* Add Version Footer */}
        <TouchableOpacity style={styles.versionFooter} onPress={() => setAppInfoModalVisible(true)}>
          <Text style={styles.versionText}>Fedha v{version}</Text>
          <Text style={styles.copyrightText}>© {new Date().getFullYear()} Fedha</Text>
        </TouchableOpacity>
      </Screen>

      <AppInfoModal />
      <ImagePreviewModal />
    </>
  )
}

const styles = StyleSheet.create({
  avatar: {
    backgroundColor: colors.palette.primary200,
  },
  avatarContainer: {
    marginBottom: spacing.md,
    position: "relative",
  },
  closeButton: {
    padding: spacing.xs,
  },
  companyInfo: {
    color: colors.palette.neutral600,
    fontSize: 14,
    lineHeight: 20,
    textAlign: "center",
  },
  copyrightText: {
    color: colors.palette.neutral500,
    fontSize: 12,
    marginTop: spacing.xs,
  },
  divider: {
    backgroundColor: colors.palette.neutral300,
    height: 1,
    marginVertical: spacing.lg,
    width: "100%",
  },
  editIconContainer: {
    backgroundColor: colors.palette.primary500,
    borderColor: colors.palette.neutral100,
    borderRadius: 12,
    borderWidth: 2,
    bottom: 0,
    padding: 6,
    position: "absolute",
    right: 0,
  },
  imagePreviewContainer: {
    backgroundColor: colors.palette.neutral200,
    borderRadius: 10,
    height: Dimensions.get("window").height * 0.4,
    marginVertical: spacing.md,
    overflow: "hidden",
    width: "100%",
  },
  infoLabel: {
    color: colors.palette.neutral600,
    fontSize: 14,
    marginBottom: spacing.xs,
  },
  infoSection: {
    marginBottom: spacing.md,
    width: "100%",
  },
  infoValue: {
    color: colors.palette.neutral900,
    fontSize: 20,
    fontWeight: "500",
  },
  menuItem: {
    alignItems: "center",
    borderBottomColor: colors.palette.neutral200,
    borderBottomWidth: 1,
    flexDirection: "row",
    justifyContent: "space-between",
    padding: spacing.md,
  },
  menuItemLeft: {
    alignItems: "center",
    flexDirection: "row",
  },
  menuItemSubtitle: {
    color: colors.palette.neutral600,
    fontSize: 12,
    marginTop: 2,
  },
  menuItemTextContainer: {
    marginLeft: spacing.sm,
  },
  menuItemTitle: {
    fontSize: 16,
    fontWeight: "500",
  },
  modalActions: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: spacing.sm,
  },
  modalButton: {
    flex: 1,
    marginHorizontal: spacing.xs,
  },
  modalCloseButton: {
    padding: spacing.xs,
    position: "absolute",
    right: spacing.md,
    top: spacing.md,
    zIndex: 1,
  },
  modalContent: {
    backgroundColor: colors.palette.neutral100,
    borderRadius: 20,
    maxHeight: "80%",
    padding: spacing.lg,
    position: "relative",
    width: "90%",
  },
  modalHeader: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: spacing.sm,
  },
  modalLogo: {
    height: 90,
    marginBottom: spacing.lg,
    width: 90,
  },
  modalOverlay: {
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    bottom: 0,
    flex: 1,
    justifyContent: "center",
    left: 0,
    position: "absolute",
    right: 0,
    top: 0,
  },
  modalTitle: {
    color: colors.palette.neutral800,
    fontSize: 18,
    fontWeight: "bold",
  },
  previewImage: {
    height: "100%",
    width: "100%",
  },
  profileSection: {
    alignItems: "center",
    backgroundColor: colors.palette.neutral200,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
    paddingVertical: spacing.xl,
  },
  root: {
    backgroundColor: colors.palette.neutral100,
    flex: 1,
  },
  section: {
    backgroundColor: colors.palette.neutral100,
    borderRadius: 12,
    marginHorizontal: spacing.sm,
    marginTop: spacing.md,
    overflow: "hidden",
  },
  userEmail: {
    color: colors.palette.neutral600,
    fontSize: 16,
  },
  userName: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: spacing.xs,
  },
  versionFooter: {
    alignItems: "center",
    marginTop: spacing.xl,
    paddingVertical: spacing.lg,
  },
  versionText: {
    color: colors.palette.neutral600,
    fontSize: 14,
    fontWeight: "500",
  },
})

import { FC, useState } from "react"
import {
  ViewStyle,
  View,
  TouchableOpacity,
  Image,
  StyleSheet,
  FlatList,
  Modal,
  ScrollView,
} from "react-native"
import { AppStackScreenProps } from "@/navigators"
import { Header, Screen, Text, Icon, Button } from "@/components"
import { colors, spacing } from "@/theme"

interface CouponsScreenProps extends AppStackScreenProps<"Coupons"> {}

// Define the coupon item type
interface CouponItem {
  id: string
  name: string
  logo: any
  description?: string
}

// Define the category type
interface CouponCategory {
  id: string
  title: string
  items: CouponItem[]
}

// Create the data structure for our coupons
const couponCategories: CouponCategory[] = [
  {
    id: "entertainment",
    title: "Entertainment",
    items: [
      {
        id: "appstore",
        name: "App Store & iTunes",
        logo: require(""),
        description: "Achetez des cartes-cadeaux pour l'App Store et iTunes.",
      },
      {
        id: "googleplay",
        name: "Google Play",
        logo: require(""),
        description: "Achetez des codes-cadeaux pour Google Play Store.",
      },
      {
        id: "netflix",
        name: "Netflix",
        logo: require("../../assets/images/logos/netflix.png"),
        description: "Achetez des abonnements et cartes-cadeaux Netflix.",
      },
      {
        id: "primevideo",
        name: "Prime Video",
        logo: require("../../assets/images/logos/primevideo.png"),
        description: "Achetez des abonnements Amazon Prime Video.",
      },
    ],
  },
  {
    id: "gaming",
    title: "Gaming",
    items: [
      {
        id: "easports",
        name: "EA Sports",
        logo: require(""),
        description: "Achetez des points EA et des codes pour les jeux EA Sports.",
      },
      {
        id: "xbox",
        name: "Xbox",
        logo: require(""),
        description: "Achetez des cartes Xbox Live et Game Pass.",
      },
      {
        id: "playstation",
        name: "PlayStation",
        logo: require(""),
        description: "Achetez des cartes PlayStation Store et PS Plus.",
      },
      {
        id: "steam",
        name: "Steam",
        logo: require(""),
        description: "Achetez des cartes-cadeaux Steam pour des jeux PC.",
      },
    ],
  },
  {
    id: "music",
    title: "Music",
    items: [
      {
        id: "spotify",
        name: "Spotify",
        logo: require(""),
        description: "Achetez des abonnements Spotify Premium.",
      },
    ],
  },
]

// Use giftcard icon as fallback

export const CouponsScreen: FC<CouponsScreenProps> = ({ navigation }) => {
  const [selectedCoupon, setSelectedCoupon] = useState<CouponItem | null>(null)
  const [modalVisible, setModalVisible] = useState(false)

  // Handle coupon item press
  const handleCouponPress = (coupon: CouponItem) => {
    setSelectedCoupon(coupon)
    setModalVisible(true)
  }

  // Close the modal
  const handleCloseModal = () => {
    setModalVisible(false)
    setSelectedCoupon(null)
  }

  // Render a coupon item
  const renderCouponItem = ({ item }: { item: CouponItem }) => (
    <TouchableOpacity
      style={styles.couponItem}
      onPress={() => handleCouponPress(item)}
      activeOpacity={0.7}
    >
      <View style={styles.logoContainer}>
        {/* Try to load the image, use fallback icon if it fails */}
        <Image
          source={item.logo}
          style={styles.logo}
          defaultSource={require("../../assets/images/logos/netflix.png")}
        />
      </View>
      <Text style={styles.couponName}>{item.name}</Text>
    </TouchableOpacity>
  )

  // Render a category section
  const renderCategory = ({ item }: { item: CouponCategory }) => (
    <View style={styles.categoryContainer}>
      <Text style={styles.categoryTitle}>{item.title}</Text>
      <FlatList
        data={item.items}
        renderItem={renderCouponItem}
        keyExtractor={(item) => item.id}
        horizontal={false}
        numColumns={2}
        contentContainerStyle={styles.couponGrid}
      />
    </View>
  )

  return (
    <>
      <Header
        leftIcon="backicon"
        onLeftPress={navigation.goBack}
        title="Coupons"
        backgroundColor={colors.palette.neutral100}
      />
      <Screen style={$root} preset="scroll">
        <FlatList
          data={couponCategories}
          renderItem={renderCategory}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.container}
          showsVerticalScrollIndicator={false}
        />
      </Screen>

      {/* Coupon Detail Modal */}
      <Modal
        visible={modalVisible}
        transparent={true}
        animationType="slide"
        onRequestClose={handleCloseModal}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <TouchableOpacity style={styles.closeButton} onPress={handleCloseModal}>
              <Icon icon="x" size={24} color={colors.palette.neutral800} />
            </TouchableOpacity>

            {selectedCoupon && (
              <ScrollView contentContainerStyle={styles.modalScrollContent}>
                <View style={styles.modalLogoContainer}>
                  <Image
                    source={selectedCoupon.logo}
                    style={styles.modalLogo}
                    defaultSource={require("../../assets/images/logos/netflix.png")}
                  />
                </View>

                <Text style={styles.modalTitle}>{selectedCoupon.name}</Text>

                {selectedCoupon.description && (
                  <Text style={styles.modalDescription}>{selectedCoupon.description}</Text>
                )}

                <View style={styles.optionsContainer}>
                  <Text style={styles.optionsTitle}>Options disponibles</Text>

                  <View style={styles.optionItem}>
                    <Text style={styles.optionName}>Carte-cadeau 10$</Text>
                    <Text style={styles.optionPrice}>20.000 FC</Text>
                  </View>

                  <View style={styles.optionItem}>
                    <Text style={styles.optionName}>Carte-cadeau 25$</Text>
                    <Text style={styles.optionPrice}>50.000 FC</Text>
                  </View>

                  <View style={styles.optionItem}>
                    <Text style={styles.optionName}>Carte-cadeau 50$</Text>
                    <Text style={styles.optionPrice}>100.000 FC</Text>
                  </View>
                </View>

                <Button
                  text="Acheter maintenant"
                  preset="filled"
                  style={styles.buyButton}
                  onPress={() => {
                    handleCloseModal()
                    // Add purchase logic here
                  }}
                />
              </ScrollView>
            )}
          </View>
        </View>
      </Modal>
    </>
  )
}

const $root: ViewStyle = {
  flex: 1,
  backgroundColor: colors.palette.neutral200,
}

const styles = StyleSheet.create({
  buyButton: {
    marginTop: spacing.md,
  },
  categoryContainer: {
    marginBottom: spacing.xl,
  },
  categoryTitle: {
    color: colors.palette.neutral800,
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: spacing.sm,
  },
  closeButton: {
    alignSelf: "flex-end",
    padding: spacing.xs,
  },
  container: {
    padding: spacing.md,
  },
  couponGrid: {
    paddingVertical: spacing.xs,
  },
  couponItem: {
    alignItems: "center",
    backgroundColor: colors.palette.neutral100,
    borderRadius: 12,
    elevation: 3,
    flex: 1,
    margin: spacing.xs,
    maxWidth: "48%",
    padding: spacing.md,
    shadowColor: colors.palette.neutral900,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  couponName: {
    color: colors.palette.neutral800,
    fontSize: 14,
    fontWeight: "600",
    textAlign: "center",
  },
  logo: {
    height: 70,
    resizeMode: "contain",
    width: 70,
  },
  logoContainer: {
    alignItems: "center",
    height: 80,
    justifyContent: "center",
    marginBottom: spacing.sm,
    width: 80,
  },
  modalContent: {
    backgroundColor: colors.palette.neutral100,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: "80%",
    padding: spacing.lg,
  },
  modalDescription: {
    color: colors.palette.neutral700,
    fontSize: 16,
    marginBottom: spacing.xl,
    paddingHorizontal: spacing.sm,
    textAlign: "center",
  },
  modalLogo: {
    height: 120,
    resizeMode: "contain",
    width: 120,
  },
  modalLogoContainer: {
    alignItems: "center",
    marginVertical: spacing.lg,
  },
  modalOverlay: {
    backgroundColor: colors.palette.overlay50,
    flex: 1,
    justifyContent: "flex-end",
  },
  modalScrollContent: {
    paddingBottom: spacing.xl,
  },
  modalTitle: {
    color: colors.palette.neutral900,
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: spacing.sm,
    textAlign: "center",
  },
  optionItem: {
    borderBottomColor: colors.palette.neutral300,
    borderBottomWidth: 1,
    flexDirection: "row",
    justifyContent: "space-between",
    paddingVertical: spacing.sm,
  },
  optionName: {
    color: colors.palette.neutral800,
    fontSize: 16,
  },
  optionPrice: {
    color: colors.palette.primary500,
    fontSize: 16,
    fontWeight: "bold",
  },
  optionsContainer: {
    backgroundColor: colors.palette.neutral200,
    borderRadius: 12,
    marginBottom: spacing.xl,
    padding: spacing.md,
  },
  optionsTitle: {
    color: colors.palette.neutral800,
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: spacing.md,
  },
})

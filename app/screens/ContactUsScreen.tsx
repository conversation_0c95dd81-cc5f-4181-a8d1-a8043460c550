/* eslint-disable react-native/sort-styles */
/* eslint-disable react-native/no-unused-styles */
/* eslint-disable react-native/no-color-literals */
/* eslint-disable import/no-unresolved */
import { FC, useEffect, useState } from "react"
import { View, StyleSheet, TouchableOpacity, Linking, Image, ScrollView } from "react-native"
import { AppStackScreenProps } from "@/navigators"
import { Header, Screen, Text, Icon, IconTypes } from "@/components"
import { colors, spacing } from "@/theme"
import { BlurView } from "expo-blur"
import { LinearGradient } from "expo-linear-gradient"
import { useStores } from "@/store"

interface ContactUsScreenProps extends AppStackScreenProps<"ContactUs"> {}

interface SocialLinkProps {
  icon: IconTypes
  url: string
}

interface FedhaBusinessData {
  adresse: string | null
  city: string
  country: string
  description: string | null
  emails: string[]
  image_banner: string | null
  image_logo: string | null
  name: string
  registration_number: string | null
  socials: Array<{ social: string; username: string }>
  telephones: string[]
  website: string | null
}

const SocialLink = ({ icon, url }: SocialLinkProps) => (
  <TouchableOpacity style={styles.socialLink} onPress={() => Linking.openURL(url)}>
    <Icon icon={icon} size={24} color={colors.palette.primary500} />
  </TouchableOpacity>
)

const ContactCard = ({
  icon,
  // title,
  content,
}: {
  icon: string
  title: string
  content: string
}) => (
  <BlurView intensity={30} tint="light" style={styles.contactCard}>
    <Icon icon={icon as IconTypes} size={28} color={colors.palette.primary500} />
    <Text style={styles.cardContent}>{content}</Text>
  </BlurView>
)

export const ContactUsScreen: FC<ContactUsScreenProps> = ({ navigation }) => {
  const {
    appsettings: { fetchFedhaDetails, fedhaBusinessAccount },
  } = useStores()

  const [fedha, setFedha] = useState<FedhaBusinessData | null>(null)

  useEffect(() => {
    fetchFedhaDetails()
  }, [fetchFedhaDetails])

  // Set Fedha business account details when available
  useEffect(() => {
    if (fedhaBusinessAccount) {
      // Handle the API response structure: {"data": {...}}
      const fedhaData =
        typeof fedhaBusinessAccount === "object" && "data" in fedhaBusinessAccount
          ? fedhaBusinessAccount.data
          : fedhaBusinessAccount
      setFedha(fedhaData as FedhaBusinessData)
    }
  }, [fedhaBusinessAccount])
  return (
    <>
      <Header
        title="A propos de nous"
        leftIcon="backicon"
        onLeftPress={navigation.goBack}
        backgroundColor="transparent"
      />
      <Screen
        preset="scroll"
        // safeAreaEdges={["top"]}
        statusBarStyle="dark"
        contentContainerStyle={styles.screenContainer}
      >
        <LinearGradient
          colors={["rgba(13, 81, 82, 0.1)", "rgba(13, 81, 82, 0.05)"]}
          style={styles.gradient}
        >
          <ScrollView showsVerticalScrollIndicator={false}>
            {/* Logo Section */}
            <View style={styles.logoContainer}>
              <Image
                source={
                  fedha?.image_logo
                    ? { uri: fedha.image_logo }
                    : require("../../assets/images/logo.png")
                }
                style={styles.logo}
                resizeMode="contain"
              />
              {/* <Text style={styles.tagline}>Votre partenaire financier de confiance</Text> */}
              <Text style={styles.companyInfo}>
                {fedha?.description
                  ? fedha.description
                  : "Fedha, c’est la réponse à l’exclusion financière en RDC. Nous permettons à chaque Congolais d’envoyer, recevoir et gérer son argent facilement, en toute sécurité, où qu’il soit. Une solution simple, rapide et accessible pour reprendre le contrôle de ses finances."}
              </Text>
            </View>

            {/* Contact Cards */}
            <View style={styles.cardsContainer}>
              <ContactCard
                icon="email"
                title="Email"
                content={
                  fedha?.emails && fedha.emails.length > 0 ? fedha.emails[0] : "<EMAIL>"
                }
              />
              <ContactCard
                icon="whatsapp"
                title="Téléphone"
                content={
                  fedha?.telephones && fedha.telephones.length > 0
                    ? fedha.telephones[0]
                    : "+243 999 999 999"
                }
              />
              {/* {fedha?.city && fedha?.country && (
                <ContactCard
                  icon="mappin"
                  title="Adresse"
                  content={`${fedha.city}, ${fedha.country}`}
                />
              )} */}
            </View>

            {/* Social Media Links */}
            <View style={styles.socialContainer}>
              <Text style={styles.socialTitle}>Suivez-nous</Text>
              <View style={styles.socialLinks}>
                {fedha?.socials && fedha.socials.length > 0 ? (
                  // Display social links from API
                  fedha.socials.map((social, index) => {
                    // Get icon based on social platform
                    let icon: IconTypes = "caretRight" // Default icon

                    // Check if the social platform has a matching icon
                    const platform = social.social.toLowerCase()
                    if (platform === "facebook") icon = "facebook"
                    else if (platform === "twitter") icon = "twitter"
                    else if (platform === "instagram") icon = "instagram"
                    else if (platform === "linkedin") icon = "linkedin"

                    return <SocialLink key={index} icon={icon} url={social.username} />
                  })
                ) : (
                  // Default social links
                  <>
                    <SocialLink icon="facebook" url="https://www.facebook.com/fedhapay/" />
                    <SocialLink icon="twitter" url="https://x.com/fedhapay" />
                    <SocialLink icon="instagram" url="https://www.instagram.com/fedhalink/" />
                    <SocialLink icon="linkedin" url="https://www.linkedin.com/company/fedhapay/" />
                  </>
                )}
              </View>
            </View>

            {/* Professional Footer */}
            <View style={styles.footer}>
              <View style={styles.footerSection}>
                <Text style={styles.footerText}>
                  {fedha?.registration_number ? `RCCM: ${fedha.registration_number}\n` : ""}
                  {fedha?.description ? `${fedha.description}\n` : ""}
                </Text>
              </View>

              {/* {fedha?.city && fedha?.country && (
                <View style={styles.footerSection}>
                  <Text style={styles.footerTitle}>Adresse</Text>
                  <Text style={styles.footerText}>
                    {fedha?.adresse ? `${fedha.adresse}\n` : ""}
                    {`${fedha.city}, ${fedha.country}`}
                  </Text>
                </View>
              )} */}

              <View style={styles.footerDivider} />

              <Text style={styles.copyrightText}>
                Fedha © {new Date().getFullYear()} | Tous droits réservés
              </Text>
            </View>
          </ScrollView>
        </LinearGradient>
      </Screen>
    </>
  )
}

const styles = StyleSheet.create({
  businessName: {
    color: colors.palette.neutral800,
    fontSize: 18,
    fontWeight: "bold",
    marginTop: spacing.xs,
  },
  cardContent: {
    color: colors.palette.neutral600,
    fontSize: 16,
  },
  cardsContainer: {
    gap: spacing.md,
    paddingHorizontal: spacing.lg,
  },
  contactCard: {
    alignItems: "center",
    backgroundColor: "rgba(255, 255, 255, 0.7)",
    borderColor: "rgba(255, 255, 255, 0.3)",
    borderRadius: 16,
    borderWidth: 1,
    flexDirection: "row",
    gap: spacing.sm,
    padding: spacing.md,
  },
  copyrightText: {
    color: colors.palette.neutral500,
    fontSize: 12,
    textAlign: "center",
  },
  footer: {
    // backgroundColor: "rgba(255, 255, 255, 0.7)",
    borderTopColor: "rgba(13, 81, 82, 0.1)",
    borderTopWidth: 1,
    marginTop: spacing.xl,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.lg,
  },
  footerDivider: {
    backgroundColor: "rgba(255, 255, 255, 0.7)",
    height: 1,
    marginVertical: spacing.md,
  },
  footerSection: {
    marginBottom: spacing.md,
  },
  footerText: {
    color: colors.palette.neutral600,
    fontSize: 14,
    lineHeight: 20,
  },
  footerTitle: {
    color: colors.palette.neutral800,
    fontSize: 16,
    fontWeight: "bold",
    marginBottom: spacing.xs,
  },
  gradient: {
    flex: 1,
  },
  companyInfo: {
    color: colors.palette.neutral600,
    fontSize: 14,
    lineHeight: 20,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm,
    textAlign: "center",
  },
  logo: {
    // height: 210,
    width: 210,
  },
  logoContainer: {
    alignItems: "center",
    // paddingVertical: spacing.xl,
  },
  screenContainer: {
    flexGrow: 1,
  },
  socialContainer: {
    alignItems: "center",
    marginTop: spacing.xl,
  },
  socialLink: {
    alignItems: "center",
    backgroundColor: "rgba(255, 255, 255, 0.7)",
    borderRadius: 25,
    height: 50,
    justifyContent: "center",
    padding: spacing.xs,
    width: 50,
  },
  socialLinks: {
    flexDirection: "row",
    gap: spacing.lg,
    justifyContent: "center",
  },
  socialTitle: {
    color: colors.palette.neutral800,
    fontSize: 16,
    marginBottom: spacing.sm,
  },
  tagline: {
    color: colors.palette.neutral700,
    fontSize: 16,
    fontStyle: "italic",
    marginTop: spacing.sm,
  },
})

/* eslint-disable no-restricted-imports */
/* eslint-disable @typescript-eslint/no-unused-vars */
import { FC, useState, useRef } from "react"
import {
  ViewStyle,
  View,
  ScrollView,
  Dimensions,
  ImageSourcePropType,
  TextStyle,
  ImageStyle,
} from "react-native"
import { AppStackScreenProps } from "@/navigators"
import { BuyAirtime, BuyElectricity, Header, Screen, Text, Button } from "@/components"
import { colors, spacing, typography } from "@/theme"
import { ServicesToBuyList } from "@/utils/Menus"
import React from "react"
import { useStores } from "@/store"
import { Image } from "react-native"
import { LinearGradient } from "expo-linear-gradient"

interface BuyScreenProps extends AppStackScreenProps<"Buy"> {}

// Promotional banners data
const promotionalBanners = [
  {
    id: 1,
    title: "Économisez 20%",
    subtitle: "Sur tous les services électriques",
    image: require("../../assets/images/banners/Snel.png"),
    gradient: [colors.palette.primary600, colors.palette.primary400] as const,
  },
  {
    id: 2,
    title: "Nouveau Service",
    subtitle: "Forfaits & Minutes bientôt disponible",
    image: require("../../assets/images/banners/netWORKmUTL.png"),
    gradient: [colors.palette.secondary500, colors.palette.secondary300],
  },
  {
    id: 3,
    title: "Paiement Rapide",
    subtitle: "Rechargez en moins de 30 secondes",
    image: require("../../assets/images/banners/Snel.png"),
    gradient: [colors.palette.terciary200, colors.palette.terciary100],
  },
]

// Promotional Banner Component
const PromotionalBanner: FC<{
  title: string
  subtitle: string
  image: ImageSourcePropType
  gradient: readonly [string, string, ...string[]]
}> = ({ title, subtitle, image, gradient }) => {
  return (
    <View style={$bannerContainer}>
      <LinearGradient colors={gradient} style={$bannerGradient}>
        <View style={$bannerContent}>
          <View style={$bannerTextContainer}>
            <Text style={$bannerTitle}>{title}</Text>
            <Text style={$bannerSubtitle}>{subtitle}</Text>
          </View>
          <Image source={image} style={$bannerImage} />
        </View>
      </LinearGradient>
    </View>
  )
}

// Modern Service Card Component
const ServiceCard: FC<{
  title: string
  description: string
  image: ImageSourcePropType
  event: "active" | "comingSoon" | "inactive" | any
  onPress: () => void
}> = ({ title, description, image, event, onPress }) => {
  const getStatusInfo = () => {
    switch (event) {
      case "active":
        return {
          showButton: true,
          buttonText: "Acheter maintenant",
          statusText: null,
          buttonColor: colors.palette.primary600,
          statusColor: null,
        }
      case "comingSoon":
        return {
          showButton: false,
          buttonText: null,
          statusText: "Bientôt disponible",
          buttonColor: null,
          statusColor: colors.palette.secondary500,
        }
      case "inactive":
        return {
          showButton: false,
          buttonText: null,
          statusText: "Temporairement indisponible",
          buttonColor: null,
          statusColor: colors.palette.neutral400,
        }
      default:
        return {
          showButton: false,
          buttonText: null,
          statusText: "Service indisponible",
          buttonColor: null,
          statusColor: colors.palette.angry500,
        }
    }
  }

  const statusInfo = getStatusInfo()

  return (
    <View style={$serviceCard}>
      <View style={$serviceImageContainer}>
        <Image source={image} style={$serviceImage} />
        {event !== "active" && (
          <View style={$serviceOverlay}>
            <Text style={$overlayText}>{statusInfo.statusText}</Text>
          </View>
        )}
      </View>

      <View style={$serviceContent}>
        <Text style={$serviceTitle}>{title}</Text>
        <Text style={$serviceDescription}>{description}</Text>

        {statusInfo.showButton ? (
          <Button
            text={statusInfo.buttonText || ""}
            style={[
              $serviceButton,
              statusInfo.buttonColor ? { backgroundColor: statusInfo.buttonColor } : {},
            ]}
            textStyle={$serviceButtonText}
            onPress={onPress}
          />
        ) : (
          <View
            style={[
              $serviceStatus,
              statusInfo.statusColor ? { backgroundColor: `${statusInfo.statusColor}20` } : {},
            ]}
          >
            <Text
              style={[
                $serviceStatusText,
                statusInfo.statusColor ? { color: statusInfo.statusColor } : {},
              ]}
            >
              {statusInfo.statusText}
            </Text>
          </View>
        )}
      </View>
    </View>
  )
}

export const BuyScreen: FC<BuyScreenProps> = ({ navigation }) => {
  const [selectedService, setSelectedService] = useState<string | null>(null)
  const [currentBannerIndex, setCurrentBannerIndex] = useState(0)
  const scrollViewRef = useRef<ScrollView>(null)
  const {
    services: { Services },
  } = useStores()

  const handleBuyPress = (itemName: string) => {
    setSelectedService(itemName)
    console.log("Selected service:", itemName)
  }

  const handleBackToServices = () => {
    setSelectedService(null)
  }

  // Auto-scroll banners
  React.useEffect(() => {
    const interval = setInterval(() => {
      setCurrentBannerIndex((prevIndex) =>
        prevIndex === promotionalBanners.length - 1 ? 0 : prevIndex + 1,
      )
    }, 4000) // Change banner every 4 seconds

    return () => clearInterval(interval)
  }, [])

  // Scroll to current banner
  React.useEffect(() => {
    if (scrollViewRef.current) {
      const { width } = Dimensions.get("window")
      scrollViewRef.current.scrollTo({
        x: currentBannerIndex * (width - 32), // Account for padding
        animated: true,
      })
    }
  }, [currentBannerIndex])

  const renderMarketplace = () => {
    return (
      <View style={$marketplaceContainer}>
        {/* Promotional Banners Section */}
        <View style={$bannersSection}>
          <ScrollView
            ref={scrollViewRef}
            horizontal
            pagingEnabled
            showsHorizontalScrollIndicator={false}
            style={$bannersScrollView}
            onMomentumScrollEnd={(event) => {
              const { width } = Dimensions.get("window")
              const newIndex = Math.round(event.nativeEvent.contentOffset.x / (width - 32))
              setCurrentBannerIndex(newIndex)
            }}
          >
            {promotionalBanners.map((banner) => (
              <PromotionalBanner
                key={banner.id}
                title={banner.title}
                subtitle={banner.subtitle}
                image={banner.image}
                gradient={banner.gradient}
              />
            ))}
          </ScrollView>

          {/* Banner Indicators */}
          <View style={$bannerIndicators}>
            {promotionalBanners.map((_, index) => (
              <View
                key={index}
                style={[
                  $bannerIndicator,
                  {
                    backgroundColor:
                      index === currentBannerIndex
                        ? colors.palette.primary600
                        : colors.palette.neutral300,
                  },
                ]}
              />
            ))}
          </View>
        </View>

        {/* Services Section */}
        <View style={$servicesSection}>
          <Text style={$sectionTitle}>Nos Services</Text>
          <Text style={$sectionSubtitle}>Découvrez tous nos services disponibles</Text>

          <View style={$servicesGrid}>
            {ServicesToBuyList.map((service, index) => (
              <ServiceCard
                key={index}
                title={service.serviceName}
                description={service.serviceDescription}
                image={service.serviceBannert}
                event={service.event}
                onPress={() => handleBuyPress(service.serviceName)}
              />
            ))}
          </View>
        </View>
      </View>
    )
  }

  const renderSelectedServiceComponent = () => {
    switch (selectedService) {
      case "Forfaits & Minutes":
        return <BuyAirtime />
      case "SNEL Cash Power":
        // Replace with your SNEL component
        return <BuyElectricity navigation={navigation} />
      case "REGIDESO":
        // Replace with your REGIDESO component
        return <BuyAirtime />
      default:
        return null
    }
  }

  return (
    <>
      <Header
        leftIcon="backicon"
        onLeftPress={selectedService ? handleBackToServices : navigation.goBack}
        title={selectedService ? "Détails d'achat" : "Achetez avec Fedha"}
        backgroundColor={colors.palette.neutral100}
      />
      <Screen style={$root} preset="scroll" safeAreaEdges={["bottom"]} statusBarStyle="dark">
        {selectedService ? renderSelectedServiceComponent() : renderMarketplace()}
      </Screen>
    </>
  )
}

const $root: ViewStyle = {
  flex: 1,
  backgroundColor: colors.palette.neutral100,
}

// Marketplace Container
const $marketplaceContainer: ViewStyle = {
  flex: 1,
}

// Banner Styles
const $bannersSection: ViewStyle = {
  marginBottom: spacing.xl,
}

const $bannersScrollView: ViewStyle = {
  height: 180,
}

const $bannerContainer: ViewStyle = {
  width: Dimensions.get("window").width - 32,
  height: 160,
  marginHorizontal: 16,
  borderRadius: 16,
  overflow: "hidden",
  elevation: 4,
  shadowColor: colors.palette.neutral900,
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
  shadowRadius: 8,
}

const $bannerGradient: ViewStyle = {
  flex: 1,
  justifyContent: "center",
}

const $bannerContent: ViewStyle = {
  flex: 1,
  flexDirection: "row",
  alignItems: "center",
  paddingHorizontal: spacing.lg,
}

const $bannerTextContainer: ViewStyle = {
  flex: 1,
}

const $bannerTitle: TextStyle = {
  fontFamily: typography.primary.bold,
  fontSize: 20,
  fontWeight: "700",
  color: colors.palette.neutral100,
  marginBottom: 4,
}

const $bannerSubtitle: TextStyle = {
  fontFamily: typography.primary.normal,
  fontSize: 14,
  color: colors.palette.neutral100,
  opacity: 0.9,
}

const $bannerImage: ImageStyle = {
  width: 80,
  height: 80,
  borderRadius: 8,
}

const $bannerIndicators: ViewStyle = {
  flexDirection: "row",
  justifyContent: "center",
  alignItems: "center",
  marginTop: spacing.sm,
  gap: spacing.xs,
}

const $bannerIndicator: ViewStyle = {
  width: 8,
  height: 8,
  borderRadius: 4,
}

// Services Section Styles
const $servicesSection: ViewStyle = {
  paddingHorizontal: spacing.md,
  paddingBottom: spacing.xl,
}

const $sectionTitle: TextStyle = {
  fontFamily: typography.primary.bold,
  fontSize: 24,
  fontWeight: "700",
  color: colors.palette.neutral900,
  marginBottom: spacing.xs,
}

const $sectionSubtitle: TextStyle = {
  fontFamily: typography.primary.normal,
  fontSize: 16,
  color: colors.palette.neutral600,
  marginBottom: spacing.lg,
}

const $servicesGrid: ViewStyle = {
  gap: spacing.md,
}

// Service Card Styles
const $serviceCard: ViewStyle = {
  backgroundColor: colors.palette.neutral100,
  borderRadius: 16,
  overflow: "hidden",
  elevation: 3,
  shadowColor: colors.palette.neutral900,
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.08,
  shadowRadius: 8,
  marginBottom: spacing.md,
}

const $serviceImageContainer: ViewStyle = {
  position: "relative",
  height: 140,
}

const $serviceImage: ImageStyle = {
  width: "100%",
  height: "100%",
  resizeMode: "cover",
}

const $serviceOverlay: ViewStyle = {
  position: "absolute",
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  backgroundColor: "rgba(0, 0, 0, 0.6)",
  justifyContent: "center",
  alignItems: "center",
}

const $overlayText: TextStyle = {
  fontFamily: typography.primary.medium,
  fontSize: 16,
  fontWeight: "600",
  color: colors.palette.neutral100,
  textAlign: "center",
}

const $serviceContent: ViewStyle = {
  padding: spacing.md,
}

const $serviceTitle: TextStyle = {
  fontFamily: typography.primary.bold,
  fontSize: 18,
  fontWeight: "700",
  color: colors.palette.neutral900,
  marginBottom: spacing.xs,
}

const $serviceDescription: TextStyle = {
  fontFamily: typography.primary.normal,
  fontSize: 14,
  color: colors.palette.neutral600,
  lineHeight: 20,
  marginBottom: spacing.md,
}

const $serviceButton: ViewStyle = {
  backgroundColor: colors.palette.primary600,
  borderRadius: 12,
  paddingVertical: spacing.sm,
  paddingHorizontal: spacing.md,
  alignItems: "center",
}

const $serviceButtonText: TextStyle = {
  fontFamily: typography.primary.medium,
  fontSize: 14,
  fontWeight: "600",
  color: colors.palette.neutral100,
}

const $serviceStatus: ViewStyle = {
  borderRadius: 12,
  paddingVertical: spacing.sm,
  paddingHorizontal: spacing.md,
  alignItems: "center",
  borderWidth: 1,
  borderColor: colors.palette.neutral300,
}

const $serviceStatusText: TextStyle = {
  fontFamily: typography.primary.medium,
  fontSize: 14,
  fontWeight: "600",
}

/* eslint-disable no-restricted-imports */
/* eslint-disable @typescript-eslint/no-unused-vars */
import { FC, useState, useRef } from "react"
import {
  ViewStyle,
  View,
  ScrollView,
  Dimensions,
  ImageSourcePropType,
  TextStyle,
  ImageStyle,
} from "react-native"
import { AppStackScreenProps } from "@/navigators"
import { BuyAirtime, BuyElectricity, Header, Screen, Text, Button, ServiceCard } from "@/components"
import { colors, spacing, typography } from "@/theme"
import { ServicesToBuyList } from "@/utils/Menus"
import React from "react"
// import { useStores } from "@/store" // Removed unused import
import { Image } from "react-native"
import { LinearGradient } from "expo-linear-gradient"
import { useStores } from "@/store"

interface BuyScreenProps extends AppStackScreenProps<"Buy"> {}

// Electricity services data
const electricityItems = [
  {
    id: "1",
    title: "SOCODEE",
    icon: "lightbulb",
    buttonText: "Acheter",
  },
  {
    id: "2",
    title: "SNEL",
    icon: "lightbulb",
    buttonText: "Acheter",
  },
]

// Promotional banners data
const promotionalBanners = [
  {
    id: 1,
    title: "Économisez 20%",
    subtitle: "Sur tous les services électriques",
    image: require("../../assets/images/banners/Snel.png"),
    gradient: [colors.palette.primary600, colors.palette.primary400] as const,
  },
]

// Promotional Banner Component
const PromotionalBanner: FC<{
  title: string
  subtitle: string
  image: ImageSourcePropType
  gradient: readonly [string, string, ...string[]]
  onGetIt: () => void
}> = ({ title, subtitle, image, gradient, onGetIt }) => {
  return (
    <View style={$bannerContainer}>
      <LinearGradient colors={gradient} style={$bannerGradient}>
        <View style={$bannerContent}>
          <View style={$bannerTextContainer}>
            <Text style={$bannerTitle}>{title}</Text>
            <Text style={$bannerSubtitle}>{subtitle}</Text>
            <Button
              text="Get it"
              style={$bannerButton}
              textStyle={$bannerButtonText}
              onPress={onGetIt}
            />
          </View>
          <Image source={image} style={$bannerImage} />
        </View>
      </LinearGradient>
    </View>
  )
}

// SingleServiceCard component moved to ServiceCard.tsx as "single" layout

export const BuyScreen: FC<BuyScreenProps> = ({ navigation }) => {
  const [selectedService, setSelectedService] = useState<string | null>(null)
  const [currentBannerIndex, setCurrentBannerIndex] = useState(0)
  const scrollViewRef = useRef<ScrollView>(null)
  const {
    services: { Services },
  } = useStores()

  const handleBuyPress = (itemName: string) => {
    setSelectedService(itemName)
    console.log("🛒 Service selected for purchase:", itemName)
    console.log("🔄 Navigating to service topup screen...")

    // Add specific service handling
    switch (itemName.toLowerCase()) {
      case "snel":
        console.log("⚡ Opening SNEL electricity topup...")
        break
      case "airtel":
        console.log("📱 Opening Airtel mobile topup...")
        break
      case "orange":
        console.log("🍊 Opening Orange mobile topup...")
        break
      case "vodacom":
        console.log("📶 Opening Vodacom mobile topup...")
        break
      default:
        console.log("🎯 Opening generic service topup for:", itemName)
    }
  }

  // Handle electricity item press
  const handleElectricityItemPress = (item: any) => {
    console.log("⚡ Electricity item selected:", item.title)
    console.log("🔄 Item data:", item)

    // Handle specific electricity services
    switch (item.id) {
      case "1":
        console.log("💡 Opening Prepaid electricity service...")
        setSelectedService("Prepaid electricity")
        break
      case "2":
        console.log("⚡ Opening SNEL service...")
        setSelectedService("SNEL Cash Power")
        break
      default:
        console.log("🎯 Unknown electricity service:", item.title)
    }
  }

  const handleBackToServices = () => {
    setSelectedService(null)
  }

  // Handle banner "Get it" button press
  const handleBannerGetIt = (bannerId: number) => {
    const banner = promotionalBanners.find((b) => b.id === bannerId)
    console.log(`🎯 Banner "Get it" pressed:`, banner?.title)

    // Add specific logic for each banner
    switch (bannerId) {
      case 1:
        console.log("🔌 Navigating to SNEL electricity service...")
        // Navigate to SNEL service or show discount
        break
      case 2:
        console.log("📱 Showing coming soon for network services...")
        // Show coming soon message
        break
      case 3:
        console.log("⚡ Highlighting fast payment feature...")
        // Highlight payment speed
        break
      default:
        console.log("🎯 Unknown banner action")
    }
  }

  // Auto-scroll banners (slower with longer wait)
  React.useEffect(() => {
    const interval = setInterval(() => {
      setCurrentBannerIndex((prevIndex) =>
        prevIndex === promotionalBanners.length - 1 ? 0 : prevIndex + 1,
      )
    }, 8000) // Change banner every 8 seconds (slower)

    return () => clearInterval(interval)
  }, [])

  // Scroll to current banner
  React.useEffect(() => {
    if (scrollViewRef.current) {
      const { width } = Dimensions.get("window")
      scrollViewRef.current.scrollTo({
        x: currentBannerIndex * (width - 32), // Account for padding
        animated: true,
      })
    }
  }, [currentBannerIndex])

  const renderMarketplace = () => {
    return (
      <View style={$marketplaceContainer}>
        {/* Promotional Banners Section */}
        <View style={$bannersSection}>
          <ScrollView
            ref={scrollViewRef}
            horizontal
            pagingEnabled
            showsHorizontalScrollIndicator={false}
            style={$bannersScrollView}
            onMomentumScrollEnd={(event) => {
              const { width } = Dimensions.get("window")
              const newIndex = Math.round(event.nativeEvent.contentOffset.x / (width - 32))
              setCurrentBannerIndex(newIndex)
            }}
          >
            {promotionalBanners.map((banner) => (
              <PromotionalBanner
                key={banner.id}
                title={banner.title}
                subtitle={banner.subtitle}
                image={banner.image}
                gradient={banner.gradient}
                onGetIt={() => handleBannerGetIt(banner.id)}
              />
            ))}
          </ScrollView>

          {/* Banner Indicators */}
          {/* <View style={$bannerIndicators}>
            {promotionalBanners.map((_, index) => (
              <View
                key={index}
                style={[
                  $bannerIndicator,
                  {
                    backgroundColor:
                      index === currentBannerIndex
                        ? colors.palette.primary600
                        : colors.palette.neutral300,
                  },
                ]}
              />
            ))}
          </View> */}
        </View>

        {/* Services Section */}
        <View style={$servicesSection}>
          {/* <Text style={$sectionTitle}>Nos Services</Text>
          <Text style={$sectionSubtitle}>Découvrez tous nos services disponibles</Text> */}

          {/* Electricity Services - Multi Layout */}
          <ServiceCard
            layout="multi"
            title="Services Électriques"
            description="Rechargez votre compteur électrique facilement"
            items={electricityItems}
            onItemPress={handleElectricityItemPress}
            style={$electricityCard}
          />

          {/* Other Services - Single Layout */}
          <View style={$servicesGrid}>
            {ServicesToBuyList.map((service, index) => (
              <ServiceCard
                key={index}
                layout="single"
                title={service.serviceName}
                description={service.serviceDescription}
                image={service.serviceBannert}
                event={service.event as "active" | "comingSoon" | "inactive"}
                onPress={() => handleBuyPress(service.serviceName)}
              />
            ))}
          </View>
        </View>
      </View>
    )
  }

  const renderSelectedServiceComponent = () => {
    switch (selectedService) {
      case "Forfaits & Minutes":
        return <BuyAirtime />
      case "SNEL Cash Power":
        // Replace with your SNEL component
        return <BuyElectricity navigation={navigation} />
      case "REGIDESO":
        // Replace with your REGIDESO component
        return <BuyAirtime />
      default:
        return null
    }
  }

  return (
    <>
      <Header
        leftIcon="backicon"
        onLeftPress={selectedService ? handleBackToServices : navigation.goBack}
        title={selectedService ? "Détails d'achat" : "Achetez avec Fedha"}
        backgroundColor={colors.palette.neutral100}
      />
      <Screen style={$root} preset="scroll" safeAreaEdges={["bottom"]} statusBarStyle="dark">
        {selectedService ? renderSelectedServiceComponent() : renderMarketplace()}
      </Screen>
    </>
  )
}

const $root: ViewStyle = {
  flex: 1,
  backgroundColor: colors.palette.neutral100,
}

// Marketplace Container
const $marketplaceContainer: ViewStyle = {
  flex: 1,
}

// Banner Styles
const $bannersSection: ViewStyle = {
  marginBottom: spacing.xl,
}

const $bannersScrollView: ViewStyle = {
  height: 200, // Made bigger
}

const $bannerContainer: ViewStyle = {
  width: Dimensions.get("window").width - 33,
  height: 200, // Made bigger
  marginHorizontal: 16,
  borderRadius: 16,
  overflow: "hidden",
  elevation: 4,
  shadowColor: colors.palette.neutral900,
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
  shadowRadius: 8,
}

const $bannerGradient: ViewStyle = {
  flex: 1,
  justifyContent: "center",
}

const $bannerContent: ViewStyle = {
  flex: 1,
  flexDirection: "row",
  alignItems: "center",
  paddingHorizontal: spacing.lg,
}

const $bannerTextContainer: ViewStyle = {
  flex: 1,
}

const $bannerTitle: TextStyle = {
  fontFamily: typography.primary.bold,
  fontSize: 20,
  fontWeight: "700",
  color: colors.palette.neutral100,
  marginBottom: 4,
}

const $bannerSubtitle: TextStyle = {
  fontFamily: typography.primary.normal,
  fontSize: 14,
  color: colors.palette.neutral100,
  opacity: 0.9,
}

const $bannerImage: ImageStyle = {
  width: 90, // Made bigger
  height: 90, // Made bigger
  borderRadius: 8,
}

const $bannerButton: ViewStyle = {
  backgroundColor: "rgba(255, 255, 255, 0.2)",
  borderRadius: 20,
  paddingVertical: spacing.xs,
  paddingHorizontal: spacing.md,
  marginTop: spacing.sm,
  alignSelf: "flex-start",
  borderWidth: 1,
  borderColor: "rgba(255, 255, 255, 0.3)",
}

const $bannerButtonText: TextStyle = {
  fontFamily: typography.primary.medium,
  fontSize: 12,
  fontWeight: "600",
  color: colors.palette.neutral100,
}

const $bannerIndicators: ViewStyle = {
  flexDirection: "row",
  justifyContent: "center",
  alignItems: "center",
  marginTop: spacing.sm,
  gap: spacing.xs,
}

const $bannerIndicator: ViewStyle = {
  width: 8,
  height: 8,
  borderRadius: 4,
}

// Services Section Styles
const $servicesSection: ViewStyle = {
  paddingHorizontal: spacing.md,
  paddingBottom: spacing.xl,
}

const $sectionTitle: TextStyle = {
  fontFamily: typography.primary.bold,
  fontSize: 24,
  fontWeight: "700",
  color: colors.palette.neutral900,
  marginBottom: spacing.xs,
}

const $sectionSubtitle: TextStyle = {
  fontFamily: typography.primary.normal,
  fontSize: 16,
  color: colors.palette.neutral600,
  marginBottom: spacing.lg,
}

const $servicesGrid: ViewStyle = {
  gap: spacing.md,
}

// Electricity Card Style
const $electricityCard: ViewStyle = {
  marginBottom: spacing.lg,
}

// Service Card Styles moved to ServiceCard.tsx component

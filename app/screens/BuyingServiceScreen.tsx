import { FC } from "react"
import { ViewStyle } from "react-native"
import { AppStackScreenProps } from "@/navigators"
import { Screen, Text } from "@/components"
// import { useNavigation } from "@react-navigation/native"

interface BuyingServiceScreenProps extends AppStackScreenProps<"BuyingService"> {}


export const BuyingServiceScreen: FC<BuyingServiceScreenProps> = () => {

  // Pull in navigation via hook
  // const navigation = useNavigation()
  return (
    <Screen style={$root} preset="scroll">
      <Text text="buyingService" />
    </Screen>
  )

}

const $root: ViewStyle = {
  flex: 1,
}

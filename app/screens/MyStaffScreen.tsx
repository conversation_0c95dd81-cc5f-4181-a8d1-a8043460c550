/* eslint-disable react-native/no-inline-styles */
/* eslint-disable react-native/sort-styles */
/* eslint-disable react-native/no-color-literals */
import { FC, useEffect, useState, useCallback, useRef, Key } from "react"
import {
  View,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Modal,
  Image,
  ActivityIndicator,
  Alert,
} from "react-native"
import { AppStackScreenProps } from "@/navigators"
import { Screen, Text, Icon, Header, Button, TextField } from "@/components"
import { colors, spacing } from "@/theme"
import { useStores } from "@/store"
import { StaffMember } from "@/store/StaffStore"
import { formatDistanceToNow } from "date-fns"
import React from "react"

interface MyStaffScreenProps extends AppStackScreenProps<"MyStaff"> {}

export const MyStaffScreen: FC<MyStaffScreenProps> = ({ navigation }) => {
  const { staff, business } = useStores()
  const [searchQuery, setSearchQuery] = useState("")
  const [filteredStaff, setFilteredStaff] = useState<any[]>([])
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [isConfirmModalVisible, setIsConfirmModalVisible] = useState(false)
  const [selectedStaffId, setSelectedStaffId] = useState<string | null>(null)

  // Get the main business
  const mainBusiness = business.businesses?.find((b) => b.is_main === true) || null
  const businessId = mainBusiness?.business_no || ""

  // Use a ref to track if initial fetch has been done
  const initialFetchDone = useRef(false)

  // Fetch staff data only once when component mounts
  useEffect(() => {
    if (businessId && !initialFetchDone.current) {
      initialFetchDone.current = true
      // Clear any existing staff data and fetch fresh data
      staff.fetchPersonnel(businessId)
    }
  }, [businessId, staff])

  // For debugging only
  useEffect(() => {
    if (__DEV__) {
      console.log("Staff data:", staff.staff.length, "members")
    }
  }, [staff.staff])
  // Filter staff data when search query changes
  useEffect(() => {
    if (staff.staff && staff.staff.length > 0) {
      if (searchQuery) {
        const filtered = staff.staff.filter((member) => {
          const query = searchQuery.toLowerCase()
          return (
            member.name.toLowerCase().includes(query) ||
            member.email.toLowerCase().includes(query) ||
            member.staffId.toLowerCase().includes(query) ||
            member.role.toLowerCase().includes(query)
          )
        })
        setFilteredStaff(filtered)
      } else {
        setFilteredStaff(staff.staff)
      }
    }
  }, [searchQuery, staff.staff])

  // Handle staff member selection
  const handleSelectStaff = useCallback(
    (id: string) => {
      staff.selectStaffMember(id)
      setSelectedStaffId(id)
      setIsModalVisible(true)
    },
    [staff],
  )

  // Handle staff member removal
  const handleRemoveStaff = useCallback(async () => {
    if (selectedStaffId) {
      const success = await staff.removeStaffMember(selectedStaffId)
      if (success) {
        setIsConfirmModalVisible(false)
        setIsModalVisible(false)
        setSelectedStaffId(null)
      } else {
        Alert.alert("Error", "Failed to remove staff member. Please try again.")
      }
    }
  }, [selectedStaffId, staff])

  // Format last login time
  const formatLastLogin = (lastLogin: string | null) => {
    if (!lastLogin) return "Never logged in"
    return formatDistanceToNow(new Date(lastLogin), { addSuffix: true })
  }

  // Render staff item
  const renderStaffItem = ({ item }: { item: StaffMember }) => (
    <TouchableOpacity
      style={styles.staffItem}
      onPress={() => handleSelectStaff(item.id)}
      activeOpacity={0.7}
    >
      <View style={styles.staffItemContent}>
        <View style={styles.staffImageContainer}>
          {item.profileImage ? (
            <Image source={{ uri: item.profileImage }} style={styles.staffImage} />
          ) : (
            <View style={[styles.staffImage, styles.staffImagePlaceholder]}>
              <Text style={styles.staffImagePlaceholderText}>
                {item.name
                  .split(" ")
                  .map((n) => n[0])
                  .join("")
                  .toUpperCase()}
              </Text>
            </View>
          )}
          <View
            style={[
              styles.statusIndicator,
              item.status === "active"
                ? styles.statusActive
                : item.status === "inactive"
                  ? styles.statusInactive
                  : styles.statusSuspended,
            ]}
          />
        </View>

        <View style={styles.staffInfo}>
          <Text style={styles.staffName}>{item.name}</Text>
          <Text style={styles.staffId}>{item.staffId}</Text>
          <Text style={styles.staffRole}>{item.role}</Text>
        </View>

        <View style={styles.staffMeta}>
          <Text style={styles.lastLogin}>{formatLastLogin(item.lastLogin)}</Text>
          <Icon icon="arwRigh" size={16} color={colors.palette.neutral500} />
        </View>
      </View>
    </TouchableOpacity>
  )

  // Render staff detail modal
  const renderStaffDetailModal = () => {
    const selectedStaff = staff.staff?.find((s) => s.id === selectedStaffId)
    if (!selectedStaff) return null

    return (
      <Modal
        visible={isModalVisible}
        transparent
        animationType="slide"
        onRequestClose={() => setIsModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Staff Details</Text>
              <TouchableOpacity onPress={() => setIsModalVisible(false)} style={styles.closeButton}>
                <Icon icon="x" size={24} color={colors.palette.neutral800} />
              </TouchableOpacity>
            </View>

            <View style={styles.staffDetailHeader}>
              {selectedStaff.profileImage ? (
                <Image
                  source={{ uri: selectedStaff.profileImage }}
                  style={styles.detailStaffImage}
                />
              ) : (
                <View style={[styles.detailStaffImage, styles.staffImagePlaceholder]}>
                  <Text style={styles.detailStaffImageText}>
                    {selectedStaff.name
                      .split(" ")
                      .map((n: any) => n[0])
                      .join("")
                      .toUpperCase()}
                  </Text>
                </View>
              )}

              <View style={styles.staffDetailHeaderInfo}>
                <Text style={styles.detailStaffName}>{selectedStaff.name}</Text>
                <View style={styles.staffBadge}>
                  <Text style={styles.staffBadgeText}>{selectedStaff.role}</Text>
                </View>
                <Text style={styles.detailStaffId}>{selectedStaff.staffId}</Text>
              </View>
            </View>

            <View style={styles.staffDetailContent}>
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Email</Text>
                <Text style={styles.detailValue}>{selectedStaff.email}</Text>
              </View>

              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Phone</Text>
                <Text style={styles.detailValue}>{selectedStaff.phone}</Text>
              </View>

              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Status</Text>
                <View style={styles.statusBadge}>
                  <View
                    style={[
                      styles.statusDot,
                      selectedStaff.status === "active"
                        ? styles.statusDotActive
                        : selectedStaff.status === "inactive"
                          ? styles.statusDotInactive
                          : styles.statusDotSuspended,
                    ]}
                  />
                  <Text
                    style={[
                      styles.statusText,
                      selectedStaff.status === "active"
                        ? styles.statusTextActive
                        : selectedStaff.status === "inactive"
                          ? styles.statusTextInactive
                          : styles.statusTextSuspended,
                    ]}
                  >
                    {selectedStaff.status.charAt(0).toUpperCase() + selectedStaff.status.slice(1)}
                  </Text>
                </View>
              </View>

              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Last Login</Text>
                <Text style={styles.detailValue}>{formatLastLogin(selectedStaff.lastLogin)}</Text>
              </View>

              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Permissions</Text>
                <View style={styles.permissionsContainer}>
                  {selectedStaff.permissions.map(
                    (permission: string, index: Key | null | undefined) => (
                      <View key={index} style={styles.permissionBadge}>
                        <Text style={styles.permissionText}>
                          {permission.charAt(0).toUpperCase() + permission.slice(1)}
                        </Text>
                      </View>
                    ),
                  )}
                </View>
              </View>
            </View>

            <View style={styles.modalActions}>
              <Button
                text="Edit Access"
                preset="default"
                style={styles.editButton}
                onPress={() => {
                  // Handle edit functionality
                  setIsModalVisible(false)
                }}
              />
              <Button
                text="Revoke Access"
                preset="default"
                style={styles.revokeButton}
                textStyle={styles.revokeButtonText}
                onPress={() => {
                  setIsConfirmModalVisible(true)
                }}
              />
            </View>
          </View>
        </View>
      </Modal>
    )
  }

  // Render confirmation modal
  const renderConfirmModal = () => (
    <Modal
      visible={isConfirmModalVisible}
      transparent
      animationType="fade"
      onRequestClose={() => setIsConfirmModalVisible(false)}
    >
      <View style={styles.confirmModalOverlay}>
        <View style={styles.confirmModalContent}>
          <Icon icon="warning" size={40} color={colors.error} style={styles.confirmIcon} />
          <Text style={styles.confirmTitle}>Revoke Access</Text>
          <Text style={styles.confirmText}>
            Are you sure you want to revoke access for this staff member? This action cannot be
            undone.
          </Text>

          <View style={styles.confirmActions}>
            <Button
              text="Cancel"
              preset="default"
              style={styles.cancelButton}
              onPress={() => setIsConfirmModalVisible(false)}
            />
            <Button
              text="Revoke"
              preset="default"
              style={styles.confirmRevokeButton}
              textStyle={styles.confirmRevokeButtonText}
              onPress={handleRemoveStaff}
            />
          </View>
        </View>
      </View>
    </Modal>
  )

  return (
    <>
      <Header
        title="Staff Management"
        leftIcon="backicon"
        onLeftPress={() => navigation.goBack()}
        rightIcon="plus"
        onRightPress={() => {
          // Handle add staff functionality
          console.log("Add staff")
        }}
      />
      <Screen style={styles.root} preset="fixed" statusBarStyle="dark">
        <View style={styles.searchContainer}>
          <TextField
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholder="Rechercher par nom, rôle ou ID"
            style={styles.searchInput}
            containerStyle={styles.searchContainer}
            LeftAccessory={() => (
              <Icon
                icon="search"
                size={25}
                containerStyle={{ paddingTop: 15, paddingHorizontal: 5 }}
                color={colors.palette.neutral500}
              />
            )}
            RightAccessory={() => (
              <>
                {searchQuery ? (
                  <Icon
                    icon="x"
                    size={25}
                    containerStyle={{ padding: 15 }}
                    color={colors.palette.neutral500}
                    onPress={() => setSearchQuery("")}
                  />
                ) : null}
              </>
            )}
          />
        </View>

        {staff.loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.palette.primary500} />
            <Text style={styles.loadingText}>Loading staff members...</Text>
          </View>
        ) : staff.error ? (
          <View style={styles.errorContainer}>
            <Icon icon="warning" size={60} color={colors.error} />
            <Text style={styles.errorText}>{staff.error}</Text>
            <Button
              text="Retry"
              preset="default"
              onPress={() => {
                if (businessId) {
                  staff.fetchPersonnel(businessId)
                }
              }}
              style={styles.retryButton}
            />
          </View>
        ) : filteredStaff.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Icon icon="user" size={60} color={colors.palette.neutral400} />
            <Text style={styles.emptyText}>
              {searchQuery ? "No staff members match your search" : "No staff members found"}
            </Text>
            {searchQuery ? (
              <Button
                text="Clear Search"
                preset="default"
                onPress={() => setSearchQuery("")}
                style={styles.clearSearchButton}
              />
            ) : (
              <Button
                text="Add Staff Member"
                preset="default"
                onPress={() => {
                  // Handle add staff functionality
                  console.log("Add staff")
                }}
                style={styles.addStaffButton}
              />
            )}
          </View>
        ) : (
          <FlatList
            data={filteredStaff}
            renderItem={renderStaffItem}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.listContent}
            showsVerticalScrollIndicator={false}
            ItemSeparatorComponent={() => <View style={styles.separator} />}
          />
        )}
      </Screen>

      {renderStaffDetailModal()}
      {renderConfirmModal()}
    </>
  )
}

const styles = StyleSheet.create({
  addStaffButton: {
    minWidth: 200,
  },
  cancelButton: {
    backgroundColor: colors.palette.neutral200,
    flex: 1,
    marginRight: spacing.sm,
  },
  clearSearchButton: {
    minWidth: 150,
  },
  closeButton: {
    padding: spacing.xs,
  },
  confirmActions: {
    flexDirection: "row",
    width: "100%",
  },
  confirmIcon: {
    marginBottom: spacing.md,
  },
  confirmModalContent: {
    alignItems: "center",
    backgroundColor: colors.background,
    borderRadius: 12,
    maxWidth: 400,
    padding: spacing.lg,
    width: "90%",
  },
  confirmModalOverlay: {
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    flex: 1,
    justifyContent: "center",
    padding: spacing.lg,
  },
  confirmRevokeButton: {
    backgroundColor: colors.palette.angry500,
    flex: 1,
    marginLeft: spacing.sm,
  },
  confirmRevokeButtonText: {
    color: colors.palette.neutral100,
  },
  confirmText: {
    color: colors.palette.neutral600,
    fontSize: 14,
    marginBottom: spacing.lg,
    textAlign: "center",
  },
  confirmTitle: {
    color: colors.text,
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: spacing.sm,
  },
  detailLabel: {
    color: colors.palette.neutral500,
    fontSize: 14,
    marginBottom: 4,
  },
  detailRow: {
    marginBottom: spacing.md,
  },
  detailStaffId: {
    color: colors.palette.neutral600,
    fontSize: 14,
  },
  detailStaffImage: {
    borderRadius: 40,
    height: 80,
    marginRight: spacing.md,
    width: 80,
  },
  detailStaffImageText: {
    color: colors.palette.neutral800,
    fontSize: 28,
    fontWeight: "bold",
  },
  detailStaffName: {
    color: colors.text,
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: spacing.xs,
  },
  detailValue: {
    color: colors.text,
    fontSize: 16,
  },
  editButton: {
    backgroundColor: colors.palette.neutral200,
    flex: 1,
    marginRight: spacing.sm,
  },
  emptyContainer: {
    alignItems: "center",
    // flex: 1,
    justifyContent: "center",
    paddingHorizontal: spacing.lg,
  },
  emptyText: {
    color: colors.palette.neutral600,
    fontSize: 16,
    marginBottom: spacing.lg,
    marginTop: spacing.md,
    textAlign: "center",
  },
  errorContainer: {
    alignItems: "center",
    // flex: 1,
    justifyContent: "center",
    paddingHorizontal: spacing.lg,
  },
  errorText: {
    color: colors.error,
    fontSize: 16,
    marginBottom: spacing.lg,
    marginTop: spacing.md,
    textAlign: "center",
  },
  retryButton: {
    minWidth: 150,
  },
  lastLogin: {
    color: colors.palette.neutral500,
    fontSize: 12,
    marginBottom: 4,
  },
  listContent: {
    paddingBottom: spacing.lg,
    paddingHorizontal: spacing.md,
  },
  loadingContainer: {
    alignItems: "center",
    // flex: 1,
    justifyContent: "center",
  },
  loadingText: {
    color: colors.palette.neutral600,
    fontSize: 16,
    marginTop: spacing.sm,
  },
  modalActions: {
    borderTopColor: colors.palette.neutral200,
    borderTopWidth: 1,
    flexDirection: "row",
    justifyContent: "space-between",
    paddingTop: spacing.md,
  },
  modalContent: {
    backgroundColor: colors.background,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingHorizontal: spacing.lg,
    paddingBottom: spacing.xl + spacing.lg, // Extra padding for bottom safe area
    maxHeight: "89%",
  },
  modalHeader: {
    alignItems: "center",
    borderBottomColor: colors.palette.neutral200,
    borderBottomWidth: 1,
    flexDirection: "row",
    justifyContent: "space-between",
    paddingVertical: spacing.md,
  },
  modalOverlay: {
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    flex: 1,
    justifyContent: "flex-end",
  },
  modalTitle: {
    color: colors.text,
    fontSize: 18,
    fontWeight: "bold",
  },
  permissionBadge: {
    backgroundColor: colors.palette.neutral200,
    borderRadius: 4,
    marginBottom: spacing.xs,
    marginRight: spacing.xs,
    paddingHorizontal: spacing.sm,
    paddingVertical: 4,
  },
  permissionText: {
    color: colors.palette.neutral800,
    fontSize: 12,
  },
  permissionsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    marginTop: 4,
  },
  revokeButton: {
    backgroundColor: colors.palette.angry100,
    flex: 1,
    marginLeft: spacing.sm,
  },
  revokeButtonText: {
    color: colors.palette.angry500,
  },
  root: {
    // backgroundColor: colors.background,
    flex: 1,
  },
  searchContainer: {
    borderBottomColor: colors.palette.neutral200,
    borderBottomWidth: 1,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
  },
  searchInput: {
    backgroundColor: "transparent",
    flex: 1,
    fontSize: 16,
    height: 40,
  },
  separator: {
    backgroundColor: colors.palette.neutral200,
    height: 1,
  },
  staffBadge: {
    alignSelf: "flex-start",
    backgroundColor: colors.palette.primary100,
    borderRadius: 4,
    marginBottom: spacing.xs,
    paddingHorizontal: spacing.sm,
    paddingVertical: 2,
  },
  staffBadgeText: {
    color: colors.palette.primary500,
    fontSize: 12,
    fontWeight: "500",
  },
  staffDetailContent: {
    paddingBottom: spacing.lg,
  },
  staffDetailHeader: {
    alignItems: "center",
    flexDirection: "row",
    paddingVertical: spacing.lg,
  },
  staffDetailHeaderInfo: {
    flex: 1,
  },
  staffId: {
    color: colors.palette.neutral600,
    fontSize: 14,
    marginBottom: 2,
  },
  staffImage: {
    borderRadius: 25,
    height: 50,
    width: 50,
  },
  staffImageContainer: {
    marginRight: spacing.md,
    position: "relative",
  },
  staffImagePlaceholder: {
    alignItems: "center",
    backgroundColor: colors.palette.neutral300,
    justifyContent: "center",
  },
  staffImagePlaceholderText: {
    color: colors.palette.neutral800,
    fontSize: 18,
    fontWeight: "bold",
  },
  staffInfo: {
    flex: 1,
  },
  staffItem: {
    paddingVertical: spacing.md,
  },
  staffItemContent: {
    alignItems: "center",
    flexDirection: "row",
  },
  staffMeta: {
    alignItems: "flex-end",
  },
  staffName: {
    color: colors.text,
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 2,
  },
  staffRole: {
    color: colors.palette.primary500,
    fontSize: 14,
    fontWeight: "500",
  },
  statusActive: {
    backgroundColor: colors.palette.primary500,
  },
  statusBadge: {
    alignItems: "center",
    flexDirection: "row",
  },
  statusDot: {
    borderRadius: 4,
    height: 8,
    marginRight: spacing.xs,
    width: 8,
  },
  statusDotActive: {
    backgroundColor: colors.palette.primary500,
  },
  statusDotInactive: {
    backgroundColor: colors.palette.neutral500,
  },
  statusDotSuspended: {
    backgroundColor: colors.palette.angry500,
  },
  statusInactive: {
    backgroundColor: colors.palette.neutral500,
  },
  statusIndicator: {
    borderColor: colors.background,
    borderRadius: 6,
    borderWidth: 2,
    bottom: 0,
    height: 12,
    position: "absolute",
    right: 0,
    width: 12,
  },
  statusSuspended: {
    backgroundColor: colors.palette.angry500,
  },
  statusText: {
    fontSize: 16,
    fontWeight: "500",
  },
  statusTextActive: {
    color: colors.palette.primary500,
  },
  statusTextInactive: {
    color: colors.palette.neutral500,
  },
  statusTextSuspended: {
    color: colors.palette.angry500,
  },
})

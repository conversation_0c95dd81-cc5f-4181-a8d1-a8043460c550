import { ErrorInfo } from "react"
import { Linking, ScrollView, TextStyle, View, ViewStyle } from "react-native"
import { Button, Icon, Screen, Text } from "../../components"
import { $Gstyles, colors, spacing, type ThemedStyle } from "@/theme"
import { useAppTheme } from "@/utils/useAppTheme"

export interface ErrorDetailsProps {
  error: Error
  errorInfo: ErrorInfo | null
  onReset(): void
}

/**
 * Renders the error details screen.
 * @param {ErrorDetailsProps} props - The props for the `ErrorDetails` component.
 * @returns {JSX.Element} The rendered `ErrorDetails` component.
 */
export function ErrorDetails(props: ErrorDetailsProps) {
  const { themed } = useAppTheme()

  const openWhatsApp = () => {
    const message = `Bonjour, j'ai besoin d'aide avec cette error \n${props.errorInfo?.componentStack ?? ""}`

    // Replace with your actual WhatsApp number
    const phoneNumber = "+243836803506"

    // Create WhatsApp URL with encoded message
    const url = `https://wa.me/${phoneNumber.replace("+", "")}?text=${encodeURIComponent(message)}`

    Linking.openURL(url)
      .then((supported) => {
        if (supported) {
          return Linking.openURL(url)
        } else {
          alert("WhatsApp n'est pas installé sur votre appareil")
          return Promise.resolve() // Explicitly return a resolved promise
        }
      })
      .catch((err) => console.error("An error occurred", err))
  }


  return (
    <Screen
      preset="fixed"
      safeAreaEdges={["top", "bottom"]}
      contentContainerStyle={themed($contentContainer)}
      style={{  }}
    >
       <View
        style={{
          flexDirection: "row",
          justifyContent: "space-between",
          // alignItems: "center",
          top: 0,
          right: 0,
          position: "absolute",
          marginBottom: spacing.md,
        }}
      >
        <View style={{ flexDirection: "row", gap: spacing.sm }}>
          <Icon
            icon="whatsapp"
            size={30}
            onPress={openWhatsApp}
            color={colors.palette.primary200}
            containerStyle={[$Gstyles.ActionIcon, { backgroundColor: colors.palette.primary600, padding: 9,  }]}
          />
        </View>
      </View>
      <View style={$topSection}>
        <Icon icon="warning" size={104} />
        <Text style={themed($heading)} preset="subheading" tx="errorScreen:title" />
        <Text tx="errorScreen:friendlySubtitle" style={{ textAlign: "center", paddingVertical: 30 }} />
      </View>

      {/* <ScrollView
        style={themed($errorSection)}
        contentContainerStyle={themed($errorSectionContentContainer)}
      >
        <Text style={themed($errorContent)} weight="bold" text={`${props.error}`.trim()} />
        <Text
          selectable
          style={themed($errorBacktrace)}
          text={`${props.errorInfo?.componentStack ?? ""}`.trim()}
        />
      </ScrollView> */}

      <Button
        preset="reversed"
        style={themed($resetButton)}
        onPress={props.onReset}
        tx="errorScreen:reset"
      />
    </Screen>
  )
}

const $contentContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  alignItems: "center",
  paddingHorizontal: spacing.lg,
  paddingTop: spacing.xl,
  justifyContent: "center",
  flex: 1,
})

const $topSection: ViewStyle = {
  // flex: 1,
  alignItems: "center",
}

const $heading: ThemedStyle<TextStyle> = ({ colors, spacing }) => ({
  color: colors.error,
  marginBottom: spacing.md,
})

const $errorSection: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  flex: 2,
  backgroundColor: colors.separator,
  marginVertical: spacing.md,
  borderRadius: 6,
})

const $errorSectionContentContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  padding: spacing.md,
})

const $errorContent: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.error,
})

const $errorBacktrace: ThemedStyle<TextStyle> = ({ colors, spacing }) => ({
  marginTop: spacing.md,
  color: colors.textDim,
})

const $resetButton: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  backgroundColor: colors.error,
  paddingHorizontal: spacing.xxl,
})

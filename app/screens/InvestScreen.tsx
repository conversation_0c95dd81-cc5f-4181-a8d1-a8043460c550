import { FC } from "react"
import { ViewStyle } from "react-native"
import { AppStackScreenProps } from "@/navigators"
import { Header, Screen, Text } from "@/components"
// import { useNavigation } from "@react-navigation/native"

interface InvestScreenProps extends AppStackScreenProps<"Invest"> {}

export const InvestScreen: FC<InvestScreenProps> = ({ navigation }) => {
  // Pull in navigation via hook
  // const navigation = useNavigation()
  return (
    <>
      <Header
        title="Mes Investissements"
        leftIcon="backicon"
        onLeftPress={() => navigation.goBack()}
      />
      <Screen style={$root} preset="scroll">
        <Text text="invest" />
      </Screen>
    </>
  )
}

const $root: ViewStyle = {
  flex: 1,
}

import { FC, useEffect } from "react"
import { ViewStyle, View, TextStyle, TouchableOpacity } from "react-native"
import { AppStackScreenProps } from "@/navigators"
import { Header, Screen, Text, Icon } from "@/components"
import { colors, spacing } from "@/theme"
import { useWalletStore } from "@/store/FedhaPochiStore"
import { LinearGradient } from "expo-linear-gradient"

interface InvestScreenProps extends AppStackScreenProps<"Invest"> {}

interface InvestmentOption {
  id: string
  title: string
  subtitle: string
  icon: string
  iconColor: string
  hasApplyButton: boolean
  amount?: number
  percentage?: number
}

const investmentOptions: InvestmentOption[] = [
  {
    id: "community",
    title: "Investir en Communauté",
    subtitle: "Community AVECs",
    icon: "group",
    iconColor: colors.palette.primary600,
    hasApplyButton: true,
  },
  {
    id: "local",
    title: "Investir Localement",
    subtitle: "Local business/economy",
    icon: "briefcase",
    iconColor: colors.palette.neutral900,
    hasApplyButton: true,
  },
  {
    id: "minerals",
    title: "Investir en Minéraux",
    subtitle: "Mineral investments",
    icon: "diamond",
    iconColor: colors.palette.secondsMain,
    hasApplyButton: true,
  },
  {
    id: "copec",
    title: "Copec Savings",
    subtitle: "Savings account",
    icon: "wallet",
    iconColor: colors.palette.neutral900,
    hasApplyButton: false,
    amount: 125000.5,
  },
]

export const InvestScreen: FC<InvestScreenProps> = ({ navigation }) => {
  const { currentBalance, fetchWallet } = useWalletStore()

  useEffect(() => {
    fetchWallet()
  }, [fetchWallet])

  const formatCurrency = (amount: number) => {
    return amount.toLocaleString("fr-CD", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })
  }

  const handleApplyNow = (optionId: string) => {
    // Handle apply now action based on investment type
    console.log(`Apply for investment: ${optionId}`)
    // You can navigate to specific investment screens here
    // Example navigation:
    // navigation.navigate("InvestmentDetails", { type: optionId })
  }

  return (
    <>
      <Header
        title="Mes Investissements"
        leftIcon="backicon"
        onLeftPress={() => navigation.goBack()}
      />
      <Screen style={$root} preset="scroll">
        {/* Total Holdings Header */}
        <View style={$totalHoldingsCard}>
          <LinearGradient
            colors={[colors.palette.neutral900, colors.palette.neutral700]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={$gradientCard}
          >
            <Text style={$totalHoldingsLabel}>Total des Avoirs</Text>
            <Text style={$totalHoldingsAmount}>FC {formatCurrency(currentBalance)}</Text>
          </LinearGradient>
        </View>

        {/* FedhaPochi Savings Account */}
        <View style={$savingsCard}>
          <View style={$savingsHeader}>
            <View style={$savingsInfo}>
              <Icon icon="fedhapochi" size={40} color={colors.palette.primary600} />
              <View style={$savingsDetails}>
                <Text style={$savingsTitle}>FedhaPochi Saving</Text>
                <Text style={$savingsSubtitle}>Compte d&apos;épargne principal</Text>
              </View>
            </View>
            <View style={$savingsAmount}>
              <Text style={$savingsAmountText}>FC {formatCurrency(currentBalance)}</Text>
              <Text style={$savingsPercentage}>+5.5%</Text>
            </View>
          </View>
        </View>

        {/* Investment Options */}
        <View style={$investmentSection}>
          <Text style={$sectionTitle}>Options d&apos;Investissement</Text>

          {investmentOptions.map((option) => (
            <View key={option.id} style={$investmentCard}>
              <View style={$investmentHeader}>
                <View style={$investmentInfo}>
                  <Icon
                    icon={option.icon as any}
                    size={36}
                    color={option.iconColor}
                    containerStyle={$investmentIcon}
                  />
                  <View style={$investmentDetails}>
                    <Text style={$investmentTitle}>{option.title}</Text>
                    <Text style={$investmentSubtitle}>{option.subtitle}</Text>
                  </View>
                </View>

                <View style={$investmentAction}>
                  {option.hasApplyButton ? (
                    <TouchableOpacity
                      style={$applyButton}
                      onPress={() => handleApplyNow(option.id)}
                    >
                      <Text style={$applyButtonText}>Ouvrir un compte</Text>
                    </TouchableOpacity>
                  ) : (
                    <Text style={$investmentAmount}>FC {formatCurrency(option.amount || 0)}</Text>
                  )}
                </View>
              </View>
            </View>
          ))}
        </View>
      </Screen>
    </>
  )
}

const $root: ViewStyle = {
  flex: 1,
  paddingBottom: spacing.xl,
  paddingHorizontal: spacing.md,
  backgroundColor: colors.palette.neutral100,
}

const $totalHoldingsCard: ViewStyle = {
  marginBottom: spacing.lg,
  borderRadius: 16,
  overflow: "hidden",
  shadowColor: colors.palette.neutral800,
  shadowOffset: { width: 0, height: 4 },
  shadowOpacity: 0.1,
  shadowRadius: 8,
  elevation: 4,
}

const $gradientCard: ViewStyle = {
  padding: spacing.lg,
  alignItems: "center",
}

const $totalHoldingsLabel: TextStyle = {
  color: colors.palette.neutral300,
  fontSize: 14,
  fontWeight: "500",
  marginBottom: spacing.xs,
}

const $totalHoldingsAmount: TextStyle = {
  color: colors.palette.neutral100,
  fontSize: 32,
  padding: 10,
  fontWeight: "bold",
  letterSpacing: 1,
}

const $savingsCard: ViewStyle = {
  backgroundColor: colors.palette.neutral100,
  borderRadius: 12,
  padding: spacing.md,
  marginBottom: spacing.lg,
  shadowColor: colors.palette.neutral800,
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.05,
  shadowRadius: 4,
  elevation: 2,
  borderWidth: 1,
  borderColor: colors.palette.neutral200,
}

const $savingsHeader: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
}

const $savingsInfo: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  flex: 1,
}

const $savingsDetails: ViewStyle = {
  marginLeft: spacing.sm,
  flex: 1,
}

const $savingsTitle: TextStyle = {
  fontSize: 16,
  fontWeight: "600",
  color: colors.text,
  marginBottom: 2,
}

const $savingsSubtitle: TextStyle = {
  fontSize: 12,
  color: colors.palette.neutral600,
}

const $savingsAmount: ViewStyle = {
  alignItems: "flex-end",
}

const $savingsAmountText: TextStyle = {
  fontSize: 16,
  fontWeight: "bold",
  color: colors.text,
}

const $savingsPercentage: TextStyle = {
  fontSize: 12,
  color: colors.palette.primary600,
  fontWeight: "500",
  marginTop: 2,
}

const $investmentSection: ViewStyle = {
  marginTop: spacing.sm,
}

const $sectionTitle: TextStyle = {
  fontSize: 18,
  fontWeight: "600",
  color: colors.text,
  marginBottom: spacing.md,
}

const $investmentCard: ViewStyle = {
  backgroundColor: colors.palette.neutral100,
  borderRadius: 12,
  padding: spacing.md,
  marginBottom: spacing.sm,
  shadowColor: colors.palette.neutral800,
  shadowOffset: { width: 0, height: 1 },
  shadowOpacity: 0.05,
  shadowRadius: 2,
  elevation: 1,
  borderWidth: 1,
  borderColor: colors.palette.neutral200,
}

const $investmentHeader: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
}

const $investmentInfo: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  flex: 1,
}

const $investmentIcon: ViewStyle = {
  backgroundColor: colors.palette.neutral200,
  borderRadius: 8,
  padding: spacing.xs,
}

const $investmentDetails: ViewStyle = {
  marginLeft: spacing.sm,
  flex: 1,
}

const $investmentTitle: TextStyle = {
  fontSize: 16,
  fontWeight: "600",
  color: colors.text,
  marginBottom: 2,
}

const $investmentSubtitle: TextStyle = {
  fontSize: 12,
  color: colors.palette.neutral600,
}

const $investmentAction: ViewStyle = {
  alignItems: "flex-end",
}

const $applyButton: ViewStyle = {
  backgroundColor: colors.palette.primary600,
  paddingHorizontal: spacing.md,
  paddingVertical: spacing.xs,
  borderRadius: 8,
}

const $applyButtonText: TextStyle = {
  color: colors.palette.neutral100,
  fontSize: 14,
  fontWeight: "500",
}

const $investmentAmount: TextStyle = {
  fontSize: 16,
  fontWeight: "bold",
  color: colors.text,
}

/* eslint-disable @typescript-eslint/no-unused-vars */
// src/screens/ScannerScreen.tsx
import { FC, useEffect, useRef, useState } from "react"
import {
  ViewStyle,
  StatusBar,
  View,
  ActivityIndicator,
  Alert,
  TextStyle,
  TouchableOpacity,
  Modal,
} from "react-native"
import { CurvedHeaderBackground, Icon, Scanner, Text } from "@/components"
import { CustomScannerRef } from "@/components/CustomScanner"
import { colors, spacing } from "@/theme"
import { useSafeAreaInsets } from "react-native-safe-area-context"
import { NativeStackScreenProps } from "@react-navigation/native-stack"
import { useStores } from "@/store"
import { getPochiID } from "@/services/api"

// --- (Keep your type definitions: ScannerParamList, ScannerScreenProps) ---
type ScannerParamList = {
  Scanner: {
    hideTabBar?: boolean
    onScanComplete?: (data: any) => Promise<void> | void
    returnScreen?: string
    user_type?: "standard" | "business"
  }
}
type ScannerScreenProps = NativeStackScreenProps<ScannerParamList, "Scanner">
// --- (End of type definitions) ---

const HEADER_HEIGHT = 125 // Define header height (adjust as needed based on curve/content)
const CURVE_DEPTH = 20 // Adjust this to match the background component's curve

export const ScannerScreen: FC<ScannerScreenProps> = ({ navigation, route }) => {
  const insets = useSafeAreaInsets()
  const { auth } = useStores()
  const [isScanning, setIsScanning] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)
  const [isSearching, setIsSearching] = useState(true)
  const [scanStatus, setScanStatus] = useState<"searching" | "success" | "error" | null>(null)
  const [statusMessage, setStatusMessage] = useState("")
  const { onScanComplete, returnScreen } = route.params || {}
  const [flash, setflash] = useState<boolean>(false)
  const scannerRef = useRef<CustomScannerRef>(null)

  // Verification error modal state
  const [showVerificationErrorModal, setShowVerificationErrorModal] = useState(false)
  const [verificationErrorMessage, setVerificationErrorMessage] = useState("")
  const [lastScannedData, setLastScannedData] = useState<any>(null)

  // Hide tab bar if needed
  useEffect(() => {
    if (route.params?.hideTabBar) {
      const parent = navigation.getParent()
      if (parent) {
        parent.setOptions({ tabBarStyle: { display: "none" } })
        return () => {
          parent.setOptions({ tabBarStyle: undefined })
        }
      }
    }
    return () => {}
  }, [navigation, route.params?.hideTabBar])

  // Add delay for searching state and then enable scanning
  useEffect(() => {
    // Set initial status
    setScanStatus("searching")
    setStatusMessage("Recherche en cours...")

    // Add a delay before enabling the scanner
    const searchingTimer = setTimeout(() => {
      setIsSearching(false)
      setIsScanning(true)
      setScanStatus(null)
    }, 5000) // 2 second delay

    // Clean up timer on unmount
    return () => clearTimeout(searchingTimer)
  }, [])

  // Helper function to extract the actual data (handles API response with details)
  const extractData = (data: any): any => {
    if (typeof data === "object" && data.details && typeof data.details === "object") {
      return data.details
    }
    return data
  }

  // Helper functions to identify data types
  const isBusinessAccount = (data: any): boolean => {
    // Extract data if it's wrapped in a details object
    const actualData = extractData(data)

    // Check if it's a business account
    return (
      typeof actualData === "object" &&
      actualData.wallet_id &&
      actualData.name &&
      !actualData.cart_items
    )
  }

  const isPersonalAccount = (data: any): boolean => {
    // Extract data if it's wrapped in a details object
    const actualData = extractData(data)

    // Check if it's a personal account
    return (
      typeof actualData === "object" &&
      actualData.wallet_id &&
      actualData.first_name &&
      actualData.last_name
    )
  }

  const isInvoicePayment = (data: any): boolean => {
    // Extract data if it's wrapped in a details object
    const actualData = extractData(data)

    // Check if it's an invoice payment
    return (
      typeof actualData === "object" &&
      actualData.wallet_id &&
      actualData.cart_items &&
      Array.isArray(actualData.cart_items)
    )
  }

  // Get data type description for logging
  const getDataTypeDescription = (data: any): string => {
    if (isBusinessAccount(data)) return "business_account"
    if (isPersonalAccount(data)) return "personal_account"
    if (isInvoicePayment(data)) return "invoice_payment"
    return "unknown_format"
  }

  // Handle successful scan with data type detection
  const handleScanSuccess = async (data: any) => {
    // console.log("Scanned data:", data)
    // Stop scanning immediately when a QR code is detected
    setIsScanning(false)
    setIsProcessing(true)
    setScanStatus("success")
    setStatusMessage("Code QR détecté!")

    // Save the scanned data for retry functionality
    setLastScannedData(data)

    try {
      // Short delay to show success message
      await new Promise((resolve) => setTimeout(resolve, 500))

      // Check if the data is in one of our expected formats
      if (isBusinessAccount(data) || isPersonalAccount(data) || isInvoicePayment(data)) {
        const dataType = getDataTypeDescription(data)
        console.log(`Detected data type: ${dataType}`, data)

        // Extract the actual data (in case it's wrapped in a details object)
        const actualData = extractData(data)

        // Pass the data directly back without verification
        if (onScanComplete && typeof onScanComplete === "function") {
          onScanComplete(actualData)
        }

        // Navigate to return screen or safely go back
        if (returnScreen) {
          ;(navigation as any).navigate(returnScreen, {
            scannedData: actualData,
            dataType: dataType,
          })
        } else {
          // Use the same safe navigation logic as handleClose
          handleClose()
        }
      } else {
        // If data is not in expected format, try to verify with API
        try {
          const verifQR = await getPochiID(data)
          // console.log("Verification result:", verifQR)

          if (verifQR.success) {
            // Check the data type of the verified data
            const dataType = getDataTypeDescription(verifQR.data)
            console.log(`API verified data type: ${dataType}`, verifQR.data)

            // Extract the actual data from the API response
            const actualData = extractData(verifQR.data)

            // If verification is successful, pass the verified data back
            if (onScanComplete && typeof onScanComplete === "function") {
              onScanComplete(actualData)
            }

            // Navigate to return screen or safely go back
            if (returnScreen) {
              ;(navigation as any).navigate(returnScreen, {
                scannedData: actualData,
                dataType: dataType,
              })
            } else {
              // Use the same safe navigation logic as handleClose
              handleClose()
            }
          } else {
            // If verification fails, show the error modal
            setVerificationErrorMessage(verifQR.data || "Code QR non reconnu")
            setShowVerificationErrorModal(true)
            setScanStatus("error")
            setStatusMessage("Échec de vérification")
          }
        } catch (verifyError) {
          console.error("Error verifying QR code:", verifyError)
          setVerificationErrorMessage("Erreur lors de la vérification du code QR")
          setShowVerificationErrorModal(true)
          setScanStatus("error")
          setStatusMessage("Échec de vérification")
        }
      }
    } catch (error) {
      console.error("Error processing scan:", error)
      // Make sure scanning stays off even if there's an error
      setIsScanning(false)
      setScanStatus("error")
      setStatusMessage("Erreur de traitement du code QR")

      // Show verification error modal with the error message
      setVerificationErrorMessage("Une erreur s'est produite lors de la vérification du code QR.")
      setShowVerificationErrorModal(true)
    } finally {
      setIsProcessing(false)
    }
  }

  // Handle retry scanning
  // const handleRetry = () => {
  //   // Hide the error modal
  //   setShowVerificationErrorModal(false)
  //   // Reset status
  //   setScanStatus(null)
  //   setStatusMessage("")
  //   // Start scanning again
  //   setIsScanning(true)
  // }

  // Handle cancel scanning
  const handleCancel = () => {
    // Hide the error modal
    setShowVerificationErrorModal(false)
    // Navigate back or to the return screen
    handleClose()
  }

  // Updated handleClose to safely navigate back
  const handleClose = () => {
    try {
      // Check if we can go back
      const canGoBack = navigation.canGoBack()
      console.log("Can go back:", canGoBack)

      if (canGoBack) {
        console.log("Navigating back...")
        navigation.goBack()
      } else {
        // If we can't go back, navigate to TabNav or BusinessTabNav
        // First check route params, then fall back to auth store
        const routeUserType = route.params?.user_type
        const userType = routeUserType || auth.user_type
        const targetScreen = userType === "business" ? "BusinessTabNav" : "TabNav"

        console.log("Can't go back, navigating to:", targetScreen)
        navigation.navigate(targetScreen as any)
      }
    } catch (error) {
      console.error("Navigation error:", error)
      // As a last resort, try to navigate to TabNav
      console.log("Error occurred, trying to navigate to TabNav")
      navigation.navigate("TabNav" as any)
    }
  }

  return (
    <View style={$root}>
      <StatusBar barStyle="light-content" backgroundColor={colors.palette.Black} />

      {/* Header Area with Curved Background */}
      <View style={[$headerContainer, { height: HEADER_HEIGHT }]}>
        {/* Curved Background - Render FIRST so it's behind other elements */}
        <CurvedHeaderBackground
          height={HEADER_HEIGHT}
          curveDepth={CURVE_DEPTH}
          backgroundColor={colors.palette.neutral100}
        />

        {/* Header Content (on top of the curve) */}
        <View style={[$headerContent, { paddingTop: insets.top }]}>
          <TouchableOpacity
            onPress={handleClose}
            style={$closeButton}
            activeOpacity={0.7} // Add this to give visual feedback when pressed
          >
            <Icon icon="x" size={28} color={colors.palette.neutral900} />
          </TouchableOpacity>

          <Text style={$headerTitle}>Scanner QR Code</Text>

          <View style={$headerRight}>
            <TouchableOpacity
              onPress={() => {
                // Toggle flash state
                // flash = !flash
                setflash(!flash)
                // If we had a custom scanner with ref, we could call:
                scannerRef.current?.toggleFlash()
              }}
              style={$flashButton}
              activeOpacity={0.7} // Add this to give visual feedback when pressed
            >
              <Icon
                icon={!flash ? "flashOff" : "flashOn"}
                size={28}
                color={colors.palette.neutral900}
              />
            </TouchableOpacity>
          </View>
        </View>

        {/* Status Message Area */}
        <View style={$statusContainer}>
          {isSearching && (
            <View style={$statusContent}>
              <ActivityIndicator size="small" color={colors.palette.primary500} />
              <Text style={$statusText}>Recherche en cours...</Text>
            </View>
          )}

          {!isSearching && scanStatus === "success" && (
            <View style={$statusContent}>
              <Icon icon="check" size={20} color={colors.palette.primary500} />
              <Text style={[$statusText, $successText]}>{statusMessage}</Text>
            </View>
          )}

          {!isSearching && scanStatus === "error" && (
            <View style={$statusContent}>
              <Icon icon="warning" size={20} color={colors.palette.angry500} />
              <Text style={[$statusText, $errorText]}>{statusMessage}</Text>
            </View>
          )}
        </View>
      </View>

      {/* Scanner View */}
      <View style={$scannerContainer}>
        <Scanner
          flash={flash}
          visible={isScanning && !isProcessing} // Make sure scanner is not active when processing
          onClose={handleClose}
          onscan={handleScanSuccess}
          style={$scanner}
        />
      </View>
      {/* Processing Overlay */}
      {isProcessing && (
        <View style={$processingOverlay}>
          <ActivityIndicator size="large" color={colors.palette.primary500} />
          <Text style={$processingText}>Traitement du code QR...</Text>
        </View>
      )}

      {/* Verification Error Modal */}
      <Modal
        visible={showVerificationErrorModal}
        transparent={true}
        animationType="slide"
        onRequestClose={handleCancel}
      >
        <View style={$modalOverlay}>
          <View style={$modalContainer}>
            <View style={$modalHeader}>
              <Icon icon="warning" size={40} color={colors.palette.angry500} />
              <Text style={$modalTitle}>Vérification échouée</Text>
            </View>

            <Text style={$modalMessage}>{verificationErrorMessage}</Text>

            <View style={$modalButtonsContainer}>
              <TouchableOpacity
                style={[$modalButton, $secondaryButton]}
                onPress={handleCancel}
                activeOpacity={0.7}
              >
                <Text style={[$modalButtonText, $secondaryButtonText]}>Annuler</Text>
              </TouchableOpacity>

              {/* <TouchableOpacity style={$modalButton} onPress={handleRetry} activeOpacity={0.7}>
                <Text style={$modalButtonText}>Réessayer</Text>
              </TouchableOpacity> */}
            </View>
          </View>
        </View>
      </Modal>
    </View>
  )
}

// Styles
const $root: ViewStyle = {
  flex: 1,
  backgroundColor: colors.palette.Black,
}

const $headerContainer: ViewStyle = {
  position: "relative",
  zIndex: 10,
}

const $headerContent: ViewStyle = {
  position: "absolute",
  top: 0,
  left: 0,
  right: 0,
  paddingHorizontal: spacing.md,
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
  zIndex: 20, // Increased z-index to ensure it's above the curved background
  marginTop: 15,
}

const $headerTitle: TextStyle = {
  flex: 1,
  textAlign: "center",
  fontSize: 18,
  fontWeight: "600",
  color: colors.palette.neutral800,
  marginLeft: -spacing.lg,
}

const $closeButton: ViewStyle = {
  padding: 10,
  zIndex: 30, // Ensure buttons are clickable
}

const $headerRight: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  gap: spacing.sm,
  zIndex: 30, // Ensure buttons are clickable
}

const $flashButton: ViewStyle = {
  padding: spacing.xs,
  zIndex: 30, // Ensure buttons are clickable
}

// Status message styles
const $statusContainer: ViewStyle = {
  position: "absolute",
  bottom: 10,
  left: 0,
  right: 0,
  alignItems: "center",
  paddingHorizontal: spacing.md,
  zIndex: 25, // Increased z-index to ensure it's above the curved background but below buttons
}

const $statusContent: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  backgroundColor: colors.palette.neutral200,
  paddingVertical: spacing.xs,
  paddingHorizontal: spacing.md,
  borderRadius: 20,
  minWidth: 150,
  justifyContent: "center",
}

const $statusText: TextStyle = {
  marginLeft: spacing.xs,
  fontSize: 14,
  fontWeight: "500",
  color: colors.palette.neutral800,
}

const $successText: TextStyle = {
  color: colors.palette.primary500,
}

const $errorText: TextStyle = {
  color: colors.palette.angry500,
}

const $scannerContainer: ViewStyle = {
  flex: 1,
}

const $scanner: ViewStyle = {
  flex: 1,
}

// Processing overlay styles
const $processingOverlay: ViewStyle = {
  position: "absolute",
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  backgroundColor: "rgba(0, 0, 0, 0.8)",
  justifyContent: "center",
  alignItems: "center",
  zIndex: 1000,
}

const $processingText: TextStyle = {
  color: colors.palette.neutral100,
  marginTop: spacing.md,
  fontSize: 16,
  fontWeight: "500",
}

// Modal styles
const $modalOverlay: ViewStyle = {
  flex: 1,
  backgroundColor: "rgba(0, 0, 0, 0.5)",
  justifyContent: "center",
  alignItems: "center",
  padding: spacing.lg,
}

const $modalContainer: ViewStyle = {
  backgroundColor: colors.palette.neutral100,
  borderRadius: 20,
  padding: spacing.lg,
  width: "100%",
  maxWidth: 400,
  alignItems: "center",
  elevation: 5,
  shadowColor: "#000",
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.25,
  shadowRadius: 3.84,
}

const $modalHeader: ViewStyle = {
  alignItems: "center",
  marginBottom: spacing.md,
}

const $modalTitle: TextStyle = {
  fontSize: 20,
  fontWeight: "bold",
  color: colors.palette.neutral900,
  marginTop: spacing.sm,
  textAlign: "center",
}

const $modalMessage: TextStyle = {
  fontSize: 16,
  color: colors.palette.neutral700,
  textAlign: "center",
  marginBottom: spacing.lg,
  lineHeight: 22,
}

const $modalButtonsContainer: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
  width: "100%",
  marginTop: spacing.sm,
}

const $modalButton: ViewStyle = {
  backgroundColor: colors.palette.primary500,
  paddingVertical: spacing.sm,
  paddingHorizontal: spacing.md,
  borderRadius: 25,
  flex: 1,
  marginHorizontal: spacing.xs,
  alignItems: "center",
}

const $secondaryButton: ViewStyle = {
  backgroundColor: colors.palette.neutral900,
}

const $modalButtonText: TextStyle = {
  color: colors.palette.neutral100,

  fontSize: 16,
  fontWeight: "600",
  textAlign: "center",
}

const $secondaryButtonText: TextStyle = {
  color: colors.palette.neutral100,
}

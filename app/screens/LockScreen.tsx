import { FC, useEffect, useState } from "react"
import {
  ViewStyle,
  View,
  TextStyle,
  TouchableOpacity,
  ImageStyle,
  BackHandler,
  ActivityIndicator,
  ImageBackground,
  Dimensions,
} from "react-native"
import { AppStackScreenProps } from "@/navigators"
import { Screen, Text, TextField, Icon, WatcherAction, Button } from "@/components"
import { colors, spacing } from "@/theme"
import { useStores } from "@/store/rootStore"
import { Avatar } from "react-native-paper"
import { capitalizeFirstLetter, getNameInitials } from "@/utils/actions"
import * as SecureStore from "expo-secure-store"
import * as LocalAuthentication from "expo-local-authentication"
import * as Haptics from "expo-haptics"
import { useFocusEffect } from "@react-navigation/native"

interface LockScreenProps extends AppStackScreenProps<"Lock"> {}

const { height } = Dimensions.get("window")
const HEADER_HEIGHT = height * 0.6

export const LockScreen: FC<LockScreenProps> = ({ navigation }) => {
  const [password, setPassword] = useState("")
  const [isPasswordHidden, setIsPasswordHidden] = useState(true)
  const [profileImage, setProfileImage] = useState<string | null>(null)
  const [loading, setLoading] = useState(false)
  const {
    auth: { user },
  } = useStores()

  const isButtonDisabled = loading || !password

  // Prevent going back
  useFocusEffect(() => {
    const onBackPress = () => {
      return true // Prevents going back
    }
    BackHandler.addEventListener("hardwareBackPress", onBackPress)
    return () => BackHandler.removeEventListener("hardwareBackPress", onBackPress)
  })

  useEffect(() => {
    if (user?.profile_picture) {
      setProfileImage(user.profile_picture)
    }
  }, [user])

  const handleUnlock = async () => {
    try {
      setLoading(true)
      const storedPassword = await SecureStore.getItemAsync("userPassword")

      if (!storedPassword) {
        console.error("No stored password found")
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error)
        return
      }

      if (password === storedPassword) {
        navigation.navigate("Dashboard")
      } else {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error)
      }
    } catch (error) {
      console.error("Error verifying password:", error)
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error)
    } finally {
      setLoading(false)
      setPassword("") // Clear password field after attempt
    }
  }

  const handleBiometric = async () => {
    const { success } = await LocalAuthentication.authenticateAsync({
      promptMessage: "Veuillez utiliser votre empreinte digitale ou votre identification faciale",
    })

    if (success) {
      navigation.navigate("Dashboard")
    } else {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error)
    }
  }

  return (
    <ImageBackground
      source={require("../../assets/images/bgdeem.png")}
      style={$root}
      resizeMode="cover"
    >
      <Screen
        style={$screenStyle}
        preset="fixed"
        safeAreaEdges={["top"]}
        statusBarStyle="dark"
        backgroundColor="transparent"
      >
        <View style={$container}>
          {/* Profile Section */}
          <View style={$profileSection}>
            <View style={$avatarContainer}>
              {profileImage ? (
                <Avatar.Image size={100} source={{ uri: profileImage }} style={$avatar} />
              ) : (
                <Avatar.Text
                  size={100}
                  label={getNameInitials(user?.first_name || "", user?.last_name || "")}
                  style={$avatar}
                  color={colors.palette.neutral100}
                />
              )}
            </View>
            <View style={$usrText}>
              <Text style={$userName}>
                {capitalizeFirstLetter(user?.first_name || "")}{" "}
                {capitalizeFirstLetter(user?.last_name || "")}
              </Text>
              <Text style={$welcomeBack}>Bienvenue!</Text>
            </View>
          </View>

          {/* Password Input Section */}
          <View style={$inputSection}>
            {/* <Text text="Entrer votre mot de passe" preset="formLabel" style={$inputLabel} /> */}

            <TextField
              value={password}
              onChangeText={setPassword}
              secureTextEntry={isPasswordHidden}
              placeholder="Enter your password"
              placeholderTextColor={colors.palette.neutral400}
              autoCapitalize="none"
              autoCorrect={false}
              containerStyle={$inputContainer}
              style={$input}
              // onSubmitEditing={handleUnlock}
              RightAccessory={() => (
                <WatcherAction
                  style={$watcherIcon}
                  iconSize={24}
                  isHidden={isPasswordHidden}
                  onToggle={() => setIsPasswordHidden(!isPasswordHidden)}
                />
              )}
            />
            <View style={$buttonContainer}>
              <Button
                testID="login-button"
                preset="reversed"
                text={loading ? "Chargement..." : "Se connecter"}
                style={[$loginButton, isButtonDisabled && $disabledButton]}
                disabled={isButtonDisabled}
                onPress={handleUnlock}
              >
                {loading && <ActivityIndicator size="small" color="white" />}
              </Button>
            </View>
          </View>

          {/* Biometric Button */}
          <View style={$biometricSection}>
            <TouchableOpacity
              style={$biometricButton}
              onPress={handleBiometric}
              activeOpacity={0.7}
            >
              <Icon icon="fingerprint" size={50} color={colors.palette.neutral900} />
              {/* <Text style={$biometricText}>Use Fingerprint</Text> */}
            </TouchableOpacity>
          </View>
        </View>
      </Screen>
    </ImageBackground>
  )
}

const $root: ViewStyle = {
  flex: 1,
  width: "100%",
}

const $screenStyle: ViewStyle = {
  // flex: 1,
  width: "100%",
}

const $buttonContainer: ViewStyle = {
  alignItems: "center",
  paddingVertical: spacing.md,
}

const $loginButton: ViewStyle = {
  backgroundColor: colors.palette.neutral900,
  borderRadius: 16,
  top: 20,
  elevation: 3,
  paddingVertical: spacing.lg,
  width: "100%",
}

const $disabledButton: ViewStyle = {
  opacity: 0.5,
}

const $container: ViewStyle = {
  // flex: 1,
  height: HEADER_HEIGHT,
  justifyContent: "space-between",
  paddingHorizontal: spacing.md,
  paddingTop: spacing.xxl * 2,
  paddingBottom: spacing.xxl,
}

const $profileSection: ViewStyle = {
  alignItems: "center",
}

const $avatarContainer: ViewStyle = {
  marginBottom: spacing.md,
  // shadowColor: colors.palette.neutral900,
  // shadowOffset: { width: 0, height: 4 },
  // shadowOpacity: 0.3,
  // shadowRadius: 8,
  // elevation: 5,
}
const $usrText: ViewStyle = {
  alignItems: "center",
  // paddingTop: spacing.xl,
}

const $avatar: ImageStyle = {
  // borderWidth: 3,
  // borderColor: colors.palette.primary400,
}

const $userName: TextStyle = {
  fontSize: 25,
  fontWeight: "bold",
  color: colors.palette.neutral800,
  textAlign: "center",
  marginBottom: spacing.xs,
}

const $welcomeBack: TextStyle = {
  fontSize: 18,
  color: colors.palette.accent500,
  textAlign: "center",
}

const $inputSection: ViewStyle = {
  width: "100%",
  paddingHorizontal: spacing.lg,
}

const $inputContainer: ViewStyle = {
  backgroundColor: colors.palette.neutral200,
  borderRadius: 12,
  borderWidth: 0,
  top: 20,
}

const $input: TextStyle = {
  color: colors.palette.neutral900,
  fontSize: 16,
  paddingVertical: spacing.xs,
}

const $biometricSection: ViewStyle = {
  alignItems: "center",
}

const $watcherIcon: ViewStyle = {
  right: spacing.sm,
  top: 20,
}

const $biometricButton: ViewStyle = {
  alignItems: "center",
  paddingTop: spacing.xxl * 2,
  padding: spacing.md,
  // borderRadius: 30,
  // backgroundColor: colors.palette.neutral800,
  // shadowColor: colors.palette.neutral900,
  // shadowOffset: { width: 0, height: 4 },
  // shadowOpacity: 0.2,
  // shadowRadius: 6,
  // elevation: 4,
}

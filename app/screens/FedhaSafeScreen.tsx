import { FC } from "react"
import { ViewStyle, TouchableOpacity, Image, ImageStyle } from "react-native"
import { AppStackScreenProps } from "@/navigators"
import { Screen } from "@/components"
import { colors } from "@/theme"

interface FedhaSafeScreenProps extends AppStackScreenProps<"FedhaSafe"> {}

export const FedhaSafeScreen: FC<FedhaSafeScreenProps> = ({ navigation }) => {
  return (
    <Screen
      safeAreaEdges={["bottom", "top"]}
      style={$root}
      preset="fixed"
      statusBarStyle="dark"
      // backgroundColor={colors.palette.neutral900}
    >
      <TouchableOpacity
        style={$imageContainer}
        onPress={() => navigation.navigate("Lock")}
        activeOpacity={0.8}
      >
        <Image
          source={require("../../assets/images/fedhaSafeLogo.png")}
          style={[$lockImage, { tintColor: colors.palette.neutral400 }]}
          resizeMode="contain"
        />
      </TouchableOpacity>
    </Screen>
  )
}

const $root: ViewStyle = {
  flex: 1,
  justifyContent: "center",
  alignItems: "center",
}

const $imageContainer: ViewStyle = {
  flex: 1,
  justifyContent: "center",
  alignItems: "center",
  width: "100%",
}

const $lockImage: ImageStyle = {
  width: 200,
}

/* eslint-disable import/no-unresolved */
import { FC, useState } from "react"
import {
  View,
  ViewStyle,
  TextStyle,
  TouchableOpacity,
  Image,
  TextInput,
  ImageStyle,
  FlatList,
} from "react-native"
import { AppStackScreenProps } from "@/navigators"
import { Header, Screen, Text, Icon } from "@/components"
import { colors, spacing, typography } from "@/theme"

interface ElimuHubScreenProps extends AppStackScreenProps<"ElimuHub"> {}

interface VideoItem {
  id: string
  title: string
  thumbnail: number // Change from string to number since require() returns a number
  description: string
  duration: string
}

export const ElimuHubScreen: FC<ElimuHubScreenProps> = ({ navigation, route }) => {
  const [searchQuery, setSearchQuery] = useState("")
  const title = route.params?.serviceName || "ElimuHub"

  // Mock data - Replace with actual API data
  const videos: VideoItem[] = [
    {
      id: "1",
      title: `Comment utiliser ${title}`,
      description: `Un guide complet sur l'utilisation de ${title} pour vos transactions quotidiennes`,
      thumbnail: require("../../assets/images/VideoThum.png"),
      duration: "4:30",
    },
    // Add more videos...
  ]

  const renderVideoItem = ({ item }: { item: VideoItem }) => (
    <TouchableOpacity style={$videoItem}>
      <View style={$thumbnailContainer}>
        <Image source={item.thumbnail} style={$thumbnail as ImageStyle} />
        <View style={$playIconOverlay}>
          <Icon icon="play" size={24} color={colors.palette.neutral100} />
        </View>
      </View>
      <View style={$videoInfo}>
        <Text style={$videoTitle} numberOfLines={2}>
          {item.title}
        </Text>
        <Text style={$videoDescription} numberOfLines={2}>
          {item.description}
        </Text>
        <View style={$durationContainer}>
          <Icon icon="T0clock" size={20} color={colors.palette.neutral500} />
          <Text style={$duration}>{item.duration}</Text>
        </View>
      </View>
    </TouchableOpacity>
  )

  return (
    <>
      <Header
        leftIcon="backicon"
        onLeftPress={navigation.goBack}
        title={"Comment ça marche ?"}
        backgroundColor={colors.palette.neutral100}
      />
      <Screen style={$root} preset="fixed" contentContainerStyle={$screenContent}>
        <View style={$headerSection}>
          <Text style={$mainTitle} numberOfLines={2}>
            {`Comment fonctionne ${title}`}
          </Text>
          <View style={$searchContainer}>
            <Icon icon="search" size={20} color={colors.palette.neutral500} />
            <TextInput
              style={$searchInput}
              placeholder="Rechercher..."
              value={searchQuery}
              onChangeText={setSearchQuery}
              placeholderTextColor={colors.palette.neutral500}
            />
          </View>
        </View>

        <FlatList
          data={videos}
          renderItem={renderVideoItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={$listContent}
          showsVerticalScrollIndicator={false}
        />
      </Screen>
    </>
  )
}

const $root: ViewStyle = {
  flex: 1,
  backgroundColor: colors.background,
}

const $screenContent: ViewStyle = {
  flex: 1,
}

const $headerSection: ViewStyle = {
  backgroundColor: colors.palette.neutral100,
  paddingHorizontal: spacing.md,
  paddingVertical: spacing.md,
  borderBottomWidth: 1,
  borderBottomColor: colors.palette.neutral300,
}

const $mainTitle: TextStyle = {
  ...typography.secondary,
  fontSize: 20,
  fontWeight: "bold",
  color: colors.palette.neutral800,
  marginBottom: spacing.md,
}

const $searchContainer: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  backgroundColor: colors.palette.neutral200,
  borderRadius: 8,
  padding: spacing.sm,
  borderWidth: 1,
  borderColor: colors.palette.neutral300,
}

const $searchInput: TextStyle = {
  flex: 1,
  marginLeft: spacing.xs,
  fontSize: 16,
  color: colors.text,
  height: 40,
}

const $listContent: ViewStyle = {
  padding: spacing.md,
}

const $videoItem: ViewStyle = {
  flexDirection: "row",
  marginBottom: spacing.md,
  backgroundColor: colors.palette.neutral100,
  borderRadius: 12,
  overflow: "hidden",
  elevation: 2,
  shadowColor: colors.palette.neutral800,
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
  shadowRadius: 2,
}

const $thumbnailContainer: ViewStyle = {
  position: "relative",
  justifyContent: "center",
  alignItems: "center",
}

const $thumbnail: ImageStyle = {
  width: 120,
  height: 90,
  resizeMode: "cover",
}

const $playIconOverlay: ViewStyle = {
  position: "absolute",
  backgroundColor: colors.palette.neutral800 + "80", // 80 adds 50% opacity
  width: 40,
  height: 40,
  borderRadius: 20,
  justifyContent: "center",
  alignItems: "center",
}

const $videoInfo: ViewStyle = {
  flex: 1,
  padding: spacing.sm,
  justifyContent: "space-between",
}

const $videoTitle: TextStyle = {
  ...typography.secondary,
  fontSize: 16,
  fontWeight: "600",
  marginBottom: spacing.xs,
}

const $videoDescription: TextStyle = {
  fontSize: 14,
  color: colors.palette.neutral600,
  marginBottom: spacing.xs,
}

const $durationContainer: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  gap: spacing.xs,
}

const $duration: TextStyle = {
  fontSize: 12,
  color: colors.palette.neutral500,
}

/* eslint-disable no-restricted-imports */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable react-native/no-inline-styles */
/* eslint-disable import/no-unresolved */
import { FC, useEffect, useState } from "react"
import {
  TouchableOpacity,
  View,
  ViewStyle,
  StyleSheet,
  Image,
  ScrollView,
  TextInput,
  TextStyle,
  Alert,
  Platform,
  ActivityIndicator,
} from "react-native"
import { AppStackScreenProps } from "@/navigators"
import { launchCamera, launchImageLibrary } from "react-native-image-picker"
import { useImageCompression } from "@/hooks/useImageCompression"
import {
  Button,
  ChangeCurency,
  ConfirmationWidget,
  FedhaPochiWidget,
  FencyTextInput,
  Header,
  Icon,
  Screen,
  Text,
  SimpleLoader,
} from "@/components"
import { $Gstyles, colors, Recevoirstyles, spacing } from "@/theme"
// import { FedhaPochiTopUp } from "@/services/api"
import { useStores } from "@/store/rootStore"
import { FedhaPochiTopUp, getAccountList } from "@/services/api"
import React from "react"
import { useNavigation } from "@react-navigation/native"
// import { showRechargementNotification } from "@/utils/notificationHelpers"

interface RecevoirScreenProps extends AppStackScreenProps<"Recevoir"> {}

interface ReactNativeFormDataValue {
  uri: string
  type: string
  name: string
}

declare global {
  interface FormData {
    append(name: string, value: ReactNativeFormDataValue): void
  }
}

const WalletsPage = () => {
  const navigation = useNavigation()
  const {
    appsettings: { currency, getServerCurrencyCode },
  } = useStores()
  const walletIcons: { [key: string]: any } = {
    Vodacom: require("../../../assets/images/vodacom.png"),
    Airtel: require("../../../assets/images/airtlm.png"),
    Orange: require("../../../assets/images/orangem.png"),
  }

  // Image compression hook
  const { isCompressing, validateAndCompress } = useImageCompression()

  const [transferProof, setTransferProof] = useState<string | any>(null)
  const [exwallet, setExwallet] = useState<any>([])
  const [errors, setErrors] = useState<{
    transferProof?: string
    transactionId?: string
    amount?: string
  }>({})

  // Handle image selection from gallery with compression
  const handleImagePicker = async () => {
    launchImageLibrary(
      {
        mediaType: "photo",
        quality: 0.8, // Initial quality
        maxWidth: 2000,
        maxHeight: 2000,
      },
      async (response) => {
        if (response.assets && response.assets[0]?.uri) {
          console.log("📷 Image selected from gallery")

          // Compress image before setting it
          const compressedUri = await validateAndCompress(response.assets[0].uri, true)

          if (compressedUri) {
            setTransferProof(compressedUri)
            console.log("✅ Compressed image set for upload")
          } else {
            Alert.alert(
              "Erreur",
              "Impossible de traiter l'image. Veuillez essayer avec une autre image.",
              [{ text: "OK" }],
            )
          }
        }
      },
    )
  }

  // Handle camera capture with compression
  const handleCameraLaunch = async () => {
    launchCamera(
      {
        mediaType: "photo",
        quality: 0.8, // Initial quality
        maxWidth: 2000,
        maxHeight: 2000,
      },
      async (response) => {
        if (response.assets && response.assets[0]?.uri) {
          console.log("📸 Image captured from camera")

          // Compress image before setting it
          const compressedUri = await validateAndCompress(response.assets[0].uri, true)

          if (compressedUri) {
            setTransferProof(compressedUri)
            console.log("✅ Compressed image set for upload")
          } else {
            Alert.alert(
              "Erreur",
              "Impossible de traiter l'image. Veuillez essayer avec une autre image.",
              [{ text: "OK" }],
            )
          }
        }
      },
    )
  }
  const getWalletsList = async () => {
    try {
      const response = await getAccountList()
      if (response.success) {
        const wallets = response.data
        console.log(wallets.data)
        setExwallet(wallets.data)
      } else {
        // Handle the error
        console.error(response.message)
      }
    } catch (error) {
      console.error("Error fetching account list:", error)
    }
  }

  useEffect(() => {
    getWalletsList()
  }, [])
  // here th user will be selecting the wallet they will use basically the next window will just shhw once they select a wallet
  // for now it just one wallet so will just by default put it simple
  const wallets = [
    // { name: "M-Pesa", number: "************", icon: "mpesa" },
    { name: "Airtel Money", value: "airtel_money", number: "************", icon: "airtlm" },
    // { name: "Orange Money", number: "************", icon: "orangem" },
  ]

  const {
    tabContent,
    walletName,
    messageContainer,
    walletRight,
    walletNumber,
    IcoImage,
    walletLeft,
    walletRow,
    ProofTImage,
    walletId,
  } = Recevoirstyles

  const [isSubmitted, setIsSubmitted] = useState<boolean>(false)
  const [isamount, setisAmount] = useState<number>(0)
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false)
  const [isSuccess, setIsSuccess] = useState<boolean | null>(null)
  const [showCurrencyModal, setShowCurrencyModal] = useState(false)
  const [transactionId, setTransactionId] = useState<string>("")
  const [isLoading, setIsLoading] = useState(false)

  // ConfirmationWidget state
  const [showConfirmation, setShowConfirmation] = useState(false)
  const [transactionResult, setTransactionResult] = useState<{
    isSuccess: boolean
    apiResponse?: any
    errorMessage?: string
  } | null>(null)
  const {
    transactions: { fetchTransactions, fetchTopups },
  } = useStores()

  // Handle confirmation widget close - navigate to dashboard
  const handleConfirmationClose = () => {
    setShowConfirmation(false)
    setTransactionResult(null)
    // Navigate to dashboard
    navigation.navigate("Dashboard" as never)
  }

  // Test function to manually show confirmation widget
  // const testConfirmationWidget = () => {
  //   console.log("🧪 Testing ConfirmationWidget...")
  //   setTransactionResult({
  //     isSuccess: true,
  //     apiResponse: {
  //       detail: "successful topup",
  //       amount: 1000,
  //       currency: "CDF",
  //       transaction_id: "TEST_123",
  //       created_at: new Date().toISOString(),
  //       service_type: "FedhaPochiTopUp",
  //       status: "completed",
  //     },
  //   })
  //   setShowConfirmation(true)
  // }

  const handleSubmit = async () => {
    try {
      setIsLoading(true) // Show loader
      setErrors({})

      // Validate inputs
      if (!transferProof) {
        setErrors((prev) => ({ ...prev, transferProof: "La preuve de transfert est requise" }))
        return
      }

      if (!transactionId) {
        setErrors((prev) => ({ ...prev, transactionId: "L'ID de transaction est requis" }))
        return
      }

      if (!isamount) {
        setErrors((prev) => ({ ...prev, amount: "Le montant est requis" }))
        return
      }

      const formData = new FormData()
      formData.append("transaction_image", {
        uri: transferProof,
        type: "image/jpeg",
        name: "transfer_proof.jpg",
      } as any)
      formData.append("transaction_id", transactionId)
      formData.append("amount", isamount.toString())
      formData.append("currency", getServerCurrencyCode(currency))

      const response = await FedhaPochiTopUp(formData)

      if (response?.detail === "successful topup") {
        console.log("✅ Topup successful:", response)
        setShowConfirmation(true)
        // Set success result for ConfirmationWidget
        setTransactionResult({
          isSuccess: true,
          apiResponse: {
            ...response,
            amount: isamount,
            currency: getServerCurrencyCode(currency),
            transaction_id: transactionId,
            created_at: new Date().toISOString(),
            service_type: "FedhaPochiTopUp",
            status: "completed",
          },
        })

        console.log("🎉 Setting showConfirmation to true")
        setShowConfirmation(true)

        // Update stores
        fetchTopups()
        fetchTransactions()
        setIsSuccess(true)
        setIsSubmitted(true)

        // Reset form
        setTransactionId("")
        setisAmount(0)
        setTransferProof("")
        setErrors({})
      } else {
        throw new Error("La demande de rechargement a échoué")
      }
    } catch (error: any) {
      console.error("❌ TopUp Failed:", error.response?.data || error.message)

      // Handle different types of errors
      let errorMessage = "Une erreur s'est produite lors de la transaction. Veuillez réessayer."

      if (error.response?.data) {
        if (error.response.data.errors?.transaction_image) {
          errorMessage = "Erreur avec l'image: " + error.response.data.errors.transaction_image[0]
        } else if (error.response.data.errors?.amount) {
          errorMessage = "Erreur avec le montant: " + error.response.data.errors.amount[0]
        } else if (error.response.data.errors?.transaction_id) {
          errorMessage =
            "Erreur avec l'ID de transaction: " + error.response.data.errors.transaction_id[0]
        } else if (error.response.data.message) {
          errorMessage = error.response.data.message
        } else if (error.response.data.detail) {
          errorMessage = error.response.data.detail
        }
      }

      // Set failure result for ConfirmationWidget
      setTransactionResult({
        isSuccess: false,
        errorMessage: errorMessage,
      })

      console.log("❌ Setting showConfirmation to true for error")
      setShowConfirmation(true)

      setIsSuccess(false)
    } finally {
      setIsLoading(false) // Hide loader
    }
  }

  // console.log("cur", currency)
  return (
    <View>
      {exwallet.map((wallet: any, index: number) => (
        <View key={index} style={walletRow}>
          <View style={walletLeft}>
            <Image
              source={walletIcons[wallet.network]} // Replace with your QR image path
              style={IcoImage}
              resizeMode="contain"
            />
            <Text style={walletName}>{wallet.name}</Text>
          </View>
          <View style={walletRight}>
            <Text style={walletNumber}>{wallet.account}</Text>
          </View>
        </View>
      ))}

      <View style={$boxcomp}>
        {isSubmitted ? (
          <View style={messageContainer}>
            <Text>Your transaction is under verification.</Text>
            <Text>Your wallet will be credited automatically.</Text>
          </View>
        ) : (
          <>
            <View
              style={{
                flexDirection: "row",
                justifyContent: "space-between",
                // borderBottomColor: colors.palette.neutral300,
              }}
            >
              <TouchableOpacity
                onPress={handleCameraLaunch}
                disabled={isCompressing}
                style={{
                  flexDirection: "row",
                  marginHorizontal: spacing.md,
                  paddingVertical: spacing.sm,
                  borderBottomWidth: 1,
                  borderBottomColor: colors.palette.neutral300,
                  opacity: isCompressing ? 0.6 : 1,
                }}
              >
                <Icon icon="photocamera" color={colors.palette.primary600} size={25} />
                <Text numberOfLines={2} style={walletId}>
                  {isCompressing ? "Traitement..." : "Capture une photo"}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={handleImagePicker}
                disabled={isCompressing}
                style={{
                  flexDirection: "row",
                  paddingVertical: spacing.sm,
                  borderBottomWidth: 1,
                  borderBottomColor: colors.palette.neutral300,
                  opacity: isCompressing ? 0.6 : 1,
                }}
              >
                <Icon icon="upload_image" color={colors.palette.primary600} size={25} />
                <Text style={walletId}>{isCompressing ? "Traitement..." : "Choisir Galerie"}</Text>
              </TouchableOpacity>
            </View>

            <View>
              <View
                style={{
                  width: "100%",
                }}
              >
                {transferProof ? (
                  <View>
                    <Image
                      source={{ uri: transferProof }}
                      resizeMode="contain"
                      style={ProofTImage}
                    />
                  </View>
                ) : (
                  <Text style={$errorText}>{errors.transferProof}</Text>
                )}

                <FencyTextInput
                  value={transactionId}
                  onChange={(value: string) => {
                    setTransactionId(value)
                    // Clear error when user starts typing
                    if (errors.transactionId) {
                      setErrors((prev) => ({ ...prev, transactionId: undefined }))
                    }
                  }}
                  inputname="Enter Transaction ID"
                  placeholder="numéro du transaction ID"
                  status={errors.transactionId ? "error" : undefined}
                />

                <FencyTextInput
                  value={isamount}
                  onChange={(value: number) => {
                    setisAmount(value)
                    // Clear error when user starts typing
                    if (errors.amount) {
                      setErrors((prev) => ({ ...prev, amount: undefined }))
                    }
                  }}
                  inputname="Montant Envoyer"
                  keyboardType="numeric"
                  placeholder="Saisir le montant"
                  LeftIcon={currency === "FC" ? "FC" : currency === "USD" ? "dollar" : ""}
                  leftonPress={() => setShowCurrencyModal(!showCurrencyModal)}
                  status={errors.amount ? "error" : undefined}
                />
                <Button
                  testID="next-screen-button"
                  preset="filled"
                  style={[$button, isSubmitting && { opacity: 0.5 }]}
                  textStyle={{ color: colors.palette.neutral100 }}
                  onPress={handleSubmit}
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <View style={{ flexDirection: "row", alignItems: "center" }}>
                      <ActivityIndicator size="small" color="white" style={{ marginRight: 8 }} />
                      <Text style={{ color: colors.palette.neutral100 }}>Chargement...</Text>
                    </View>
                  ) : (
                    <Text style={{ color: colors.palette.neutral100 }}>Recharger</Text>
                  )}
                </Button>

                {/* Test button for ConfirmationWidget */}
                {/* <Button
                  testID="test-confirmation-button"
                  preset="default"
                  style={[
                    $button,
                    { marginTop: spacing.sm, backgroundColor: colors.palette.secondary500 },
                  ]}
                  textStyle={{ color: colors.palette.neutral100 }}
                  onPress={testConfirmationWidget}
                >
                  Test Confirmation
                </Button> */}
              </View>
            </View>
            <ChangeCurency
              isVisible={showCurrencyModal}
              onClose={() => setShowCurrencyModal(false)}
            />
          </>
        )}
      </View>
      <View
        style={{
          bottom: 0,
          width: "100%",
        }}
      >
        <Text style={$hint}>
          Envoyez le montant vers l’un de nos portefeuilles : Airtel Money. Téléchargez une photo de
          la transaction et entrez l’ID de transfert. Une fois vérifié, votre portefeuille
          FedhaPochi sera debuté.
        </Text>
      </View>
      <SimpleLoader visible={isLoading} />

      {/* ConfirmationWidget for topup results */}
      {/* {console.log("🔍 ConfirmationWidget props:", {
        visible: showConfirmation,
        isSuccess: transactionResult?.isSuccess,
        hasApiResponse: !!transactionResult?.apiResponse,
        errorMessage: transactionResult?.errorMessage,
      })} */}
      <ConfirmationWidget
        visible={showConfirmation}
        isSuccess={transactionResult?.isSuccess || false}
        apiResponse={transactionResult?.apiResponse}
        errorMessage={transactionResult?.errorMessage}
        transactionType="Rechargement FedhaPochi"
        onButtonPress={handleConfirmationClose}
      />
    </View>
  )
}

export const RecevoirScreen: FC<RecevoirScreenProps> = ({ navigation }) => {
  const [activeTab, setActiveTab] = useState<"fedhapochi" | "rechargement">("fedhapochi")

  return (
    <>
      <Header leftIcon="backicon" onLeftPress={navigation.goBack} title="Recevoir" />
      <Screen
        preset="scroll"
        // contentContainerStyle={$container}
        statusBarStyle="dark"
        style={$container}
      >
        <View style={$tabButtonContainer}>
          <TouchableOpacity
            style={[$tabButton, activeTab === "fedhapochi" && $activeTabButton]}
            onPress={() => setActiveTab("fedhapochi")}
          >
            <Text style={[$tabButtonText, activeTab === "fedhapochi" && $activeTabText]}>
              FedhaPochi
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[$tabButton, activeTab === "rechargement" && $activeTabButton]}
            onPress={() => setActiveTab("rechargement")}
          >
            <Text style={[$tabButtonText, activeTab === "rechargement" && $activeTabText]}>
              Rechargement
            </Text>
          </TouchableOpacity>
        </View>
        {activeTab === "fedhapochi" ? <FedhaPochiWidget /> : <WalletsPage />}
      </Screen>
    </>
  )
}

const $container: ViewStyle = {
  flex: 1,
  paddingBottom: spacing.xl,
  paddingHorizontal: spacing.lg,
}

const $tabButtonContainer: ViewStyle = {
  flexDirection: "row",
  marginBottom: spacing.md,
  borderRadius: 8,
  backgroundColor: colors.palette.neutral200,
  padding: spacing.xs,
}

const $tabButton: ViewStyle = {
  flex: 1,
  paddingVertical: spacing.sm,
  alignItems: "center",
  borderRadius: 6,
}

const $activeTabButton: ViewStyle = {
  backgroundColor: colors.palette.neutral900,
}

const $tabButtonText: TextStyle = {
  color: colors.palette.neutral900,
  fontWeight: "600",
}

const $activeTabText: TextStyle = {
  color: colors.palette.neutral100,
}

const $button: ViewStyle = {
  marginTop: spacing.md,
  backgroundColor: colors.palette.neutral900,
  borderRadius: 8,
}

const $input: ViewStyle = {
  borderWidth: 1,
  borderColor: colors.palette.neutral300,
  borderRadius: 8,
  padding: spacing.sm,
  marginVertical: spacing.xs,
  backgroundColor: colors.palette.neutral100,
}

const $uploadButton: ViewStyle = {
  padding: spacing.sm,
  backgroundColor: colors.palette.primary500,
  borderRadius: 8,
  alignItems: "center",
  marginTop: spacing.sm,
}

const $uploadButtonText: TextStyle = {
  color: colors.palette.neutral100,
  fontSize: 16,
}

const $boxcomp: ViewStyle = {
  // borderWidth: 1,
  flex: 1,
  alignItems: "center",
  // borderColor: colors.palette.accent300,
  alignContent: "center",
  justifyContent: "center",
  borderRadius: 15,
  padding: spacing.xxs,
  marginBottom: spacing.lg,
}

const $hint: TextStyle = {
  color: colors.palette.neutral600,
  fontSize: 13,
  lineHeight: 15,
  paddingBottom: spacing.lg,
}

const $errorText: TextStyle = {
  color: colors.palette.angry500,
  fontSize: 12,
  marginBottom: spacing.sm,
}

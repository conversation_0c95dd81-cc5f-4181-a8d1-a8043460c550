/* eslint-disable no-restricted-imports */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable react-native/sort-styles */
/* eslint-disable react-native/no-color-literals */
/* eslint-disable react-native/no-unused-styles */
/* eslint-disable react-native/no-inline-styles */
import React, { FC, useState } from "react"
import {
  View,
  FlatList,
  TouchableOpacity,
  Alert,
  StyleSheet, // Import StyleSheet
  Modal,
  // Image,
} from "react-native"
import { AppStackScreenProps } from "@/navigators"
import { Button, Header, Icon, Text } from "@/components"
import { useCartStore, CartItem } from "@/store/CartStore"
import { colors, spacing } from "@/theme"
import { useStores } from "@/store/rootStore"

interface MyCartScreenProps extends AppStackScreenProps<"MyCart"> {}

export const MyCartScreen: FC<MyCartScreenProps> = ({ navigation }) => {
  const {
    mycart: { items, removeItem, updateQuantity, clearCart, getTotalPrice },
  } = useStores()
  const [isClearCartModalVisible, setIsClearCartModalVisible] = useState(false)

  // Calculate totals by currency
  const totals = getTotalPrice()

  // Handle checkout process
  const handleCheckout = () => {
    if (items.length === 0) {
      Alert.alert("Erreur", "Votre panier est vide")
      return
    }

    // Prepare cart summary
    const cartSummary = {
      items: items.map((item) => ({
        name: item.name,
        description: item.description,
        quantity: item.quantity,
        price: item.price,
        currency: item.currency,
        total: item.price * item.quantity,
      })),
      totals: Object.entries(totals).map(([currency, amount]) => ({
        currency,
        amount,
      })),
      totalItems: items.length,
    }

    // Navigate to PaymentMethods screen with cart details
    navigation.navigate("PaymentMethods", { cartSummary } as never)
  }

  // Handle showing the clear cart confirmation modal
  const handleShowClearCartModal = () => {
    if (items.length === 0) return
    setIsClearCartModalVisible(true)
  }

  // Handle clearing the cart
  const handleConfirmClearCart = () => {
    clearCart()
    setIsClearCartModalVisible(false)
  }

  // Handle canceling the clear cart action
  const handleCancelClearCart = () => {
    setIsClearCartModalVisible(false)
  }

  // Render an individual cart item
  const renderCartItem = ({ item }: { item: CartItem }) => (
    <View style={styles.cartItem}>
      <View style={styles.itemDetails}>
        <Text style={styles.itemName}>{item.name}</Text>
        <Text style={styles.itemDescription}>{item.description || "Aucune description"}</Text>
        <Text style={styles.itemPrice}>
          {item.price.toLocaleString()} {item.currency}
        </Text>
      </View>

      <View style={styles.quantityContainer}>
        <TouchableOpacity
          style={styles.quantityButton}
          onPress={() => updateQuantity(item.id, item.quantity - 1)}
        >
          <Icon icon="minus" size={16} color={colors.palette.neutral800} />
        </TouchableOpacity>

        <Text style={styles.quantityText}>{item.quantity}</Text>

        <TouchableOpacity
          style={styles.quantityButton}
          onPress={() => updateQuantity(item.id, item.quantity + 1)}
        >
          <Icon icon="plus" size={16} color={colors.palette.neutral800} />
        </TouchableOpacity>
      </View>

      <TouchableOpacity style={styles.removeButton} onPress={() => removeItem(item.id)}>
        <Icon icon="bin" size={24} color={colors.palette.neutral800} />
      </TouchableOpacity>
    </View>
  )

  return (
    <View style={styles.root}>
      <Header
        LeftActionComponent={
          <TouchableOpacity
            style={{ flexDirection: "row", alignItems: "center", paddingHorizontal: spacing.md }}
            onPress={() => navigation.goBack()}
          >
            <Icon icon="backicon" size={28} color={colors.palette.neutral900} />
            <Text style={styles.title}>Mon Panier</Text>
          </TouchableOpacity>
        }
        RightActionComponent={
          items.length > 0 ? (
            <TouchableOpacity style={styles.clearAllContainer} onPress={handleShowClearCartModal}>
              <Icon icon="bin" size={25} color={colors.palette.neutral900} />
              <Text style={styles.clearAllText}>Effacer tout</Text>
            </TouchableOpacity>
          ) : undefined
        }
      />
      <View style={styles.screenContent}>
        {items.length === 0 ? (
          <View style={styles.emptyContainer}>
            <View style={styles.emptyContentContainer}>
              <Icon icon="shoppingBag" size={80} color={colors.palette.neutral400} />
              <Text style={styles.emptyText}>Votre panier est vide</Text>
              <Button
                text="Continuer les achats"
                preset="default"
                style={styles.continueButton}
                onPress={() => navigation.goBack()}
              />
            </View>
          </View>
        ) : (
          <FlatList
            data={items}
            renderItem={renderCartItem}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.listContent}
          />
        )}
      </View>

      {/* Footer is always visible when there are items */}
      {items.length > 0 && (
        <View style={styles.footer}>
          <View style={styles.totalContainer}>
            <Text style={styles.totalText}> {items.length} Article(s)</Text>
            {Object.entries(totals).map(([currency, amount]) => (
              <Text key={currency} style={styles.totalText}>
                {amount.toLocaleString()} {currency}
              </Text>
            ))}
          </View>

          <Button
            text={"Passer à la caisse"}
            preset="reversed"
            style={styles.checkoutButton}
            onPress={handleCheckout}
          />
        </View>
      )}

      {/* Clear Cart Confirmation Modal */}
      <Modal visible={isClearCartModalVisible} animationType="fade" transparent>
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalIconContainer}>
              <Icon icon="warning" size={60} color="#FFC107" />
            </View>
            <Text style={styles.modalTitle}>
              Vous êtes sur le point de supprimer votre commande
            </Text>
            <Text style={styles.modalMessage}>
              Une fois supprimée, elle disparaîtra pour toujours. Voulez-vous supprimer
              définitivement votre commande ?
            </Text>
            <Button
              text="Oui, supprimer ma commande"
              style={styles.modalDeleteButton}
              onPress={handleConfirmClearCart}
              // preset="destructiveReversed"
            />
            <TouchableOpacity style={styles.modalGoBackButton} onPress={handleCancelClearCart}>
              <Text style={styles.modalGoBackText}>Retour</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  )
}

const styles = StyleSheet.create({
  cartItem: {
    alignItems: "center",
    backgroundColor: colors.palette.neutral100,
    borderRadius: 8,
    elevation: 2,
    flexDirection: "row",
    marginVertical: spacing.xs,
    padding: spacing.sm,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  checkoutButton: {
    backgroundColor: colors.palette.neutral900,
    borderRadius: 16,
    elevation: 3,
    paddingVertical: spacing.md,
    shadowColor: colors.palette.neutral900,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    width: "100%",
  },
  clearAllContainer: {
    alignItems: "center",
    flexDirection: "row",
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
  },
  clearAllText: {
    color: colors.palette.neutral800,
    fontSize: 15,
    paddingHorizontal: spacing.xs,
    textAlign: "center",
  },
  continueButton: {
    minWidth: 200,
  },
  emptyContainer: {
    alignItems: "center",
    flex: 1,
    justifyContent: "center",
  },
  emptyContentContainer: {
    alignItems: "center",
    justifyContent: "center",
    padding: spacing.xl,
  },
  emptyText: {
    color: colors.palette.neutral600,
    fontSize: 18,
    marginBottom: spacing.xl,
    marginTop: spacing.lg,
    textAlign: "center",
  },
  footer: {
    position: "absolute", // Stick to the bottom
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: colors.palette.neutral100,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderTopWidth: StyleSheet.hairlineWidth,
    borderTopColor: colors.palette.neutral300,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
  },
  itemDescription: {
    color: colors.palette.neutral600,
    fontSize: 14,
    marginTop: spacing.xs,
  },
  itemDetails: {
    flex: 1,
  },
  itemName: {
    color: colors.palette.neutral900,
    fontSize: 16,
    fontWeight: "bold",
  },
  itemPrice: {
    color: colors.palette.primary600,
    fontSize: 15,
    fontWeight: "bold",
    marginTop: spacing.xs,
  },
  listContent: {
    paddingBottom: 100,
    paddingHorizontal: spacing.md, // Adjust to ensure last item isn't hidden by footer
  },
  quantityButton: {
    alignItems: "center",
    backgroundColor: colors.palette.neutral200,
    borderRadius: 15,
    height: 30,
    justifyContent: "center",
    width: 30,
  },
  quantityContainer: {
    alignItems: "center",
    flexDirection: "row",
    marginRight: spacing.sm,
  },
  quantityText: {
    fontSize: 16,
    fontWeight: "bold",
    marginHorizontal: spacing.sm,
    minWidth: 20,
    textAlign: "center",
  },
  removeButton: {
    padding: spacing.xs,
  },
  root: {
    // backgroundColor: colors.background,
    flex: 1,
  },
  screen: {
    flex: 1, // Ensure the Screen component takes full height
  },
  screenContent: {
    flex: 1, // Make the content area take up available space
  },
  title: {
    color: colors.palette.neutral800,
    fontSize: 18,
    paddingHorizontal: spacing.sm,
    textAlign: "center",
  },
  totalContainer: {
    marginBottom: spacing.sm,
    flexDirection: "row",
    justifyContent: "space-between",
  },
  totalText: {
    color: colors.palette.neutral900,
    fontSize: 18,
    fontWeight: "bold",
    textAlign: "right",
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  modalContainer: {
    backgroundColor: colors.palette.neutral100,
    borderRadius: 12,
    padding: spacing.lg,
    alignItems: "center",
    width: "80%",
  },
  modalIconContainer: {
    backgroundColor: "#FFF3CD", // Light yellow background for warning
    borderRadius: 50,
    padding: spacing.md,
    marginBottom: spacing.md,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: colors.palette.neutral900,
    textAlign: "center",
    marginBottom: spacing.sm,
  },
  modalMessage: {
    fontSize: 16,
    color: colors.palette.neutral800,
    textAlign: "center",
    marginBottom: spacing.lg,
  },
  modalDeleteButton: {
    width: "100%",
    marginBottom: spacing.sm,
  },
  modalGoBackButton: {
    paddingVertical: spacing.md,
  },
  modalGoBackText: {
    color: colors.palette.neutral700,
    fontSize: 16,
    fontWeight: "bold",
  },
})

/* eslint-disable @typescript-eslint/no-unused-vars */
import { FC, useEffect, useState, useCallback, useMemo } from "react"
import {
  ViewStyle,
  TextStyle,
  View,
  SectionList,
  TouchableOpacity,
  ActivityIndicator,
  Modal,
  ImageStyle,
} from "react-native"
import { AppStackScreenProps } from "@/navigators"
import { Header, Screen, Text, Icon } from "@/components"
import { DailySummaryCard } from "@/components/business/DailySummaryCard"
import { useStores } from "@/store/rootStore"
import { colors, spacing } from "@/theme"
import { BusinessTransaction } from "@/store/BusinessTransactionStore"
import { formatDateTime } from "@/utils/dateUtils"
import { getStatusConfig, getStatusOnlyIcon } from "@/utils/actions"
import React from "react"

interface BusinessTransactionScreenProps extends AppStackScreenProps<"BusinessTransaction"> {}

// Transaction details modal props
interface TransactionDetailsModalProps {
  visible: boolean
  transaction: BusinessTransaction | null 
  onClose: () => void
}

// Transaction Details Modal Component
const TransactionDetailsModal = ({
  visible,
  transaction,
  onClose,
}: TransactionDetailsModalProps) => {
  if (!transaction) return null

  const formatAmount = (amount: string) => {
    const formattedAmount = parseFloat(amount).toLocaleString("fr-FR", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })
    return `FC ${formattedAmount}`
  }

  const getTransactionTypeLabel = (type: string, action: string) => {
    if (action === "deposit") return "Entrée"
    if (action === "withdraw") return "Sortie"

    switch (type) {
      case "transfer":
        return "Transfert"
      case "service":
        return "Service"
      default:
        return type
    }
  }

  return (
    <Modal visible={visible} transparent animationType="slide" onRequestClose={onClose}>
      <View style={$modalOverlay}>
        <View style={$modalContent}>
          {/* Header */}
          <View style={$modalHeader}>
            <View style={$modalHeaderContent}>
              {/* <Icon
                icon={transaction.transaction_action === "deposit" ? "increase" : "decrease"}
                size={32}
                color={
                  transaction.transaction_action === "deposit"
                    ? colors.palette.neutral900
                    : colors.palette.angry500
                }
                style={$headerIcon}
              /> */}
              <View>
                <Text style={$modalTitle}>Détails de la transaction</Text>
                <Text style={$modalSubtitle}>{transaction.id}</Text>
              </View>
            </View>
            <TouchableOpacity onPress={onClose} style={$closeButton}>
              <Icon icon="x" size={24} color={colors.palette.neutral800} />
            </TouchableOpacity>
          </View>

          {/* Amount Section */}
          <View style={$amountSection}>
            <Text
              style={[
                $amountText,
                {
                  color: colors.palette.neutral800,
                },
              ]}
            >
              {formatAmount(transaction.amount)}
            </Text>
            <Text style={$dateText}>{formatDateTime(transaction.created_at)}</Text>
          </View>

          {/* Details Section */}
          <View style={$detailsContainer}>
            <View style={$detailRow}>
              <Text style={$detailLabel}>Transaction</Text>
              <Text style={$detailValue}>
                {getTransactionTypeLabel(
                  transaction.transaction_type,
                  transaction.transaction_action,
                )}
              </Text>
            </View>

            <View style={$detailRow}>
              <Text style={$detailLabel}>Statut</Text>
              <View style={$statusBadgeContainer}>
                <View style={$statusBadge}>
                  <Icon
                    icon={getStatusOnlyIcon(transaction.status).icon}
                    size={14}
                    color={getStatusConfig(transaction.status).color}
                    style={$statusIcon}
                  />
                  <Text style={$statusText}>{transaction.status}</Text>
                </View>
              </View>
            </View>

            {transaction.sender && (
              <View style={$detailRow}>
                <Text style={$detailLabel}>Expéditeur</Text>
                <Text style={$detailValue}>{transaction.sender}</Text>
              </View>
            )}

            {transaction.receiver && (
              <View style={$detailRow}>
                <Text style={$detailLabel}>Destinataire</Text>
                <Text style={$detailValue}>{transaction.receiver}</Text>
              </View>
            )}

            {transaction.note && (
              <View style={$detailRow}>
                <Text style={$detailLabel}>Note</Text>
                <Text style={$detailValue}>{transaction.note}</Text>
              </View>
            )}
          </View>
        </View>
      </View>
    </Modal>
  )
}

export const BusinessTransactionScreen: FC<BusinessTransactionScreenProps> = () => {
  const { businessTransactions } = useStores()
  const {
    transactions,
    loading,
    error,
    dailySummary,
    fetchBusinessTransactions,
    fetchDailySummary,
  } = businessTransactions

  const [refreshing, setRefreshing] = useState(false)
  const [selectedTransaction, setSelectedTransaction] = useState<BusinessTransaction | null>(null)
  const [isModalVisible, setIsModalVisible] = useState(false)

  // Fetch data when the component mounts
  useEffect(() => {
    fetchBusinessTransactions()
    fetchDailySummary()
  }, [fetchBusinessTransactions, fetchDailySummary])

  const handleRefresh = useCallback(async () => {
    setRefreshing(true)
    await Promise.all([fetchBusinessTransactions(), fetchDailySummary()])
    setRefreshing(false)
  }, [fetchBusinessTransactions, fetchDailySummary])

  // Group transactions by date
  const groupedTransactions = useMemo(() => {
    if (!transactions || !Array.isArray(transactions)) {
      return []
    }

    return transactions.reduce((acc: any[], transaction: BusinessTransaction) => {
      const date = new Date(transaction.created_at).toLocaleDateString("fr-FR", {
        year: "numeric",
        month: "long",
        day: "numeric",
      })

      const existingGroup = acc.find((group) => group.title === date)

      if (existingGroup) {
        existingGroup.data.push(transaction)
      } else {
        acc.push({
          title: date,
          data: [transaction],
        })
      }

      return acc
    }, [])
  }, [transactions])

  // Add isLastInSection flag to each transaction
  const modifiedSections = useMemo(() => {
    return groupedTransactions?.map((section) => ({
      ...section,
      data: section.data.map((item: BusinessTransaction, index: number) => ({
        ...item,
        isLastInSection: index === section.data.length - 1,
      })),
    }))
  }, [groupedTransactions])

  // console.log("tr", transactions)

  // Handle transaction press
  const handleTransactionPress = (transaction: BusinessTransaction) => {
    setSelectedTransaction(transaction)
    setIsModalVisible(true)
  }

  // Render a transaction item
  const renderTransaction = ({
    item,
  }: {
    item: BusinessTransaction & { isLastInSection?: boolean }
  }) => {
    const isCredit = item.transaction_type === "deposit"
    const amount = parseFloat(item.amount)
    const formattedAmount = amount.toLocaleString("fr-FR", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })

    return (
      <TouchableOpacity
        style={[$transactionItem, item.isLastInSection && $transactionItemLastInSection]}
        onPress={() => handleTransactionPress(item)}
      >
        <View style={$statusIndicator}>
          <Icon icon={"transfer"} size={24} color={colors.palette.neutral400} />
        </View>
        <View style={$transactionContent}>
          <Text style={$description}>{item.id}</Text>
          <Text style={$recipient}>
            {item.note || (isCredit ? item.sender : item.receiver) || "N/A"}
          </Text>
        </View>
        <View style={$amountContainer}>
          <Text
            style={[
              $amount,
              {
                color: colors.palette.neutral800,
              },
            ]}
          >
            FC {formattedAmount}
          </Text>
          <Text style={$timestamp}>
            {new Date(item.created_at).toLocaleTimeString("fr-FR", {
              hour: "2-digit",
              minute: "2-digit",
            })}
          </Text>
        </View>
      </TouchableOpacity>
    )
  }

  // Render section header
  const renderSectionHeader = ({ section: { title } }: any) => (
    <View style={$sectionHeader}>
      <Text style={$sectionHeaderText}>{title}</Text>
    </View>
  )

  // Show loading state
  if (loading && !refreshing && (!transactions || transactions.length === 0)) {
    return (
      <>
        <Header title="Historique" />
        <Screen style={$root} statusBarStyle="dark">
          <View style={$loadingContainer}>
            <ActivityIndicator size="large" color={colors.palette.primary500} />
            <Text style={$loadingText}>Chargement des transactions...</Text>
          </View>
        </Screen>
      </>
    )
  }

  return (
    <>
      <Header title="Historique" />
      <Screen style={$root} statusBarStyle="dark" preset="fixed">
        {/* Daily Summary Card */}
        <DailySummaryCard summary={dailySummary} isLoading={loading} />

        {/* Transaction List */}
        {!transactions || transactions.length === 0 ? (
          <View style={$emptyContainer}>
            <Icon icon="nocontent" size={64} color={colors.palette.neutral400} />
            <Text style={$emptyText}>Aucune transaction disponible</Text>
          </View>
        ) : (
          <SectionList
            sections={modifiedSections}
            renderItem={renderTransaction}
            renderSectionHeader={renderSectionHeader}
            keyExtractor={(item) => item.id}
            contentContainerStyle={$listContent}
            stickySectionHeadersEnabled
            onRefresh={handleRefresh}
            refreshing={refreshing}
          />
        )}

        {/* Error message if needed */}
        {error && <Text text={error} style={$errorText} />}

        {/* Transaction Details Modal */}
        <TransactionDetailsModal
          visible={isModalVisible}
          transaction={selectedTransaction}
          onClose={() => {
            setIsModalVisible(false)
            setSelectedTransaction(null)
          }}
        />
      </Screen>
    </>
  )
}

const $root: ViewStyle = {
  flex: 1,
  // backgroundColor: colors.palette.neutral100,
}

const $errorText: TextStyle = {
  color: colors.error,
  textAlign: "center",
  margin: 10,
}

const $listContent: ViewStyle = {
  paddingHorizontal: spacing.md,
  paddingBottom: spacing.xl * 2,
}

const $sectionHeader: ViewStyle = {
  backgroundColor: colors.palette.neutral200,
  paddingVertical: spacing.xs,
  paddingHorizontal: spacing.sm,
  marginVertical: spacing.xs,
  borderTopLeftRadius: 12,
  borderTopRightRadius: 12,
}

const $sectionHeaderText: TextStyle = {
  fontSize: 14,
  fontWeight: "600",
  color: colors.palette.neutral700,
}

const $transactionItem: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  paddingVertical: spacing.sm,
  paddingHorizontal: spacing.sm,
  backgroundColor: colors.palette.neutral100,
  marginVertical: 1,
  borderBottomWidth: 1,
  borderBottomColor: colors.palette.neutral200,
}

const $transactionItemLastInSection: ViewStyle = {
  borderBottomLeftRadius: 12,
  borderBottomRightRadius: 12,
  marginBottom: spacing.sm,
}

const $statusIndicator: ViewStyle = {
  width: 36,
  height: 36,
  borderRadius: 18,
  backgroundColor: colors.palette.neutral200,
  justifyContent: "center",
  alignItems: "center",
  marginRight: spacing.sm,
}

const $transactionContent: ViewStyle = {
  flex: 1,
}

const $amountContainer: ViewStyle = {
  alignItems: "flex-end",
}

const $description: TextStyle = {
  fontSize: 14,
  color: colors.palette.neutral800,
  fontWeight: "600",
}

const $recipient: TextStyle = {
  fontSize: 14,
  color: colors.palette.accent500,
}

const $amount: TextStyle = {
  fontSize: 14,
  fontWeight: "bold",
  marginBottom: spacing.xxs,
}

const $timestamp: TextStyle = {
  fontSize: 12,
  color: colors.palette.neutral600,
}

const $loadingContainer: ViewStyle = {
  flex: 1,
  justifyContent: "center",
  alignItems: "center",
  padding: spacing.xl,
}

const $loadingText: TextStyle = {
  color: colors.palette.neutral600,
  marginTop: spacing.md,
}

const $emptyContainer: ViewStyle = {
  flex: 1,
  justifyContent: "center",
  alignItems: "center",
  padding: spacing.xl,
}

const $emptyText: TextStyle = {
  color: colors.palette.neutral600,
  marginTop: spacing.md,
  textAlign: "center",
}

// Modal styles
const $modalOverlay: ViewStyle = {
  flex: 1,
  backgroundColor: "rgba(0, 0, 0, 0.5)",
  justifyContent: "flex-end",
}

const $modalContent: ViewStyle = {
  backgroundColor: colors.palette.neutral100,
  borderTopLeftRadius: 20,
  borderTopRightRadius: 20,
  padding: spacing.lg,
  maxHeight: "80%",
}

const $modalHeader: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
  marginBottom: spacing.md,
}

const $modalHeaderContent: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
}

const $headerIcon = {
  marginRight: spacing.sm,
}

const $modalTitle: TextStyle = {
  fontSize: 20,
  fontWeight: "bold",
  color: colors.palette.neutral800,
}

const $modalSubtitle: TextStyle = {
  fontSize: 14,
  color: colors.palette.neutral600,
}

const $closeButton: ViewStyle = {
  padding: spacing.xs,
}

const $amountSection: ViewStyle = {
  backgroundColor: colors.palette.neutral300,
  padding: spacing.md,
  borderRadius: 12,
  marginBottom: spacing.md,
}

const $amountText: TextStyle = {
  fontSize: 24,
  fontWeight: "bold",
  marginBottom: spacing.xs,
}

const $dateText: TextStyle = {
  fontSize: 14,
  color: colors.palette.neutral600,
}

const $detailsContainer: ViewStyle = {
  gap: spacing.sm,
}

const $detailRow: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
  paddingVertical: spacing.xs,
  borderBottomWidth: 1,
  borderBottomColor: colors.palette.neutral200,
}

const $detailLabel: TextStyle = {
  fontSize: 14,
  color: colors.palette.neutral600,
}

const $detailValue: TextStyle = {
  fontSize: 14,
  color: colors.palette.neutral800,
  fontWeight: "500",
  flex: 1,
  textAlign: "right",
}

const $statusBadgeContainer: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
}

const $statusBadge: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  paddingVertical: spacing.xs,
  borderRadius: 16,
}

const $statusIcon = {
  marginRight: spacing.xs,
}

const $statusText: TextStyle = {
  fontWeight: "600",
}

/* eslint-disable no-restricted-imports */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable react-native/no-color-literals */
/* eslint-disable react-native/sort-styles */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable react-hooks/exhaustive-deps */
import React, { FC, useEffect, useState, useRef } from "react"
import {
  View,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  Image,
  RefreshControl,
  Modal,
  ScrollView,
  Share,
} from "react-native"
import { AppStackScreenProps } from "@/navigators"
import { Screen, Text, Icon, Header } from "@/components"
import { colors, spacing } from "@/theme"
import { useStores } from "@/store"
import type { Invoice } from "@/services/api/api.types"
import { format } from "date-fns"
import { fr } from "date-fns/locale"
import { getSummary, formatCurrency } from "@/utils/invoiceUtils"

interface InvoiceScreenProps extends AppStackScreenProps<"Invoice"> {}

// Status badge component
interface StatusBadgeProps {
  status: string
}

const StatusBadge: FC<StatusBadgeProps> = ({ status }) => {
  let backgroundColor: any = colors.palette.neutral300
  let textColor: any = colors.palette.neutral800
  let statusText = status || "En attente"

  switch (status?.toLowerCase()) {
    case "paid":
    case "completed":
    case "success":
      backgroundColor = "#E6F4EA"
      textColor = "#34A853"
      statusText = "Payée"
      break
    case "pending":
    case "processing":
      backgroundColor = "#FEF7E0"
      textColor = "#FBBC04"
      statusText = "En attente"
      break
    case "cancelled":
    case "failed":
      backgroundColor = "#FDECEA"
      textColor = "#EA4335"
      statusText = "Annulée"
      break
  }

  return (
    <View style={[styles.statusBadge, { backgroundColor }]}>
      <Text style={[styles.statusText, { color: textColor }]}>{statusText}</Text>
    </View>
  )
}

// Summary card component
interface SummaryCardProps {
  invoices: Invoice[]
  businessName?: string
}

const SummaryCard: FC<SummaryCardProps> = ({ invoices, businessName }) => {
  // Get invoice summary using the utility function
  const summary = getSummary(invoices)

  // Extract values from summary
  const {
    pendingCount,
    completedCount,
    canceledCount,
    pendingAmount,
    completedAmount,
    canceledAmount,
    totalAmount,
    currency,
  } = summary

  return (
    <View style={styles.summaryCard}>
      <View style={styles.summaryHeader}>
        <View style={styles.totalAmountContainer}>
          <Text style={styles.totalAmountLabel}>Montant total:</Text>
          <Text style={styles.totalAmountValue}>{formatCurrency(totalAmount, currency)}</Text>
        </View>

        {/* Additional summary information */}
        <View style={styles.detailedSummaryContainer}>
          <View style={styles.detailedSummaryRow}>
            <Text style={styles.detailedSummaryLabel}>Payées:</Text>
            <Text style={styles.detailedSummaryValue}>
              {formatCurrency(completedAmount, currency)}
            </Text>
          </View>
          <View style={styles.detailedSummaryRow}>
            <Text style={styles.detailedSummaryLabel}>En attente:</Text>
            <Text style={styles.detailedSummaryValue}>
              {formatCurrency(pendingAmount, currency)}
            </Text>
          </View>
          <View style={styles.detailedSummaryRow}>
            <Text style={styles.detailedSummaryLabel}>Annulées:</Text>
            <Text style={styles.detailedSummaryValue}>
              {formatCurrency(canceledAmount, currency)}
            </Text>
          </View>
        </View>
      </View>

      <View style={styles.summaryContent}>
        <View style={styles.summaryItem}>
          <View style={[styles.summaryIconContainer, styles.paidIconContainer]}>
            <Icon icon="check" size={20} color="#34A853" />
          </View>
          <View style={styles.summaryTextContainer}>
            <Text style={styles.summaryItemCount}>{completedCount}</Text>
            <Text style={styles.summaryItemLabel}>Payées</Text>
          </View>
        </View>

        <View style={styles.summaryItem}>
          <View style={[styles.summaryIconContainer, styles.pendingIconContainer]}>
            <Icon icon="caretRight" size={20} color="#FBBC04" />
          </View>
          <View style={styles.summaryTextContainer}>
            <Text style={styles.summaryItemCount}>{pendingCount}</Text>
            <Text style={styles.summaryItemLabel}>En attente</Text>
          </View>
        </View>

        <View style={styles.summaryItem}>
          <View style={[styles.summaryIconContainer, styles.cancelledIconContainer]}>
            <Icon icon="x" size={20} color={colors.palette.angry500} />
          </View>
          <View style={styles.summaryTextContainer}>
            <Text style={styles.summaryItemCount}>{canceledCount}</Text>
            <Text style={styles.summaryItemLabel}>Annulées</Text>
          </View>
        </View>
      </View>
    </View>
  )
}

// Invoice item component
interface InvoiceItemProps {
  invoice: Invoice
  onPress: (invoice: Invoice) => void
}

const InvoiceItem: FC<InvoiceItemProps> = ({ invoice, onPress }) => {
  // Format date
  const formattedDate = invoice.created_at
    ? format(new Date(invoice.created_at), "d MMM yyyy", { locale: fr })
    : "Date inconnue"

  // Get invoice ID (either invoice_no or id)
  const invoiceId = invoice.referance_id

  // Get amount (either total_amount or amount)
  let amount = "0"
  let currencySymbol = "FC"

  if (invoice.amount) {
    amount = invoice.amount
  }

  if (invoice.currency) {
    if (typeof invoice.currency === "number") {
      // If currency is a number, assume 1 = USD, 2 = FC
      currencySymbol = invoice.currency === 1 ? "USD" : "FC"
    } else {
      currencySymbol = invoice.currency
    }
  }

  // console.log('d', invoice)

  // Get number of items
  const itemCount = Array.isArray(invoice.items) ? invoice.items.length : 0

  return (
    <TouchableOpacity
      style={styles.invoiceItem}
      onPress={() => onPress(invoice)}
      activeOpacity={0.7}
    >
      <View style={styles.invoiceItemContent}>
        <View style={styles.invoiceItemLeft}>
          <Text style={styles.invoiceId}>{invoiceId}</Text>
          <Text style={styles.invoiceDate}>{formattedDate}</Text>
          <View style={styles.invoiceItemsContainer}>
            <Icon icon="shoppingBag" size={12} color={colors.palette.neutral600} />
            <Text style={styles.invoiceItemsCount}>{itemCount} Article(s)</Text>
          </View>
        </View>

        <View style={styles.invoiceItemRight}>
          <Text style={styles.invoiceAmount}>
            {amount} {currencySymbol}
          </Text>
          <Text style={styles.paymentMethod}>{invoice.payment_method || ""}</Text>
          <StatusBadge status={invoice.status || "pending"} />
        </View>
      </View>
    </TouchableOpacity>
  )
}

// Empty state component
const EmptyState = ({ onRefresh }: { onRefresh: () => void }) => (
  <View style={styles.emptyContainer}>
    <Icon icon="invoice" size={60} color={colors.palette.neutral400} />
    <Text style={styles.emptyText}>Aucune facture trouvée</Text>
    <TouchableOpacity style={styles.refreshButton} onPress={onRefresh}>
      <Text style={styles.refreshButtonText}>Actualiser</Text>
    </TouchableOpacity>
  </View>
)

// Error state component
const ErrorState = ({ error, onRetry }: { error: string; onRetry: () => void }) => (
  <View style={styles.errorContainer}>
    <Icon icon="x" size={24} color={colors.palette.angry500} />
    <Text style={styles.errorText}>{error}</Text>
    <TouchableOpacity style={styles.refreshButton} onPress={onRetry}>
      <Text style={styles.refreshButtonText}>Réessayer</Text>
    </TouchableOpacity>
  </View>
)

// Loading state with delay
const LoadingState = () => (
  <View style={styles.loadingContainer}>
    <ActivityIndicator size="large" color={colors.palette.primary500} />
    <Text style={styles.loadingText}>Chargement des factures...</Text>
  </View>
)

// Invoice Detail Modal component
interface InvoiceDetailModalProps {
  visible: boolean
  invoice: Invoice | null
  onClose: () => void
}

const InvoiceDetailModal: FC<InvoiceDetailModalProps> = ({ visible, invoice, onClose }) => {
  // Early return if no invoice
  if (!invoice) return null

  // Format date
  const formattedDate = invoice.created_at
    ? format(new Date(invoice.created_at), "d MMMM yyyy à HH:mm", { locale: fr })
    : "Date inconnue"

  // Get currency symbol
  let currencySymbol = "FC"
  if (invoice.currency) {
    if (typeof invoice.currency === "number") {
      currencySymbol = invoice.currency === 1 ? "USD" : "FC"
    } else {
      currencySymbol = invoice.currency
    }
  }

  // Calculate total amount
  const totalAmount = invoice.amount || "0"

  // Handle share receipt
  const shareReceipt = async () => {
    try {
      const businessName = "Votre Entreprise" // Replace with actual business name
      const invoiceId = invoice.referance_id || invoice.id

      // Create a formatted receipt text
      let receiptText = `REÇU DE FACTURE\n\n`
      receiptText += `Facture #: ${invoiceId}\n`
      receiptText += `Date: ${formattedDate}\n`
      receiptText += `Statut: ${invoice.status || "En attente"}\n\n`

      // Add items
      receiptText += `ARTICLES:\n`
      if (Array.isArray(invoice.items) && invoice.items.length > 0) {
        invoice.items.forEach((item, index) => {
          receiptText += `${index + 1}. ${item.product || "Article"} (${item.qt || 1} x ${item.amount || 0} ${currencySymbol})\n`
          if (item.description) {
            receiptText += `   Description: ${item.description}\n`
          }
        })
      } else {
        receiptText += "Aucun article détaillé\n"
      }

      receiptText += `\nMontant total: ${totalAmount} ${currencySymbol}\n`
      receiptText += `\nMerci pour votre achat!`

      await Share.share({
        message: receiptText,
        title: `Facture #${invoiceId}`,
      })
    } catch (error) {
      console.error("Erreur lors du partage:", error)
    }
  }

  return (
    <Modal visible={visible} transparent animationType="slide" onRequestClose={onClose}>
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          {/* Header */}
          <View style={styles.modalHeader}>
            <View style={styles.modalHeaderContent}>
              <Icon
                icon="invoice"
                size={32}
                color={colors.palette.primary500}
                style={styles.headerIcon}
              />
              <View>
                <Text style={styles.modalTitle}>Détails de la facture</Text>
                <Text style={styles.modalSubtitle}>{invoice.referance_id || invoice.id}</Text>
              </View>
            </View>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Icon icon="x" size={24} color={colors.palette.neutral800} />
            </TouchableOpacity>
          </View>

          {/* Receipt Content */}
          <ScrollView style={styles.modalBody}>
            {/* Status and Date */}
            <View style={styles.receiptSection}>
              <View style={styles.receiptRow}>
                <Text style={styles.receiptLabel}>Statut:</Text>
                <StatusBadge status={invoice.status || "pending"} />
              </View>
              <View style={styles.receiptRow}>
                <Text style={styles.receiptLabel}>Date:</Text>
                <Text style={styles.receiptValue}>{formattedDate}</Text>
              </View>
              <View style={styles.receiptRow}>
                <Text style={styles.receiptLabel}>Méthode:</Text>
                <Text style={styles.receiptValue}>
                  {invoice.payment_method === "scan" ? "QR Code" : invoice.payment_method || "N/A"}
                </Text>
              </View>
            </View>
            {["paid", "completed", "success"].includes(invoice.status?.toLowerCase() || "") ? (
              <View style={styles.qrSection}>
                <Icon
                  icon="Rcheck"
                  size={50}
                  color={colors.palette.primary500}
                  style={styles.headerIcon}
                />
              </View>
            ) : (
              <>
                {invoice.temp_qr && (
                  <View style={styles.qrSection}>
                    <Image
                      source={{ uri: invoice.temp_qr }}
                      style={styles.qrCode}
                      resizeMode="contain"
                    />
                  </View>
                )}
              </>
            )}

            {/* Divider */}
            <View style={styles.receiptDivider} />
            {/* QR Code if available */}

            {/* Items */}
            <View style={styles.receiptSection}>
              <Text style={styles.receiptSectionTitle}>Articles</Text>

              {Array.isArray(invoice.items) && invoice.items.length > 0 ? (
                invoice.items.map((item, index) => (
                  <View key={index} style={styles.itemCard}>
                    <View style={styles.itemHeader}>
                      <Text style={styles.itemName}>{item.product || `Article ${index + 1}`}</Text>
                      <Text style={styles.itemPrice}>
                        {item.amount || 0} {currencySymbol}
                      </Text>
                    </View>

                    {/* {item.description && (
                      <Text style={styles.itemDescription}>{item.description}</Text>
                    )} */}

                    <View style={styles.itemFooter}>
                      <Text style={styles.itemQuantity}>Quantité: {item.qt || 1}</Text>
                      <Text style={styles.itemTotal}>
                        Total: {(item.qt || 1) * (item.amount || 0)} {currencySymbol}
                      </Text>
                    </View>
                  </View>
                ))
              ) : (
                <Text style={styles.noItemsText}>Aucun article détaillé</Text>
              )}
            </View>

            {/* Divider */}
            <View style={styles.receiptDivider} />
          </ScrollView>

          {/* Footer */}
          <View style={styles.modalFooter}>
            <View style={styles.receiptSection}>
              <View style={styles.totalRow}>
                <Text style={styles.totalLabel}>Montant total:</Text>
                <Text style={styles.totalValue}>
                  {totalAmount} {currencySymbol}
                </Text>
              </View>
            </View>
            {/* <TouchableOpacity style={styles.shareButton} onPress={shareReceipt}>
              <Icon icon="share" size={20} color={colors.palette.neutral100} />
              <Text style={styles.footerButtonText}>Partager le reçu</Text>
            </TouchableOpacity> */}
          </View>
        </View>
      </View>
    </Modal>
  )
}

export const InvoiceScreen: FC<InvoiceScreenProps> = ({ navigation }) => {
  // Get the invoice store
  const {
    invoice: invoiceStore,
    business: { businesses },
  } = useStores()

  // Local state
  const [refreshing, setRefreshing] = useState(false)
  const [isDataReady, setIsDataReady] = useState(false)
  const [businessName, setBusinessName] = useState<string>("")
  const [selectedInvoice, setSelectedInvoice] = useState<Invoice | null>(null)
  const [isModalVisible, setIsModalVisible] = useState(false)

  // Refs to prevent infinite loops
  const initialFetchRef = useRef(false)
  const isMountedRef = useRef(true)

  // Debug log
  useEffect(() => {
    // console.log("Current invoices:", invoiceStore.invoices)

    // Set business name if available
    if (invoiceStore.businessName && isMountedRef.current) {
      setBusinessName(invoiceStore.businessName)
    }
  }, [invoiceStore.invoices, invoiceStore.businessName])

  // Fetch invoices on mount (only once)
  useEffect(() => {
    // Set mounted flag
    isMountedRef.current = true

    // Only fetch if we haven't already
    if (!initialFetchRef.current) {
      initialFetchRef.current = true
      fetchInvoices()
    }

    // Cleanup function
    return () => {
      isMountedRef.current = false
    }
  }, [])

  // Set data ready state after loading completes
  useEffect(() => {
    if (!invoiceStore.loading && initialFetchRef.current) {
      // Add a small delay before showing the data
      const timer = setTimeout(() => {
        if (isMountedRef.current) {
          setIsDataReady(true)
        }
      }, 500)

      return () => clearTimeout(timer)
    }
  }, [invoiceStore.loading])

  // Function to fetch invoices using the store
  const fetchInvoices = async () => {
    try {
      setIsDataReady(false)
      // Get the first business ID or use a default
      const businessId = businesses[0]?.business_no || ""
      // console.log("Fetching invoices for business:", businessId)

      await invoiceStore.fetchInvoice(businessId)
    } catch (error) {
      console.error("Error fetching invoices:", error)
    }
  }

  // Handle refresh
  const handleRefresh = async () => {
    if (!isMountedRef.current) return

    setRefreshing(true)
    await fetchInvoices()
    if (isMountedRef.current) {
      setRefreshing(false)
    }
  }

  // Handle invoice press - show modal with details
  const handleInvoicePress = (invoice: Invoice) => {
    // console.log("Invoice pressed:", invoice)
    setSelectedInvoice(invoice)
    setIsModalVisible(true)
  }

  // Handle modal close
  const handleCloseModal = () => {
    setIsModalVisible(false)
    // Add a small delay before clearing the selected invoice
    setTimeout(() => {
      setSelectedInvoice(null)
    }, 300)
  }

  // Render invoice item
  const renderInvoiceItem = ({ item }: { item: Invoice }) => {
    return <InvoiceItem invoice={item} onPress={handleInvoicePress} />
  }

  // Sort invoices by date (newest first)
  const sortInvoicesByDate = (invoices: Invoice[]) => {
    if (!invoices || !Array.isArray(invoices)) return []

    return [...invoices].sort((a, b) => {
      // Convert dates to timestamps for comparison
      const dateA = a.created_at ? new Date(a.created_at).getTime() : 0
      const dateB = b.created_at ? new Date(b.created_at).getTime() : 0

      // Sort in descending order (newest first)
      return dateB - dateA
    })
  }

  // Get sorted invoices
  const sortedInvoices = sortInvoicesByDate(invoiceStore.invoices || [])

  // Debug log to verify sorting
  // useEffect(() => {
  //   if (sortedInvoices && sortedInvoices.length > 0) {
  //     console.log("Sorted invoices by date (newest first):")
  //     sortedInvoices.slice(0, 3).forEach((invoice, index) => {
  //       console.log(
  //         `Invoice ${index + 1}: ${invoice.created_at} - ID: ${invoice.referance_id || invoice.id}`,
  //       )
  //     })
  //   }
  // }, [sortedInvoices])

  // Render list header
  const renderListHeader = () => {
    return (
      <>
        <SummaryCard invoices={invoiceStore.invoices || []} businessName={businessName} />
        <View style={styles.listHeader}>
          <Text style={styles.listHeaderTitle}>Factures récentes</Text>
        </View>
      </>
    )
  }

  return (
    <>
      <Header title="Factures" leftIcon="backicon" onLeftPress={() => navigation.goBack()} />
      <Screen style={styles.root} preset="fixed" statusBarStyle="dark">
        {invoiceStore.loading || !isDataReady ? (
          <LoadingState />
        ) : invoiceStore.error ? (
          <ErrorState error={invoiceStore.error} onRetry={fetchInvoices} />
        ) : (
          <FlatList
            data={sortedInvoices}
            renderItem={renderInvoiceItem}
            keyExtractor={(item) => item?.id || Math.random().toString()}
            contentContainerStyle={styles.listContent}
            ListHeaderComponent={renderListHeader}
            ListEmptyComponent={<EmptyState onRefresh={fetchInvoices} />}
            ItemSeparatorComponent={() => <View style={styles.separator} />}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={handleRefresh}
                colors={[colors.palette.primary500]}
                tintColor={colors.palette.primary500}
              />
            }
          />
        )}
      </Screen>

      {/* Invoice Detail Modal */}
      <InvoiceDetailModal
        visible={isModalVisible}
        invoice={selectedInvoice}
        onClose={handleCloseModal}
      />
    </>
  )
}

const styles = StyleSheet.create({
  errorContainer: {
    // flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: spacing.xl,
  },
  errorText: {
    fontSize: 16,
    color: colors.palette.angry500,
    marginTop: spacing.md,
    marginBottom: spacing.md,
    textAlign: "center",
  },
  root: {
    flex: 1,
    // backgroundColor: colors.background,
  },
  loadingContainer: {
    // flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    marginTop: spacing.sm,
    fontSize: 16,
    color: colors.palette.neutral600,
  },
  listContent: {
    paddingHorizontal: spacing.md,
    paddingBottom: spacing.xl,
  },
  summaryCard: {
    backgroundColor: colors.palette.neutral100,
    borderRadius: 16,
    padding: spacing.md,
    marginVertical: spacing.md,
    shadowColor: colors.palette.neutral900,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  summaryHeader: {
    // marginBottom: spacing.md,
  },
  totalAmountContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    // marginTop: spacing.sm,
    // paddingTop: spacing.sm,
    // borderTopWidth: 1,
    // borderTopColor: colors.palette.neutral300,
  },
  totalAmountLabel: {
    fontSize: 14,
    color: colors.palette.neutral800,
  },
  totalAmountValue: {
    fontSize: 16,
    fontWeight: "bold",
    color: colors.palette.primary500,
  },
  detailedSummaryContainer: {
    marginTop: spacing.sm,
    paddingTop: spacing.sm,
    borderTopWidth: 1,
    borderTopColor: colors.palette.neutral200,
  },
  detailedSummaryRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: spacing.xs / 2,
  },
  detailedSummaryLabel: {
    fontSize: 12,
    color: colors.palette.neutral700,
  },
  detailedSummaryValue: {
    fontSize: 12,
    fontWeight: "500",
    color: colors.palette.neutral800,
  },
  summaryContent: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  qrCode: {
    width: 100,
    height: 100,
    borderRadius: 12,
  },
  summaryItem: {
    flex: 1,
    alignItems: "center",
    padding: spacing.sm,
  },
  summaryIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: spacing.xs,
  },
  paidIconContainer: {
    backgroundColor: "#E6F4EA",
  },
  pendingIconContainer: {
    backgroundColor: "#FEF7E0",
  },
  cancelledIconContainer: {
    backgroundColor: "#FDECEA",
  },
  summaryTextContainer: {
    alignItems: "center",
  },
  summaryItemCount: {
    fontSize: 20,
    fontWeight: "bold",
    color: colors.palette.neutral900,
  },
  summaryItemLabel: {
    fontSize: 12,
    color: colors.palette.neutral600,
    marginTop: spacing.xs / 2,
  },
  listHeader: {
    marginBottom: spacing.sm,
    paddingBottom: spacing.xs,
    borderBottomWidth: 1,
    borderBottomColor: colors.palette.neutral200,
  },
  listHeaderTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: colors.palette.neutral800,
  },
  invoiceItem: {
    paddingVertical: spacing.md,
  },
  invoiceItemContent: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  invoiceItemLeft: {
    flex: 1,
  },
  invoiceId: {
    fontSize: 16,
    fontWeight: "600",
    color: colors.palette.neutral900,
    marginBottom: spacing.xs / 2,
  },
  invoiceDate: {
    fontSize: 14,
    color: colors.palette.neutral600,
    marginBottom: spacing.xs / 2,
  },
  invoiceItemsContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  invoiceItemsCount: {
    fontSize: 12,
    color: colors.palette.neutral600,
    marginLeft: spacing.xs / 2,
  },
  invoiceItemRight: {
    alignItems: "flex-end",
  },
  invoiceAmount: {
    fontSize: 14,
    fontWeight: "500",
    color: colors.palette.neutral800,
    marginTop: spacing.xs / 2,
  },
  paymentMethod: {
    fontSize: 12,
    color: colors.palette.neutral600,
    marginTop: spacing.xs / 2,
  },
  statusBadge: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs / 2,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: "500",
  },
  separator: {
    height: 1,
    backgroundColor: colors.palette.neutral300,
  },
  emptyContainer: {
    padding: spacing.xl,
    alignItems: "center",
    justifyContent: "center",
  },
  emptyText: {
    fontSize: 16,
    color: colors.palette.neutral600,
    marginTop: spacing.md,
    marginBottom: spacing.md,
    textAlign: "center",
  },
  refreshButton: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    backgroundColor: colors.palette.primary200,
    borderRadius: 8,
  },
  refreshButtonText: {
    fontSize: 14,
    color: colors.palette.primary500,
    fontWeight: "500",
  },

  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "flex-end", // Modal slides up from bottom
  },
  modalContent: {
    backgroundColor: colors.palette.neutral100,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: spacing.lg,
    maxHeight: "90%", // Take up to 90% of screen height
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.palette.neutral200,
  },
  modalHeaderContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  headerIcon: {
    marginRight: spacing.sm,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: colors.palette.neutral900,
  },
  modalSubtitle: {
    fontSize: 14,
    color: colors.palette.neutral600,
  },
  closeButton: {
    padding: spacing.xs,
  },
  modalBody: {
    padding: spacing.md,
    maxHeight: "80%",
  },
  receiptSection: {
    // marginBottom: spacing.md,
  },
  receiptRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: spacing.xs,
  },
  receiptLabel: {
    fontSize: 14,
    color: colors.palette.neutral700,
    fontWeight: "500",
  },
  receiptValue: {
    fontSize: 14,
    color: colors.palette.neutral900,
  },
  receiptDivider: {
    height: 1,
    backgroundColor: colors.palette.neutral200,
    marginVertical: spacing.md,
  },
  receiptSectionTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: colors.palette.neutral800,
    marginBottom: spacing.sm,
  },
  itemCard: {
    backgroundColor: colors.palette.neutral200,
    borderRadius: 8,
    padding: spacing.sm,
    marginBottom: spacing.xs,
  },
  itemHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: spacing.xs,
  },
  itemName: {
    fontSize: 14,
    fontWeight: "600",
    color: colors.palette.neutral900,
    flex: 1,
  },
  itemPrice: {
    fontSize: 14,
    fontWeight: "500",
    color: colors.palette.neutral800,
  },

  itemFooter: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: spacing.xs,
  },
  itemQuantity: {
    fontSize: 12,
    color: colors.palette.neutral700,
  },
  itemTotal: {
    fontSize: 12,
    fontWeight: "500",
    color: colors.palette.neutral900,
  },
  noItemsText: {
    fontSize: 14,
    color: colors.palette.neutral600,
    fontStyle: "italic",
    textAlign: "center",
    padding: spacing.md,
  },
  totalRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: spacing.sm,
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: "600",
    color: colors.palette.neutral800,
  },
  totalValue: {
    fontSize: 18,
    fontWeight: "bold",
    color: colors.palette.primary500,
  },
  qrSection: {
    alignItems: "center",
    // marginTop: spacing.md,
    // padding: spacing.md,
    // backgroundColor: colors.palette.neutral200,
    borderRadius: 8,
  },
  modalFooter: {
    padding: spacing.md,
    borderTopWidth: 1,
    borderTopColor: colors.palette.neutral200,
  },
})

import { FC, useState } from "react"
import { ViewStyle, View, TextStyle, Alert, ActivityIndicator } from "react-native"
import { AppStackScreenProps } from "@/navigators"
import { Screen, Text, FencyTextInput, <PERSON><PERSON>, Header, Icon } from "@/components"
import { colors, spacing } from "@/theme"
import { useNavigation } from "@react-navigation/native"
import { Controller, useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { useStores } from "@/store"
import { CreateBusinessProduct } from "@/services/api"
// import { useAppTheme } from "@/utils/useAppTheme"

interface AddProductScreenProps extends AppStackScreenProps<"AddProduct"> {}

// Define the form schema using Zod
const productSchema = z.object({
  name: z.string().min(1, "Le nom du produit est requis"),
  description: z.string().optional(),
  price: z.string().refine((val) => !isNaN(Number(val)) && Number(val) > 0, {
    message: "Le prix doit être un nombre positif",
  }),
})

type ProductFormData = z.infer<typeof productSchema>

export const AddProductScreen: FC<AddProductScreenProps> = () => {
  const {
    business: { businesses, fetchProducts, fetchBusinesses },
  } = useStores()
  const navigation = useNavigation()
  // const { themed } = useAppTheme()
  // Get the main business
  const mainBusiness = businesses.find((business) => business.is_main === true)
  const businessID = mainBusiness?.business_no

  const [isSubmitting, setIsSubmitting] = useState(false)

  // Setup form with validation
  const {
    control,
    handleSubmit,
    formState: { errors },
    // setValue,
    // watch,
  } = useForm<ProductFormData>({
    resolver: zodResolver(productSchema),
    defaultValues: {
      name: "",
      description: "",
      price: "",
    },
  })

  // console.log('', businesses)

  // Handle form submission
  const onSubmit = async (data: ProductFormData) => {
    if (!businessID) {
      Alert.alert("Erreur", "Aucune entreprise sélectionnée")
      return
    }

    setIsSubmitting(true)

    try {
      // Format the data according to the API's expected format
      // The API expects an array of product objects
      const productData = [
        {
          name: data.name,
          price: parseFloat(data.price),
          description: data.description || "",
        },
      ]

      // console.log("Sending product data:", productData)

      // Use the CreateBusinessProduct function with the correct format
      const result = await CreateBusinessProduct(businessID, productData)
      // console.log("API response:", result)

      // Show success message
      if (result.success) {
        // Refresh the business data to get the updated products
        await fetchBusinesses()

        // Then refresh the products list
        await fetchProducts()

        Alert.alert("Succès", "Produit ajouté avec succès", [
          { text: "OK", onPress: () => navigation.goBack() },
        ])
      } else {
        // Show specific error message from API or a fallback message
        const errorMessage =
          result.message || "Échec de la création du produit. Veuillez réessayer."
        Alert.alert("Erreur", errorMessage)
      }
    } catch (error) {
      console.error("Error submitting form:", error)
      Alert.alert("Erreur", "Une erreur s'est produite lors de l'ajout du produit")
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <>
      <Header
        title="Ajouter un produit"
        leftIcon="caretLeft"
        onLeftPress={() => navigation.goBack()}
      />
      <Screen style={$root} preset="scroll" statusBarStyle="dark">
        <View style={$container}>
          {/* Product Image */}
          <View style={$imageContainer}>
            <View style={$imagePlaceholder}>
              <Icon icon="products" size={85} color={colors.palette.neutral500} />
            </View>
          </View>

          {/* Product Name */}
          <Controller
            control={control}
            name="name"
            render={({ field: { onChange, value } }) => (
              <View>
                <FencyTextInput
                  value={value}
                  onChange={onChange}
                  inputname="Nom du produit"
                  placeholder="Entrez le nom du produit"
                  status={errors.name ? "error" : undefined}
                />
                {errors.name && <Text style={$errorText}>{errors.name.message}</Text>}
              </View>
            )}
          />

          {/* Product Description */}
          <Controller
            control={control}
            name="description"
            render={({ field: { onChange, value } }) => (
              <View>
                <FencyTextInput
                  value={value}
                  onChange={onChange}
                  inputname="Description"
                  placeholder="Entrez une description du produit"
                  multiline
                  numberOfLines={4}
                />
              </View>
            )}
          />

          {/* Price in CDF */}
          <Controller
            control={control}
            name="price"
            render={({ field: { onChange, value } }) => (
              <View>
                <FencyTextInput
                  value={value}
                  onChange={onChange}
                  inputname="Prix (FC)"
                  placeholder="Entrez le prix en FC"
                  keyboardType="numeric"
                  status={errors.price ? "error" : undefined}
                />
                {errors.price && <Text style={$errorText}>{errors.price.message}</Text>}
              </View>
            )}
          />

          {/* Submit Button */}
          <Button
            text={isSubmitting ? "Ajout en cours..." : "Ajouter le produit"}
            style={$submitButton}
            disabled={isSubmitting}
            onPress={handleSubmit(onSubmit)}
            preset="filled"
            RightAccessory={isSubmitting ? LoadingIndicator : undefined}
          />
        </View>
      </Screen>
    </>
  )
}

// Loading indicator component for the button
const LoadingIndicator = () => <ActivityIndicator size="small" color={colors.palette.neutral100} />

const $root: ViewStyle = {
  flex: 1,
  // backgroundColor: colors.background,
}

const $container: ViewStyle = {
  flex: 1,
  padding: spacing.md,
  gap: spacing.sm,
}

const $imageContainer: ViewStyle = {
  alignItems: "center",
  marginBottom: spacing.md,
}

const $imagePlaceholder: ViewStyle = {
  width: 150,
  height: 150,
  borderRadius: 10,
  backgroundColor: colors.palette.neutral200,
  justifyContent: "center",
  alignItems: "center",
  borderWidth: 1,
  borderColor: colors.palette.neutral400,
  borderStyle: "dashed",
}

const $errorText: TextStyle = {
  color: colors.error,
  fontSize: 12,
  marginTop: -spacing.xs,
  marginBottom: spacing.xs,
}

const $submitButton: ViewStyle = {
  marginTop: spacing.lg,
  marginBottom: spacing.xl,
}

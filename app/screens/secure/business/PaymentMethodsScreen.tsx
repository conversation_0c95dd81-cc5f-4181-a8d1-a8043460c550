/* eslint-disable react-native/sort-styles */
/* eslint-disable no-restricted-imports */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable react-native/no-color-literals */
/* eslint-disable react-native/no-inline-styles */
/* eslint-disable react-native/no-unused-styles */
import React, { FC, useState, useEffect, useRef } from "react"
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  Modal,
  Image,
  ActivityIndicator,
  ScrollView,
  Alert,
} from "react-native"
import { AppStackScreenProps } from "@/navigators"
import { Screen, Icon, Header, Button } from "@/components"
import { colors, spacing } from "@/theme"
import { useCartStore } from "@/store/CartStore"
import { useStores } from "@/store"
import { confirmCashPayment } from "@/services/api"

interface PaymentMethodsScreenProps extends AppStackScreenProps<"PaymentMethods"> {}

export const PaymentMethodsScreen: FC<PaymentMethodsScreenProps> = ({ navigation }) => {
  const { items, getTotalPrice, clearCart } = useCartStore()
  const [isClearCartModalVisible, setIsClearCartModalVisible] = useState(false)
  const { invoice, business } = useStores()
  const [isProcessing, setIsProcessing] = useState(false)
  // Track which payment method is selected
  const [, setSelectedPaymentMethod] = useState<"scan" | "cash" | null>("scan")
  const [isQrCodeVisible, setIsQrCodeVisible] = useState(false)
  const [isCashModalVisible, setIsCashModalVisible] = useState(false)
  const [isPaymentSuccessful, setIsPaymentSuccessful] = useState(false)
  const [invoiceId, setInvoiceId] = useState<string | null>(null)
  const [invoiceOp, setInvoiceOper] = useState<string | null>(null)
  const [qrCodeUrl, setQrCodeUrl] = useState<string | null>(null)
  const [invoiceData, setInvoiceData] = useState<any>(null)
  const [qrCodePaymentStatus, setQrCodePaymentStatus] = useState<
    "waiting" | "completed" | "failed"
  >("waiting")
  const [isLoadingQrCode, setIsLoadingQrCode] = useState(false)
  const [isLoadingCashPayment, setIsLoadingCashPayment] = useState(false)

  // WebSocket reference to maintain connection
  const webSocketRef = useRef<WebSocket | null>(null)

  const handleCancelClearCart = () => {
    setIsClearCartModalVisible(false)
  }

  const handleShowClearCartModal = () => {
    // if (items.length === 0) return
    setIsClearCartModalVisible(true)
  }

  // Effect to handle WebSocket connection when QR code is displayed
  useEffect(() => {
    // Only establish connection when QR code is visible and we have an invoice ID
    if (isQrCodeVisible && invoiceId && qrCodePaymentStatus === "waiting") {
      console.log("Establishing WebSocket connection for invoice Operator:", invoiceOp)

      // Create WebSocket connection
      const wsUrl = `wss://stageapi.fedha.link/ws/notifications/?receiver=${invoiceOp}`
      const socket = new WebSocket(wsUrl)

      console.log("WebSocket URL:", wsUrl)

      // Store the socket in the ref
      webSocketRef.current = socket

      // Set up event handlers
      socket.onopen = () => {
        console.log("WebSocket connection established")
      }

      socket.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)

          console.log("WebSocket message received:", data)

          // Check if payment is confirmed
          // Handle the nested message format: {"message": {"message": "...", "status": "success"}}
          if (
            (data.message && data.message.status === "success") ||
            data.status === "success" ||
            data.payment_status === "completed" ||
            data.status === "completed"
          ) {
            console.log("Payment confirmed via WebSocket")
            // Automatically trigger payment completion
            handleCompleteQrPayment()
          }
        } catch (error) {
          console.error("Error parsing WebSocket message:", error)
        }
      }

      socket.onerror = (error) => {
        console.error("WebSocket error:", error)
      }

      socket.onclose = () => {
        console.log("WebSocket connection closed")
      }
    }

    // Clean up function to close WebSocket when component unmounts or QR code is hidden
    return () => {
      if (webSocketRef.current) {
        console.log("Closing WebSocket connection")
        webSocketRef.current.close()
        webSocketRef.current = null
      }
    }
  }, [isQrCodeVisible, invoiceId, qrCodePaymentStatus])

  // Calculate totals from cart
  const totals = getTotalPrice()
  const amountDue = Object.values(totals)[0] || 0

  // Get the currency from the cart items (use the first item's currency)
  // Default to "FC" if no items or no currency
  const rawCurrency = items.length > 0 ? items[0].currency : "FC"

  // For display purposes, we'll use "FC", but for API we'll use "CDF"
  const currency = rawCurrency

  const handleSelectPaymentMethod = (method: "scan" | "cash") => {
    setSelectedPaymentMethod(method)

    // Reset data for both payment methods
    setQrCodeUrl(null)
    setInvoiceData(null)
    setInvoiceId(null)

    if (method === "scan") {
      // Show loading indicator for QR code
      setIsLoadingQrCode(true)

      // Start the payment process
      handlePaymentComplete("scan")
      // The QR code modal will be shown only after we get the API response
    } else if (method === "cash") {
      // Show loading indicator for cash payment
      setIsLoadingCashPayment(true)

      // Start the payment process but don't show the modal yet
      // We'll show it after we get the API response
      handlePaymentComplete("cash")
    }
  }

  const handlePaymentComplete = async (paymentMethod: "scan" | "cash") => {
    setIsProcessing(true)
    try {
      // Check if cart is empty
      if (items.length === 0) {
        throw new Error(
          "Le panier est vide. Veuillez ajouter des articles avant de procéder au paiement.",
        )
      }

      // Get the main business
      const mainBusiness = business.businesses.find((b) => b.is_main === true)

      if (!mainBusiness) {
        throw new Error("No main business found")
      }

      // Create an invoice using the InvoiceStore with the appropriate payment method
      const result = await invoice.createInvoice(mainBusiness.business_no, items, paymentMethod)

      if (!result.success) {
        throw new Error(result.message || "Failed to create invoice")
      }

      // console.log("Invoice creation response:", result.data)

      // Check the structure of the response to see where temp_qr might be
      if (result.data) {
        // console.log("Response data structure:", Object.keys(result.data))
        if (result.data.data) {
          console.log("Nested data structure:", Object.keys(result.data.data))
          console.log("Has nested temp_qr?", result.data.data.temp_qr ? "Yes" : "No")
        } else {
          console.log("Has direct temp_qr?", result.data.temp_qr ? "Yes" : "No")
        }
      }

      // Extract the actual invoice data (might be nested)
      const invoiceData = result.data.data ? result.data.data : result.data
      console.log("Using invoice data:", invoiceData)

      // Store the invoice data
      setInvoiceData(invoiceData)
      //
      console.log("Invoice data:", invoiceData.operator)
      // Store the invoice ID for later use
      if (invoiceData) {
        setInvoiceOper(invoiceData.operator)
        // Check if we have a reference ID or regular ID
        if (invoiceData.referance_id) {
          setInvoiceId(invoiceData.referance_id)
        } else if (invoiceData.id) {
          setInvoiceId(invoiceData.id)
        } else if (invoiceData.invoice_no) {
          setInvoiceId(invoiceData.invoice_no)
        }

        // Store the QR code URL if available
        if (invoiceData.temp_qr) {
          console.log("Setting QR code URL:", invoiceData.temp_qr)
          setQrCodeUrl(invoiceData.temp_qr)

          // Only show the QR code modal after we have the QR code URL
          if (paymentMethod === "scan") {
            console.log("Hiding loading indicator and showing QR code modal")
            // Hide loading indicator
            setIsLoadingQrCode(false)
            // Show the QR code modal
            setIsQrCodeVisible(true)
          }
        } else {
          console.log("No temp_qr found in invoice data")
          // Hide loading indicator even if there's no QR code
          setIsLoadingQrCode(false)
          // Show an error message
          // Alert.alert("Erreur", "Aucun code QR n'a été généré. Veuillez réessayer.")
        }
      }

      if (paymentMethod === "scan") {
        // For scan payments, we need to check for QR code
        if (!invoiceData.temp_qr) {
          console.log("No temp_qr found in invoice data for scan payment")
          // Hide loading indicator even if there's no QR code
          setIsLoadingQrCode(false)
          // Show an error message
          Alert.alert("Erreur", "Aucun code QR n'a été généré. Veuillez réessayer.")
          return
        }

        // For scan payments, keep the QR code modal open to show the QR code
        // No need to close the modal here, as we want to show the QR code
        // For scan payments, we don't set isPaymentSuccessful to true
        // because we want to keep the QR code modal open
      } else if (paymentMethod === "cash") {
        // For cash payments, we don't need a QR code
        console.log("Showing cash payment modal with invoice data")

        // Now that we have the invoice data, show the cash modal
        setIsCashModalVisible(true)

        // Hide the loading indicator
        setIsLoadingCashPayment(false)
      }

      // Clear the cart
      clearCart()
    } catch (error: any) {
      console.error("Payment failed:", error)
      // Hide loading indicators in case of error
      setIsLoadingQrCode(false)
      setIsLoadingCashPayment(false)
      // Set payment status to failed
      setQrCodePaymentStatus("failed")
      Alert.alert("Erreur", error.message || "Le paiement a échoué. Veuillez réessayer.")
    } finally {
      setIsProcessing(false)
      // Make sure loading indicators are hidden in all cases
      setIsLoadingQrCode(false)
      setIsLoadingCashPayment(false)
    }
  }

  // Function to mark QR code payment as completed
  const handleCompleteQrPayment = () => {
    // Set payment status to completed
    setQrCodePaymentStatus("completed")
    // Show success message
    Alert.alert("Paiement réussi", "Le paiement a été effectué avec succès.")
  }

  // Function to handle completion of QR payment
  const handleQrPaymentComplete = () => {
    // Close the QR code modal
    setIsQrCodeVisible(false)
    setSelectedPaymentMethod(null)
    // Reset payment status for next time
    setQrCodePaymentStatus("completed")
    // Navigate to dashboard
    navigation.navigate("BusinessTabNav", { screen: "DashboardBusiness" })
  }

  const handleConfirmClearCart = () => {
    // clearCart()
    setIsClearCartModalVisible(false)
  }

  // Function to confirm cash payment with invoice reference
  // const confirmCashPayment = async (invoiceData: any) => {
  //   try {
  //     if (!invoiceData || !invoiceData.referance_id) {
  //       throw new Error("Invoice reference is required")
  //     }

  //     console.log("Confirming cash payment for invoice:", invoiceData.referance_id)

  //     // Here you would make an API call to confirm the cash payment
  //     // For example:
  //     // const response = await axiosInstance.post('/pay/confirm/cash/', {
  //     //   invoice_reference: invoiceData.referance_id
  //     // })

  //     // For now, we'll just simulate a successful confirmation
  //     console.log("Cash payment confirmed successfully")
  //     return true
  //   } catch (error: any) {
  //     console.error("Error confirming cash payment:", error)
  //     Alert.alert("Erreur", error.message || "Échec de la confirmation du paiement en espèces")
  //     return false
  //   }
  // }

  const handleCloseQrCode = () => {
    // Just close the QR code modal without changing status
    setIsQrCodeVisible(false)
    setSelectedPaymentMethod(null)
  }

  // Function to close cash modal
  const handleCloseCashModal = () => {
    setIsCashModalVisible(false)
    setSelectedPaymentMethod(null)
  }

  const handleDismissSuccessModal = () => {
    setIsPaymentSuccessful(false)
    navigation.navigate("BusinessTabNav", { screen: "DashboardBusiness" })
  }

  // // Function to handle printing the receipt
  // const handlePrintReceipt = () => {
  //   // For now, just dismiss the modal and navigate back
  //   setIsPaymentSuccessful(false)
  //   navigation.navigate("BusinessTabNav", { screen: "DashboardBusiness" })
  //   // TODO: Implement actual receipt printing functionality
  //   console.log("Print receipt initiated")
  // }

  return (
    <>
      <Header
        title="Method de paiement"
        leftIcon="backicon"
        onLeftPress={navigation.goBack}
        backgroundColor={colors.palette.neutral100}
      />
      <Screen style={styles.root} preset="fixed" safeAreaEdges={["bottom"]} statusBarStyle="dark">
        {/* Loading overlay for QR code generation */}
        {isLoadingQrCode && (
          <View style={styles.loadingOverlay}>
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={colors.palette.primary500} />
              <Text style={styles.loadingText}>Génération du QR code...</Text>
            </View>
          </View>
        )}

        {/* Loading overlay for cash payment */}
        {isLoadingCashPayment && (
          <View style={styles.loadingOverlay}>
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={colors.palette.primary500} />
              <Text style={styles.loadingText}>Préparation du paiement en espèces...</Text>
            </View>
          </View>
        )}

        <View style={styles.amountDueContainer}>
          <Text style={styles.amount}>{`${currency}${amountDue.toLocaleString(undefined, {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          })}`}</Text>
          <Text style={styles.amountDueText}>Amount Due</Text>
        </View>
        <View style={styles.separator} />

        <View style={styles.paymentMethodsContainer}>
          {/* <Text style={styles.paymentMethodsTitle}>Choose payment method</Text>  */}

          <TouchableOpacity
            style={[
              styles.paymentMethodItem,
              {
                borderColor: colors.palette.neutral900,
                backgroundColor: colors.palette.neutral900,
              },
            ]}
            onPress={() => handleSelectPaymentMethod("scan")}
            activeOpacity={0.8} // For a slight touch feedback
          >
            <View style={styles.paymentMethodIcon}>
              <Icon icon="qrcodewinc" size={30} color={colors.palette.neutral100} />
            </View>
            <View style={styles.paymentMethodText}>
              <Text style={{ color: colors.palette.neutral100, fontSize: 20 }}>
                Scanner pour payer
              </Text>
              <Text style={{ color: colors.palette.accent400, fontSize: 14 }}>
                Simple & Sécurisé
              </Text>
            </View>
            <Icon icon="arwRigh" size={20} color={colors.palette.neutral500} />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.paymentMethodItem}
            onPress={() => handleSelectPaymentMethod("cash")}
            activeOpacity={0.8} // For a slight touch feedback
          >
            <View
              style={[styles.paymentMethodIcon, { backgroundColor: colors.palette.neutral200 }]}
            >
              <Icon icon="cashmoney" size={30} color={colors.palette.neutral900} />
            </View>
            <View style={styles.paymentMethodText}>
              <Text style={{ color: colors.palette.neutral900, fontSize: 20 }}>Argent liquide</Text>
              <Text style={{ color: colors.palette.accent400, fontSize: 14 }}>
                Espèces acceptées
              </Text>
            </View>
            <Icon icon="arwRigh" size={20} color={colors.palette.neutral500} />
          </TouchableOpacity>
        </View>
      </Screen>

      {/* QR Code Modal - Only shown after we have the API response */}
      <Modal visible={isQrCodeVisible} animationType="slide" onRequestClose={handleCloseQrCode}>
        <View style={styles.qrCodeModalContainer}>
          {/* Header with close button */}
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Paiement par QR Code</Text>
            <TouchableOpacity onPress={handleCloseQrCode} style={styles.closeButton}>
              <Icon icon="x" size={24} color={colors.palette.neutral800} />
            </TouchableOpacity>
          </View>

          <View style={styles.qrCodeContent}>
            {/* Show the amount from the API response if available */}
            {invoiceData && invoiceData.amount && (
              <Text style={styles.qrCodeAmount}>
                {`${invoiceData.currency === 1 ? "USD" : "FC"} ${parseFloat(
                  invoiceData.amount,
                ).toLocaleString(undefined, {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                })}`}
              </Text>
            )}

            {/* Invoice details */}
            {invoiceData && (
              <View style={styles.invoiceDetailsContainer}>
                {invoiceData.referance_id && (
                  <View style={styles.invoiceDetailRow}>
                    <Text style={styles.invoiceDetailLabel}>Référence:</Text>
                    <Text style={styles.invoiceDetailValue}>{invoiceData.referance_id}</Text>
                  </View>
                )}
                {invoiceData.status && (
                  <View style={styles.invoiceDetailRow}>
                    <Text style={styles.invoiceDetailLabel}>Statut:</Text>
                    <Text
                      style={[
                        styles.invoiceDetailValue,
                        invoiceData.status === "pending"
                          ? styles.statusPending
                          : invoiceData.status === "completed"
                            ? styles.statusCompleted
                            : styles.statusCanceled,
                      ]}
                    >
                      {invoiceData.status === "pending"
                        ? "En attente"
                        : invoiceData.status === "completed"
                          ? "Complété"
                          : "Annulé"}
                    </Text>
                  </View>
                )}
                {invoiceData.created_at && (
                  <View style={styles.invoiceDetailRow}>
                    <Text style={styles.invoiceDetailLabel}>Date:</Text>
                    <Text style={styles.invoiceDetailValue}>
                      {new Date(invoiceData.created_at).toLocaleString()}
                    </Text>
                  </View>
                )}
              </View>
            )}

            {/* QR code display - waiting for payment */}
            {qrCodeUrl && qrCodePaymentStatus === "waiting" && (
              <View style={styles.invoiceContainer}>
                <Image
                  source={{ uri: qrCodeUrl }}
                  style={styles.qrCodeImage}
                  resizeMode="contain"
                />
                <Text style={styles.qrCodeInstructions}>
                  Scannez ce code QR pour effectuer le paiement
                </Text>
              </View>
            )}

            {/* Payment completed state */}
            {qrCodePaymentStatus === "completed" && (
              <View style={styles.successContainer}>
                <Icon icon="check" size={60} color={colors.palette.primary500} />
                <Text style={styles.successTitle}>Paiement réussi!</Text>
                {invoiceId && <Text style={styles.invoiceText}>Facture: {invoiceId}</Text>}
                <Text style={styles.successText}>Le paiement a été effectué avec succès.</Text>
              </View>
            )}

            {/* Processing state */}
            {isProcessing && (
              <View style={styles.processingContainer}>
                <ActivityIndicator size="large" color={colors.palette.primary500} />
                <Text style={styles.qrCodeWaiting}>Traitement du paiement...</Text>
              </View>
            )}

            {/* Retry button for errors */}
            {!isProcessing && !qrCodeUrl && qrCodePaymentStatus === "failed" && (
              <TouchableOpacity
                style={styles.retryButton}
                onPress={() => handlePaymentComplete("scan")}
              >
                <Text style={styles.retryButtonText}>Réessayer</Text>
              </TouchableOpacity>
            )}
          </View>

          <View style={styles.qrCodeActions}>
            {/* Only show the Done button when payment is completed */}
            {qrCodePaymentStatus === "completed" && (
              <TouchableOpacity
                style={[styles.qrCodeButton, styles.doneButton]}
                onPress={handleQrPaymentComplete}
              >
                <Text style={styles.qrCodeButtonText}>Terminé</Text>
              </TouchableOpacity>
            )}

            {/* Show waiting message when payment is still pending */}
            {qrCodePaymentStatus === "waiting" && (
              <View style={styles.waitingContainer}>
                <ActivityIndicator size="small" color={colors.palette.primary500} />
                <Text style={styles.waitingText}>En attente de paiement...</Text>
                <Text style={styles.waitingSubText}>Le paiement sera confirmé automatiquement</Text>
              </View>
            )}
          </View>
        </View>
      </Modal>

      {/* Cash Payment Confirmation Modal */}
      <Modal visible={isCashModalVisible} animationType="slide" transparent>
        <View style={styles.successModalOverlay}>
          <View style={styles.cashModalContainer}>
            <Text style={styles.cashModalTitle}>Confirmer le paiement en espèces</Text>

            {/* Invoice details from API response */}
            {invoiceData && (
              <View style={styles.invoiceDetailsContainer}>
                <View style={styles.invoiceDetailRow}>
                  <Text style={styles.invoiceDetailLabel}>Référence:</Text>
                  <Text style={styles.invoiceDetailValue}>
                    {invoiceData.referance_id || invoiceData.id}
                  </Text>
                </View>

                <View style={styles.invoiceDetailRow}>
                  <Text style={styles.invoiceDetailLabel}>Montant:</Text>
                  <Text style={styles.invoiceDetailValue}>
                    {`${invoiceData.currency === 1 ? "USD" : "FC"} ${parseFloat(
                      invoiceData.amount || "0",
                    ).toLocaleString(undefined, {
                      minimumFractionDigits: 2,
                      maximumFractionDigits: 2,
                    })}`}
                  </Text>
                </View>

                <View style={styles.invoiceDetailRow}>
                  <Text style={styles.invoiceDetailLabel}>Statut:</Text>
                  <Text
                    style={[
                      styles.invoiceDetailValue,
                      invoiceData.status === "pending"
                        ? styles.statusPending
                        : invoiceData.status === "completed"
                          ? styles.statusCompleted
                          : styles.statusCanceled,
                    ]}
                  >
                    {invoiceData.status === "pending"
                      ? "En attente"
                      : invoiceData.status === "completed"
                        ? "Complété"
                        : "Annulé"}
                  </Text>
                </View>

                {invoiceData.created_at && (
                  <View style={styles.invoiceDetailRow}>
                    <Text style={styles.invoiceDetailLabel}>Date:</Text>
                    <Text style={styles.invoiceDetailValue}>
                      {new Date(invoiceData.created_at).toLocaleString()}
                    </Text>
                  </View>
                )}
              </View>
            )}

            <View style={styles.receiptContainer}>
              {/* Total */}
              <View style={styles.receiptTotal}>
                <Text style={styles.receiptTotalLabel}>Total</Text>
                <Text style={styles.receiptTotalAmount}>
                  {invoiceData && invoiceData.amount
                    ? `${invoiceData.currency === 1 ? "USD" : "FC"} ${parseFloat(
                        invoiceData.amount,
                      ).toLocaleString(undefined, {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2,
                      })}`
                    : `${currency} ${amountDue.toLocaleString()}`}
                </Text>
              </View>
            </View>

            <Text style={styles.cashModalText}>
              Confirmez-vous avoir reçu le paiement en espèces?
            </Text>

            <View style={styles.cashModalButtons}>
              <Button
                text="Annuler"
                onPress={handleCloseCashModal}
                style={[styles.cashModalButton, styles.cancelButton]}
                textStyle={styles.cancelButtonText}
                disabled={isProcessing}
              />
              <Button
                preset="reversed"
                text={isProcessing ? "Traitement..." : "J'ai reçu le paiement"}
                onPress={async () => {
                  // Show processing state
                  setIsProcessing(true)

                  try {
                    // Confirm the cash payment with the invoice reference
                    if (invoiceData && invoiceData.referance_id) {
                      // Call the confirmCashPayment function
                      const invoiceRef = invoiceData.referance_id
                      console.log("refIn", invoiceRef)
                      const confirmresult = await confirmCashPayment(invoiceRef)
                      console.log("Confirm result:", confirmresult)

                      if (confirmresult.success) {
                        // Close the cash modal
                        setIsCashModalVisible(false)
                        navigation.navigate("BusinessTabNav", { screen: "DashboardBusiness" })
                        // Show the success modal
                        // setIsPaymentSuccessful(true)
                      }
                    } else {
                      throw new Error("Données de facture non disponibles")
                    }
                  } catch (error: any) {
                    console.error("Error confirming cash payment:", error)
                    Alert.alert("Erreur", error.message || "Échec de la confirmation du paiement")
                  } finally {
                    setIsProcessing(false)
                  }
                }}
                style={styles.cashModalButton}
                disabled={isProcessing || !invoiceData}
              />
            </View>
          </View>
        </View>
      </Modal>

      {/* Payment Successful Modal */}
      <Modal visible={isPaymentSuccessful} animationType="slide" transparent>
        <View style={styles.successModalOverlay}>
          <View style={styles.successModalContainer}>
            <Icon icon="check" size={60} color={colors.palette.primary500} />
            <Text style={styles.successModalTitle}>Paiement réussi!</Text>
            <Text style={styles.successModalText}>
              La facture a été créée et le paiement a été enregistré avec succès.
            </Text>
            {invoiceId && <Text style={styles.invoiceIdText}>Facture #{invoiceId}</Text>}
            <View style={styles.successModalAmount}>
              <Text style={styles.successModalAmountLabel}>Montant total:</Text>
              <Text style={styles.successModalAmountValue}>
                {`${currency} ${amountDue.toLocaleString()}`}
              </Text>
            </View>
            <Button
              text="OK"
              onPress={handleDismissSuccessModal}
              style={styles.successModalButton}
            />
          </View>
        </View>
      </Modal>

      <Modal visible={isClearCartModalVisible} animationType="fade" transparent>
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalIconContainer}>
              <Icon icon="warning" size={60} color="#FFC107" />
            </View>
            <Text style={styles.modalTitle}>
              Vous êtes sur le point de supprimer votre commande
            </Text>
            <Text style={styles.modalMessage}>
              Une fois supprimée, elle disparaîtra pour toujours. Voulez-vous supprimer
              définitivement votre commande ?
            </Text>
            <Button
              text="Oui, supprimer ma commande"
              style={styles.modalDeleteButton}
              onPress={handleConfirmClearCart}
              // preset="destructiveReversed"
            />
            <TouchableOpacity style={styles.modalGoBackButton} onPress={handleCancelClearCart}>
              <Text style={styles.modalGoBackText}>Retour</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </>
  )
}

const styles = StyleSheet.create({
  root: {
    // backgroundColor: colors.background,
    flex: 1,
  },
  loadingOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(0, 0, 0, 0.7)",
    justifyContent: "center",
    alignItems: "center",
    zIndex: 1000,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  modalContainer: {
    backgroundColor: colors.palette.neutral100,
    borderRadius: 12,
    padding: spacing.lg,
    alignItems: "center",
    width: "80%",
  },
  modalIconContainer: {
    backgroundColor: "#FFF3CD", // Light yellow background for warning
    borderRadius: 50,
    padding: spacing.md,
    marginBottom: spacing.md,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: colors.palette.neutral900,
    textAlign: "center",
    marginBottom: spacing.sm,
  },
  modalMessage: {
    fontSize: 16,
    color: colors.palette.neutral800,
    textAlign: "center",
    marginBottom: spacing.lg,
  },
  modalDeleteButton: {
    width: "100%",
    marginBottom: spacing.sm,
  },
  modalGoBackButton: {
    paddingVertical: spacing.md,
  },
  modalGoBackText: {
    color: colors.palette.neutral700,
    fontSize: 16,
    fontWeight: "bold",
  },
  loadingContainer: {
    backgroundColor: colors.palette.neutral100,
    borderRadius: 12,
    padding: spacing.xl,
    alignItems: "center",
    width: "80%",
    maxWidth: 300,
  },
  loadingText: {
    fontSize: 16,
    color: colors.palette.neutral800,
    marginTop: spacing.md,
    textAlign: "center",
  },
  amountDueContainer: {
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: spacing.xl,
  },
  amount: {
    color: colors.palette.neutral900,
    fontSize: 36,
    fontWeight: "bold",
  },
  amountDueText: {
    color: colors.palette.neutral500,
    fontSize: 16,
    marginTop: spacing.xs,
  },
  separator: {
    borderBottomWidth: 1,
    borderBottomColor: colors.palette.neutral200,
    marginHorizontal: spacing.md,
  },
  paymentMethodsContainer: {
    marginTop: spacing.lg,
    paddingHorizontal: spacing.md,
  },
  paymentMethodItem: {
    alignItems: "center",
    backgroundColor: colors.palette.neutral100,
    borderColor: colors.palette.neutral200,
    borderRadius: 12, // Slightly bigger rounding
    borderWidth: 1,
    flexDirection: "row",
    marginBottom: spacing.md,
    paddingHorizontal: spacing.md, // Slightly bigger horizontal padding
    paddingVertical: spacing.lg, // Slightly bigger vertical padding
    elevation: 3, // Basic shadow for "real-world" effect
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  defaultPaymentMethod: {
    borderColor: colors.palette.neutral900,
    backgroundColor: colors.palette.neutral900,
  },
  paymentMethodIcon: {
    alignItems: "center",
    borderRadius: 8, // Slightly bigger rounding for icon bg
    height: 45, // Slightly bigger icon container
    justifyContent: "center",
    marginRight: spacing.md,
    width: 45, // Slightly bigger icon container
  },
  paymentMethodText: {
    color: colors.palette.neutral800,
    flex: 1,
  },
  arrowRight: {
    marginLeft: spacing.md,
  },
  qrCodeModalContainer: {
    flex: 1,
    // backgroundColor: colors.background,
    justifyContent: "space-between",
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    // padding: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.palette.neutral200,
  },
  // modalTitle: {
  //   fontSize: 18,
  //   fontWeight: "600",
  //   color: colors.palette.neutral900,
  // },
  closeButton: {
    padding: spacing.xs,
  },
  qrCodeContent: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  qrCodeAmount: {
    fontSize: 28,
    fontWeight: "bold",
    color: colors.palette.neutral900,
    marginBottom: spacing.lg,
  },
  qrCodeImage: {
    width: 250,
    height: 250,
    marginVertical: spacing.md,
  },
  qrCodeWaiting: {
    fontSize: 18,
    color: colors.palette.neutral600,
    marginTop: spacing.lg,
  },
  processingContainer: {
    alignItems: "center",
    justifyContent: "center",
    marginVertical: spacing.xl,
  },
  qrCodeInstructions: {
    fontSize: 14,
    color: colors.palette.neutral600,
    marginTop: spacing.md,
    textAlign: "center",
  },
  retryButton: {
    backgroundColor: colors.palette.primary500,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: 8,
    marginTop: spacing.lg,
  },
  retryButtonText: {
    color: colors.palette.neutral100,
    fontSize: 14,
    fontWeight: "600",
  },
  qrCodeActions: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: spacing.lg,
  },
  qrCodeButton: {
    backgroundColor: colors.palette.neutral900,
    borderRadius: 8,
    flexDirection: "row",
    justifyContent: "center",
    paddingVertical: spacing.lg,
    paddingHorizontal: spacing.md,
    flex: 1,
    marginHorizontal: spacing.sm,
    alignItems: "center",
  },
  simulateButton: {
    backgroundColor: colors.palette.primary500,
  },
  doneButton: {
    backgroundColor: "#28a745", // Green success color
    width: "100%",
  },
  successContainer: {
    alignItems: "center",
    justifyContent: "center",
    padding: spacing.lg,
  },
  successTitle: {
    fontSize: 24,
    fontWeight: "bold",
    color: colors.palette.primary500,
    marginTop: spacing.md,
    marginBottom: spacing.sm,
  },
  successText: {
    fontSize: 16,
    color: colors.palette.neutral800,
    marginTop: spacing.sm,
    textAlign: "center",
  },
  invoiceDetailsContainer: {
    width: "100%",
    backgroundColor: colors.palette.neutral200,
    borderRadius: 8,
    padding: spacing.md,
    marginVertical: spacing.md,
  },
  invoiceDetailRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: spacing.xs,
  },
  invoiceDetailLabel: {
    fontSize: 14,
    fontWeight: "500",
    color: colors.palette.neutral700,
  },
  invoiceDetailValue: {
    fontSize: 14,
    fontWeight: "600",
    color: colors.palette.neutral900,
  },
  statusPending: {
    color: "#f0ad4e", // Orange for pending
  },
  statusCompleted: {
    color: "#28a745", // Green for completed
  },
  statusCanceled: {
    color: "#dc3545", // Red for canceled
  },
  qrCodeButtonText: {
    color: colors.palette.neutral100,
    fontSize: 16,
    justifyContent: "center",
    alignItems: "center",
    textAlign: "center",
    fontWeight: "bold",
  },
  disabledButton: {
    backgroundColor: colors.palette.neutral300,
  },
  successModalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  successModalContainer: {
    backgroundColor: colors.palette.neutral100,
    padding: spacing.xl,
    borderRadius: 12,
    alignItems: "center",
    width: "85%",
    maxWidth: 400,
  },
  successModalTitle: {
    fontSize: 24,
    fontWeight: "bold",
    color: colors.palette.primary500,
    marginTop: spacing.md,
    marginBottom: spacing.sm,
    textAlign: "center",
  },
  successModalText: {
    fontSize: 16,
    color: colors.palette.neutral800,
    marginBottom: spacing.md,
    textAlign: "center",
  },
  invoiceIdText: {
    fontSize: 18,
    fontWeight: "600",
    color: colors.palette.neutral900,
    marginBottom: spacing.md,
  },
  successModalAmount: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    width: "100%",
    marginBottom: spacing.lg,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    backgroundColor: colors.palette.neutral200,
    borderRadius: 8,
  },
  successModalAmountLabel: {
    fontSize: 16,
    fontWeight: "500",
    color: colors.palette.neutral800,
  },
  successModalAmountValue: {
    fontSize: 18,
    fontWeight: "bold",
    color: colors.palette.primary500,
  },
  successModalButton: {
    width: "100%",
    marginBottom: spacing.sm,
  },
  printButton: {
    backgroundColor: colors.palette.neutral200,
  },
  printButtonText: {
    color: colors.palette.neutral900,
  },
  invoiceContainer: {
    alignItems: "center",
    marginVertical: spacing.md,
  },
  invoiceText: {
    fontSize: 16,
    fontWeight: "bold",
    color: colors.palette.neutral900,
    marginBottom: spacing.sm,
  },
  cashModalContainer: {
    backgroundColor: colors.palette.neutral100,
    padding: spacing.xl,
    borderRadius: 12,
    width: "90%",
    maxHeight: "90%", // Increased to allow more space
  },
  cashModalTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: colors.palette.neutral900,
    marginBottom: spacing.md,
    textAlign: "center",
  },
  cashModalText: {
    fontSize: 16,
    color: colors.palette.neutral800,
    marginVertical: spacing.md,
    textAlign: "center",
  },
  cashModalButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: spacing.md,
    gap: spacing.sm,
  },
  cashModalButton: {
    flex: 1,
    backgroundColor: colors.palette.neutral900, // Example: Green
    borderRadius: 16,
    paddingVertical: 20,
    width: "100%", // Full width
  },
  cancelButton: {
    backgroundColor: colors.palette.neutral300,
    borderWidth: 1,
    borderColor: colors.palette.neutral400,
  },
  cancelButtonText: {
    color: colors.palette.neutral900,
  },
  receiptContainer: {
    backgroundColor: colors.palette.neutral100,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.palette.neutral300,
    padding: spacing.md,
    marginVertical: spacing.md,
    maxHeight: 350, // Limit the overall height
  },
  receiptHeader: {
    fontSize: 18,
    fontWeight: "bold",
    color: colors.palette.neutral900,
    textAlign: "center",
    marginBottom: spacing.sm,
  },
  receiptDivider: {
    borderBottomWidth: 1,
    borderBottomColor: colors.palette.neutral300,
    marginVertical: spacing.sm,
  },
  receiptItems: {
    marginVertical: spacing.sm,
    maxHeight: 180, // Limit the height to make it scrollable
    flexGrow: 0, // Prevent the ScrollView from expanding
  },
  receiptItemsContent: {
    paddingRight: spacing.xs, // Add some padding for the scrollbar
  },
  receiptItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: spacing.xs,
  },
  receiptItemDetails: {
    flex: 1,
    flexDirection: "row",
  },
  receiptItemName: {
    flex: 1,
    fontSize: 14,
    color: colors.palette.neutral800,
  },
  receiptItemQuantity: {
    fontSize: 14,
    color: colors.palette.neutral600,
    marginRight: spacing.sm,
  },
  receiptItemPrice: {
    fontSize: 14,
    color: colors.palette.neutral900,
    fontWeight: "500",
  },
  receiptTotal: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: spacing.sm,
  },
  receiptTotalLabel: {
    fontSize: 16,
    fontWeight: "bold",
    color: colors.palette.neutral900,
  },
  receiptTotalAmount: {
    fontSize: 16,
    fontWeight: "bold",
    color: colors.palette.neutral900,
  },
  waitingContainer: {
    alignItems: "center",
    justifyContent: "center",
    padding: spacing.md,
    // backgroundColor: colors.palette.neutral200,
    borderRadius: 8,
    marginHorizontal: spacing.md,
    marginBottom: spacing.md,
  },
  waitingText: {
    fontSize: 16,
    fontWeight: "600",
    color: colors.palette.neutral900,
    marginTop: spacing.sm,
  },
  waitingSubText: {
    fontSize: 14,
    color: colors.palette.neutral600,
    marginTop: spacing.xs,
    textAlign: "center",
  },
})

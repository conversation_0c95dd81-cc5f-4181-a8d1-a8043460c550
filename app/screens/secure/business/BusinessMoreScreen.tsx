import { FC, useEffect, useState } from "react"
import {
  ViewStyle,
  View,
  Image,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Linking,
  Dimensions,
} from "react-native"
import { AppStackScreenProps } from "@/navigators"
import { Header, Screen, Text, Icon } from "@/components"
import { useStores } from "@/store"
import { colors, spacing } from "@/theme"

interface BusinessMoreScreenProps extends AppStackScreenProps<"BusinessMore"> {}

const { width: SCREEN_WIDTH } = Dimensions.get("window")
const BANNER_HEIGHT = 180
const LOGO_SIZE = 100
const LOGO_BORDER = 4

export const BusinessMoreScreen: FC<BusinessMoreScreenProps> = ({ navigation }) => {
  const {
    business: { getMainBusiness, fetchBusinesses },
  } = useStores()

  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const loadBusinessData = async () => {
      setLoading(true)
      await fetchBusinesses()
      setLoading(false)
    }

    loadBusinessData()
  }, [fetchBusinesses])

  // Get the main business
  const business = getMainBusiness()

  // Default images
  const defaultBanner = require("../../../../assets/images/banners/DefaultBackground.png")
  const defaultLogo = require("../../../../assets/images/fedhaSafeLogo.png")

  // Format phone numbers for display
  const formatPhoneNumbers = (phones: string[]) => {
    if (!phones || phones.length === 0) return "Aucun numéro de téléphone"
    return phones.join(", ")
  }

  // Format emails for display
  const formatEmails = (emails: string[]) => {
    if (!emails || emails.length === 0) return "Aucune adresse email"
    return emails.join(", ")
  }

  // Handle phone call
  const handleCall = (phone: string) => {
    if (phone) {
      Linking.openURL(`tel:${phone}`)
    }
  }

  // Handle email
  const handleEmail = (email: string) => {
    if (email) {
      Linking.openURL(`mailto:${email}`)
    }
  }

  // Handle website visit
  const handleWebsite = (website: string | null) => {
    if (website) {
      Linking.openURL(website.startsWith("http") ? website : `https://${website}`)
    }
  }

  // Render a section with title and content
  const renderSection = (title: string, content: string | JSX.Element, _icon: string) => (
    <View style={styles.section}>
      <View style={styles.sectionHeader}>
        <Icon icon="info" size={20} color={colors.palette.primary500} />
        <Text style={styles.sectionTitle}>{title}</Text>
      </View>
      {typeof content === "string" ? <Text style={styles.sectionContent}>{content}</Text> : content}
    </View>
  )

  // Render contact buttons
  const renderContactButtons = () => {
    const hasPhone = business?.telephones && business.telephones.length > 0
    const hasEmail = business?.emails && business.emails.length > 0

    if (!hasPhone && !hasEmail) return null

    return (
      <View style={styles.contactButtonsContainer}>
        {hasPhone && (
          <TouchableOpacity
            style={styles.contactButton}
            onPress={() => handleCall(business?.telephones[0])}
          >
            <Icon icon="email" size={20} color={colors.palette.neutral100} />
            <Text style={styles.contactButtonText}>Appeler</Text>
          </TouchableOpacity>
        )}

        {hasEmail && (
          <TouchableOpacity
            style={styles.contactButton}
            onPress={() => handleEmail(business?.emails[0])}
          >
            <Icon icon="email" size={20} color={colors.palette.neutral100} />
            <Text style={styles.contactButtonText}>Email</Text>
          </TouchableOpacity>
        )}

        {business?.website && (
          <TouchableOpacity
            style={styles.contactButton}
            onPress={() => handleWebsite(business.website)}
          >
            <Icon icon="search" size={20} color={colors.palette.neutral100} />
            <Text style={styles.contactButtonText}>Site web</Text>
          </TouchableOpacity>
        )}
      </View>
    )
  }

  // Render business status badge
  const renderStatusBadge = () => {
    const statusColors = {
      active: colors.palette.primary500,
      inactive: colors.palette.neutral500,
      suspended: colors.palette.angry500,
    }

    const statusText = {
      active: "Actif",
      inactive: "Inactif",
      suspended: "Suspendu",
    }

    const status = business?.status || "inactive"

    return (
      <View style={[styles.statusBadge, { backgroundColor: statusColors[status] }]}>
        <Text style={styles.statusText}>{statusText[status]}</Text>
      </View>
    )
  }

  // Render verification badge
  const renderVerificationBadge = () => {
    if (!business) return null

    return (
      <View
        style={[
          styles.verificationBadge,
          {
            backgroundColor: business.is_verified
              ? colors.palette.primary500
              : colors.palette.neutral500,
          },
        ]}
      >
        <Icon
          icon={business.is_verified ? "Rcheck" : "info"}
          size={12}
          color={colors.palette.neutral100}
        />
        <Text style={styles.verificationText}>
          {business.is_verified ? "Vérifié" : "Non vérifié"}
        </Text>
      </View>
    )
  }

  return (
    <>
      <Header title="Profil d'entreprise" leftIcon="backicon" onLeftPress={navigation.goBack} />
      <Screen style={$root} preset="scroll" statusBarStyle="dark">
        {loading ? (
          <View style={styles.loadingContainer}>
            <Text style={styles.loadingText}>Chargement du profil...</Text>
          </View>
        ) : !business ? (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>Aucune entreprise trouvée</Text>
          </View>
        ) : (
          <ScrollView contentContainerStyle={styles.scrollContent}>
            {/* Banner and Logo Section */}
            <View style={styles.bannerContainer}>
              <Image
                source={business.image_banner ? { uri: business.image_banner } : defaultBanner}
                style={styles.bannerImage}
                resizeMode="cover"
              />

              <View style={styles.logoContainer}>
                <Image
                  source={business.image_logo ? { uri: business.image_logo } : defaultLogo}
                  style={styles.logoImage}
                  resizeMode="cover"
                />
              </View>
            </View>

            {/* Business Name and Status */}
            <View style={styles.businessNameContainer}>
              <Text style={styles.businessName}>{business.name}</Text>
              <View style={styles.badgesContainer}>
                {renderStatusBadge()}
                {renderVerificationBadge()}
              </View>
            </View>

            {/* Business Type */}
            <View style={styles.businessTypeContainer}>
              <Text style={styles.businessType}>
                {business.type_business === "formal"
                  ? "Entreprise formelle"
                  : "Entreprise informelle"}
              </Text>
            </View>

            {/* Contact Buttons */}
            {renderContactButtons()}

            {/* Business Details */}
            <View style={styles.detailsContainer}>
              {/* Description */}
              {renderSection("Description", business.description || "Aucune description", "info")}

              {/* Address */}
              {renderSection(
                "Adresse",
                `${business.adresse}, ${business.city}, ${business.country}`,
                "location",
              )}

              {/* Phone Numbers */}
              {renderSection("Téléphones", formatPhoneNumbers(business.telephones), "phone")}

              {/* Emails */}
              {renderSection("Emails", formatEmails(business.emails), "mail")}

              {/* Website */}
              {renderSection("Site web", business.website || "Aucun site web", "globe")}

              {/* Categories */}
              {renderSection(
                "Catégories",
                business.categories.length > 0
                  ? business.categories.join(", ")
                  : "Aucune catégorie",
                "category",
              )}

              {/* Registration Number */}
              {renderSection(
                "Numéro d'enregistrement",
                business.registration_number || "Non enregistré",
                "document",
              )}

              {/* Business Number */}
              {renderSection("Numéro d'entreprise", business.business_no, "business")}

              {/* Wallet */}
              {renderSection(
                "Portefeuille",
                business.wallet?.pochi || "Aucun portefeuille",
                "wallet",
              )}

              {/* QR Code */}
              {business.wallet?.qr_code && (
                <View style={styles.qrCodeContainer}>
                  <Text style={styles.qrCodeTitle}>Code QR</Text>
                  <Image
                    source={{ uri: business.wallet.qr_code }}
                    style={styles.qrCodeImage}
                    resizeMode="contain"
                  />
                </View>
              )}
            </View>
          </ScrollView>
        )}
      </Screen>
    </>
  )
}

const $root: ViewStyle = {
  flex: 1,
  backgroundColor: colors.palette.neutral200,
}

const styles = StyleSheet.create({
  badgesContainer: {
    flexDirection: "row",
    justifyContent: "center",
    marginTop: spacing.xs,
  },
  bannerContainer: {
    height: BANNER_HEIGHT,
    position: "relative",
    width: SCREEN_WIDTH,
  },
  bannerImage: {
    height: BANNER_HEIGHT,
    width: SCREEN_WIDTH,
  },
  businessName: {
    color: colors.palette.neutral800,
    fontSize: 24,
    fontWeight: "bold",
    textAlign: "center",
  },
  businessNameContainer: {
    alignItems: "center",
    marginTop: LOGO_SIZE / 2 + spacing.md,
    paddingHorizontal: spacing.lg,
  },
  businessType: {
    color: colors.palette.neutral600,
    fontSize: 14,
    fontStyle: "italic",
  },
  businessTypeContainer: {
    alignItems: "center",
    marginTop: spacing.xs,
  },
  contactButton: {
    alignItems: "center",
    backgroundColor: colors.palette.primary500,
    borderRadius: 20,
    flexDirection: "row",
    marginHorizontal: spacing.xs,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
  },
  contactButtonText: {
    color: colors.palette.neutral100,
    fontSize: 14,
    fontWeight: "500",
    marginLeft: spacing.xs,
  },
  contactButtonsContainer: {
    flexDirection: "row",
    justifyContent: "center",
    marginTop: spacing.md,
    paddingHorizontal: spacing.lg,
  },
  detailsContainer: {
    marginTop: spacing.xl,
    paddingHorizontal: spacing.lg,
  },
  emptyContainer: {
    alignItems: "center",
    flex: 1,
    justifyContent: "center",
  },
  emptyText: {
    color: colors.palette.neutral600,
    fontSize: 16,
  },
  loadingContainer: {
    alignItems: "center",
    flex: 1,
    justifyContent: "center",
  },
  loadingText: {
    color: colors.palette.neutral600,
    fontSize: 16,
  },
  logoContainer: {
    backgroundColor: colors.palette.neutral100,
    borderColor: colors.palette.neutral100,
    borderRadius: LOGO_SIZE / 2,
    borderWidth: LOGO_BORDER,
    bottom: -LOGO_SIZE / 2,
    elevation: 4,
    height: LOGO_SIZE,
    left: SCREEN_WIDTH / 2 - LOGO_SIZE / 2,
    overflow: "hidden",
    position: "absolute",
    shadowColor: colors.palette.neutral900,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    width: LOGO_SIZE,
  },
  logoImage: {
    borderRadius: (LOGO_SIZE - LOGO_BORDER) / 2,
    height: "100%",
    width: "100%",
  },
  qrCodeContainer: {
    alignItems: "center",
    backgroundColor: colors.palette.neutral100,
    borderRadius: 12,
    elevation: 2,
    marginBottom: spacing.md,
    padding: spacing.md,
    shadowColor: colors.palette.neutral900,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  qrCodeImage: {
    backgroundColor: colors.palette.neutral100,
    height: 200,
    width: 200,
  },
  qrCodeTitle: {
    color: colors.palette.neutral800,
    fontSize: 16,
    fontWeight: "bold",
    marginBottom: spacing.md,
  },
  scrollContent: {
    paddingBottom: spacing.xl * 2,
  },
  section: {
    backgroundColor: colors.palette.neutral100,
    borderRadius: 12,
    elevation: 2,
    marginBottom: spacing.md,
    padding: spacing.md,
    shadowColor: colors.palette.neutral900,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  sectionContent: {
    color: colors.palette.neutral700,
    fontSize: 14,
    marginLeft: spacing.lg,
  },
  sectionHeader: {
    alignItems: "center",
    flexDirection: "row",
    marginBottom: spacing.xs,
  },
  sectionTitle: {
    color: colors.palette.neutral800,
    fontSize: 16,
    fontWeight: "bold",
    marginLeft: spacing.xs,
  },
  statusBadge: {
    borderRadius: 12,
    marginRight: spacing.xs,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xxs,
  },
  statusText: {
    color: colors.palette.neutral100,
    fontSize: 12,
    fontWeight: "500",
  },
  verificationBadge: {
    alignItems: "center",
    borderRadius: 12,
    flexDirection: "row",
    marginLeft: spacing.xs,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xxs,
  },
  verificationText: {
    color: colors.palette.neutral100,
    fontSize: 12,
    fontWeight: "500",
    marginLeft: spacing.xxs,
  },
})

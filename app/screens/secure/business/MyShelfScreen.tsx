/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable react-native/no-inline-styles */
import { FC, useEffect, useState } from "react"
import {
  View,
  ViewStyle,
  TextStyle,
  FlatList,
  TouchableOpacity,
  ImageStyle,
  ActivityIndicator,
} from "react-native"
import { AppStackScreenProps } from "@/navigators"
import { Screen, Text, Icon, TextField, Header, CartFloatWidget } from "@/components"
import { colors, spacing, typography } from "@/theme"
import { useCartStore, useStores } from "@/store"
import { Product as ProductType } from "@/services/api/api.types"

interface MyShelfScreenProps extends AppStackScreenProps<"MyShelf"> {}

// We'll use the Product type from api.types.ts

// interface Category {
//   id: string
//   name: string
// }

// const MOCK_CATEGORIES: Category[] = [
//   { id: "1", name: "Tous" },
//   { id: "2", name: "<PERSON>lectron<PERSON>" },
//   { id: "3", name: "Vêtem<PERSON>" },
//   { id: "4", name: "<PERSON><PERSON>" },
//   { id: "5", name: "Accessoires" },
// ]

// We'll use real products from the BusinessStore

export const MyShelfScreen: FC<MyShelfScreenProps> = ({ navigation }) => {
  const [searchQuery, setSearchQuery] = useState("")
  // We'll keep selectedCategory for future use
  const [selectedCategory] = useState<string>("")
  const [filteredProducts, setFilteredProducts] = useState<ProductType[]>([])
  const addToCart = useCartStore((state) => state.addItem)

  // Get business store data
  const {
    business: { products, productsLoading, fetchProducts, error },
  } = useStores()

  // Get the cart item count from the cart store
  const itemCount = useCartStore((state) => state.getItemCount())
  // Only show the count if there are items in the cart
  const showCount = itemCount > 0

  // Fetch products when component mounts
  useEffect(() => {
    fetchProducts()
  }, [fetchProducts])

  // Filter products when products array, search query, or category changes
  useEffect(() => {
    // console.log("Products in MyShelfScreen:", products)
    if (!products || products.length === 0) {
      setFilteredProducts([])
      return
    }

    let filtered = [...products]
    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(
        (product) =>
          product.name.toLowerCase().includes(query) ||
          (product.description && product.description.toLowerCase().includes(query)),
      )
    }

    // console.log("Filtered products:", filtered)
    setFilteredProducts(filtered)
  }, [products, selectedCategory, searchQuery])

  const handleAddToCart = (product: ProductType) => {
    addToCart({
      name: product.name,
      description: product.description || `Produit: ${product.name}`,
      price: product.price,
      currency: "FC", // Assuming FC is your default currency
    })
  }

  // const renderCategory = ({ item }: { item: Category }) => (
  //   <TouchableOpacity
  //     style={[styles.categoryButton, selectedCategory === item.id && styles.selectedCategoryButton]}
  //     onPress={() => setSelectedCategory(item.id)}
  //   >
  //     <Text
  //       style={[styles.categoryText, selectedCategory === item.id && styles.selectedCategoryText]}
  //     >
  //       {item.name}
  //     </Text>
  //   </TouchableOpacity>
  // )

  // console.log('dd', products)

  const renderProduct = ({ item }: { item: ProductType }) => (
    <View style={styles.productCard}>
      {/* <Image source={{ uri: item.image }} style={styles.productImage} /> */}
      <Icon icon="products" size={80} color={colors.palette.neutral300} />
      <View style={styles.productInfo}>
        <Text style={styles.productName}>{item.name}</Text>
        <Text style={styles.productSku}>{item.description || "Aucune description"}</Text>
        {/* <Text style={styles.productSku}>SKU: {item.sku}</Text>
        <Text style={styles.productStock}>En stock: {item.stock}</Text> */}
        <Text style={styles.productPrice}>{item.price.toLocaleString()} FC</Text>
      </View>
      <TouchableOpacity style={styles.addButton} onPress={() => handleAddToCart(item)}>
        <Icon icon="plus" size={24} color={colors.palette.neutral100} />
      </TouchableOpacity>
    </View>
  )

  return (
    <>
      <Header
        title="Mes Produits"
        leftIcon="backicon"
        onLeftPress={navigation.goBack}
        RightActionComponent={
          <TouchableOpacity
            style={{
              flexDirection: "row",
              alignItems: "center",
              paddingHorizontal: spacing.md,
              // backgroundColor: colors.palette.neutral300,
              padding: spacing.xs,
              borderRadius: 20,
            }}
            onPress={() => navigation.navigate("AddProduct")}
          >
            <Icon icon="plus" size={17} color={colors.palette.neutral900} />
            <Text style={{ color: colors.palette.neutral900, paddingLeft: spacing.sm }}>
              Ajouter
            </Text>
          </TouchableOpacity>
        }
      />
      <Screen style={styles.root} preset="fixed" safeAreaEdges={["bottom"]} statusBarStyle="dark">
        {/* Search Bar */}
        <TextField
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholder="Rechercher un produit..."
          style={styles.searchInput}
          containerStyle={styles.searchContainer}
          RightAccessory={() => (
            <Icon
              icon="search"
              size={25}
              containerStyle={{ padding: 15 }}
              color={colors.palette.neutral500}
            />
          )}
        />

        {/* Categories */}
        {/* <View style={styles.categoriesContainer}>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.categoriesScroll}
          >
            {MOCK_CATEGORIES.map((category) => renderCategory({ item: category }))}
          </ScrollView>
        </View> */}

        {/* Products List */}
        {productsLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.palette.primary500} />
            <Text style={styles.loadingText}>Chargement des produits...</Text>
          </View>
        ) : error ? (
          <View style={styles.errorContainer}>
            <Icon icon="warning" size={50} color={colors.error} />
            <Text style={styles.errorText}>{error}</Text>
            <TouchableOpacity style={styles.retryButton} onPress={() => fetchProducts()}>
              <Text style={styles.retryButtonText}>Réessayer</Text>
            </TouchableOpacity>
          </View>
        ) : filteredProducts.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Icon icon="products" size={80} color={colors.palette.neutral400} />
            <Text style={styles.emptyText}>Aucun produit trouvé</Text>
            <TouchableOpacity
              style={styles.addProductButton}
              onPress={() => navigation.navigate("AddProduct")}
            >
              <Text style={styles.addProductButtonText}>Ajouter un produit</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <FlatList
            data={filteredProducts}
            renderItem={renderProduct}
            keyExtractor={(item) => item.id || item.name}
            contentContainerStyle={styles.productsList}
            showsVerticalScrollIndicator={false}
          />
        )}
        {/* Only show the Cart Widget when there is a product in the cart */}
        {showCount && <CartFloatWidget navigation={navigation} />}
      </Screen>
    </>
  )
}

const styles = {
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: spacing.xl,
  } as ViewStyle,

  loadingText: {
    ...typography.secondary,
    marginTop: spacing.md,
    color: colors.palette.neutral600,
  } as TextStyle,

  errorContainer: {
    // flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: spacing.xl,
  } as ViewStyle,

  errorText: {
    ...typography.secondary,
    marginTop: spacing.md,
    color: colors.error,
    textAlign: "center",
  } as TextStyle,

  retryButton: {
    marginTop: spacing.lg,
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.md,
    backgroundColor: colors.palette.primary500,
    borderRadius: 8,
  } as ViewStyle,

  retryButtonText: {
    ...typography.secondary,
    color: colors.palette.neutral100,
  } as TextStyle,

  emptyContainer: {
    // flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: spacing.xl,
  } as ViewStyle,

  emptyText: {
    ...typography.secondary,
    marginTop: spacing.md,
    color: colors.palette.neutral600,
    marginBottom: spacing.lg,
  } as TextStyle,

  addProductButton: {
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.md,
    backgroundColor: colors.palette.neutral900,
    borderRadius: 8,
  } as ViewStyle,

  addProductButtonText: {
    ...typography.secondary,
    color: colors.palette.neutral100,
  } as TextStyle,
  root: {
    flex: 1,
    // backgroundColor: colors.background,
  } as ViewStyle,

  searchContainer: {
    paddingHorizontal: spacing.md,
    paddingTop: spacing.sm,
  } as ViewStyle,

  searchInput: {
    // backgroundColor: colors.palette.neutral200,
    borderRadius: 8,
  } as ViewStyle,

  categoriesContainer: {
    marginVertical: spacing.sm,
  } as ViewStyle,

  categoriesScroll: {
    paddingHorizontal: spacing.md,
  } as ViewStyle,

  categoryButton: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.xs,
    backgroundColor: colors.palette.neutral200,
    borderRadius: 20,
    marginRight: spacing.sm,
  } as ViewStyle,

  selectedCategoryButton: {
    backgroundColor: colors.palette.primary500,
  } as ViewStyle,

  categoryText: {
    ...typography.secondary,
    color: colors.palette.neutral800,
  } as TextStyle,

  selectedCategoryText: {
    color: colors.palette.neutral100,
  } as TextStyle,

  productsList: {
    padding: spacing.md,
  } as ViewStyle,

  productCard: {
    flexDirection: "row",
    backgroundColor: colors.palette.neutral100,
    borderRadius: 12,
    padding: spacing.sm,
    marginBottom: spacing.sm,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  } as ViewStyle,

  productImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
  } as ImageStyle,

  productInfo: {
    flex: 1,
    marginLeft: spacing.sm,
    justifyContent: "space-between",
  } as ViewStyle,

  productName: {
    ...typography.secondary,
    fontSize: 16,
    fontWeight: "bold",
    color: colors.palette.neutral900,
  } as TextStyle,

  productSku: {
    ...typography.secondary,
    fontSize: 12,
    color: colors.palette.neutral500,
  } as TextStyle,

  productStock: {
    ...typography.secondary,
    fontSize: 12,
    color: colors.palette.neutral700,
  } as TextStyle,

  productPrice: {
    ...typography.secondary,
    fontSize: 16,
    fontWeight: "bold",
    color: colors.palette.primary500,
  } as TextStyle,

  addButton: {
    backgroundColor: colors.palette.neutral900,
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
    alignSelf: "center",
  } as ViewStyle,
}

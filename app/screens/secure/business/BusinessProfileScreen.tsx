import { FC } from "react"
import { ViewStyle } from "react-native"
import { AppStackScreenProps } from "@/navigators"
import { Screen, Text } from "@/components"
// import { useNavigation } from "@react-navigation/native"

interface BusinessProfileScreenProps extends AppStackScreenProps<"BusinessProfile"> {}

export const BusinessProfileScreen: FC<BusinessProfileScreenProps> = () => {
  // Pull in navigation via hook
  // const navigation = useNavigation()
  return (
    <Screen style={$root} preset="scroll">
      <Text text="businessProfile" />
    </Screen>
  )
}

const $root: ViewStyle = {
  flex: 1,
}

/* eslint-disable react-native/no-color-literals */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable react-native/sort-styles */
/* eslint-disable react-native/no-inline-styles */
/* eslint-disable react-native/no-unused-styles */
import { FC, useState, useRef, useEffect } from "react"
import {
  View,
  TextInput,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Dimensions,
  ViewStyle,
  Image,
  Alert,
  Modal,
  ActivityIndicator,
} from "react-native"
import { AppStackScreenProps } from "@/navigators"
import { Button, CustomSelector, FencyTextInput, Header, Icon, Text } from "@/components"
import { BusinessMenuPopup } from "@/components/BusinessMenuPopup"
import { CategoryDropdown } from "@/components/business/CategoryDropdown"
import { colors, spacing } from "@/theme"
import { DRC_cities } from "@/utils/conutries"
import { launchCamera, launchImageLibrary, ImagePickerResponse } from "react-native-image-picker"
import { compressImage } from "@/utils/compresor"
import { CreateBusiness } from "@/services/api/api"
import { useStores } from "@/store/rootStore"

const { width } = Dimensions.get("window")

interface BusinessOnboardingScreenProps extends AppStackScreenProps<"BusinessOnboarding"> {}

export const BusinessOnboardingScreen: FC<BusinessOnboardingScreenProps> = ({ navigation }) => {
  const {
    business: { categories, fetchBusinessCategories },
  } = useStores()
  const [step, setStep] = useState(1)
  const scrollViewRef = useRef<ScrollView>(null)
  const [isBusinessRegistered, setIsBusinessRegistered] = useState<boolean | null>(null)
  const [businessDetails, setBusinessDetails] = useState({
    name: "",
    registration_number: "",
    country: "République Démocratique du Congo", // Default to DRC
    city: "",
    adresse: "",
    website: "",
    description: "",
    categories: "",
    businesstype: isBusinessRegistered ? "Formal" : "Informal", // Default to "normal" : "informal",
  })
  const [documents, setDocuments] = useState({
    businesslogo: null as string | null,
    businessphoto: null as string | null,
  })

  // Image picker state
  const [showImageOptions, setShowImageOptions] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const [showImagePreview, setShowImagePreview] = useState(false)
  const [selectedImageUri, setSelectedImageUri] = useState<string | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [showMenuPopup, setShowMenuPopup] = useState(false)
  // We'll use this later for submitting the form
  // const { auth } = useStores()

  useEffect(() => {
    fetchBusinessCategories()
  }, [fetchBusinessCategories])

  // console.log("cat", categories)

  const nextStep = () => {
    if (step < 4) {
      setStep(step + 1)
      scrollViewRef.current?.scrollTo({ x: width * step, animated: true })
    }
  }

  const prevStep = () => {
    if (step > 1) {
      setStep(step - 1)
      scrollViewRef.current?.scrollTo({ x: width * (step - 2), animated: true })
    }
  }

  const handleInputChange = (name: keyof typeof businessDetails, value: string) => {
    setBusinessDetails({ ...businessDetails, [name]: value })
  }

  // This function would be used in a real implementation
  // const handleDocumentUpload = (type: keyof typeof documents, file: any) => {
  //   setDocuments({ ...documents, [type]: file })
  // }

  const handleSubmit = async () => {
    setIsSubmitting(true)
    try {
      // Get all categories from the business store
      // const { categories } = business

      // Define the category type
      interface Category {
        id: number
        name: string
        description: string
      }

      // Find the category by name
      const selectedCategory = categories.find(
        (cat: Category) => cat.name === businessDetails.categories,
      )

      // Log the selected category for debugging
      // console.log("Selected category:", selectedCategory)

      // Check if we have a valid category ID
      if (!selectedCategory) {
        Alert.alert("Error", "Veuillez sélectionner une catégorie valide.")
        setIsSubmitting(false)
        return
      }

      // Create a regular JavaScript object for the API request
      const formData = {
        name: businessDetails.name,
        registration_number: businessDetails.registration_number,
        country: businessDetails.country,
        city: businessDetails.city,
        adresse: businessDetails.adresse,
        website: businessDetails.website || "",
        description: businessDetails.description,
        businesstype: businessDetails.businesstype,
        categories: selectedCategory.name,
        type_business: isBusinessRegistered ? "formal" : "informal",
        // image_logo: documents.businesslogo,
      }

      // console.log("Form Data prepared for submission:", formData)

      const { success, data, message } = await CreateBusiness(formData)
      // console.log("Business Created:", data)

      if (!success) {
        // Show specific error message from API or a fallback message
        const errorMessage =
          message || data?.error || "Failed to create business. Please try again."
        Alert.alert("Error", errorMessage)
        setIsSubmitting(false)
        return
      }

      // Clear the form only on success
      setBusinessDetails({
        name: "",
        registration_number: "",
        country: "DRC",
        city: "",
        adresse: "",
        website: "",
        description: "",
        categories: "",
        businesstype: isBusinessRegistered ? "formal" : "informal",
      })

      setDocuments({
        businesslogo: null,
        businessphoto: null,
      })

      setIsSubmitting(false)

      // Navigate only on success
      Alert.alert("Success", "Business created successfully!", [
        {
          text: "OK",
          onPress: () => navigation.navigate("DashboardBusiness" as never),
        },
      ])
    } catch (error) {
      console.error("Submit Error:", error)
      Alert.alert("Error", "An unexpected error occurred. Please try again.")
      setIsSubmitting(false)
    }
  }

  const renderStepIndicator = () => (
    <View style={styles.stepIndicatorContainer}>
      {[1, 2, 3].map((index) => (
        <View key={index} style={[styles.stepCircle, index < step && styles.stepActiveCircle]}>
          <Text style={[styles.stepNumber, index < step && styles.stepActiveNumber]}>{index}</Text>
        </View>
      ))}
    </View>
  )

  const renderStepOne = () => (
    <View style={styles.stepContent}>
      <Text style={styles.stepTitle}>Choose Business Type</Text>
      <TouchableOpacity
        style={[styles.choiceCard, isBusinessRegistered === true && styles.choiceCardSelected]}
        onPress={() => setIsBusinessRegistered(true)}
      >
        <View style={styles.cardContentVertical}>
          <View style={styles.iconContainer}>
            <Icon icon="certificate" size={39} color={colors.palette.neutral700} />
            <Text style={styles.cardTitle} text="Entreprise enregistrée" />
          </View>
          <View style={{}}>
            <Text
              preset="subheading"
              style={styles.cardDescriptionAccent}
              text="Idéal pour les entreprises officiellement reconnues par l’État (RCCM, NIF, etc.)."
            />
            <Text
              style={styles.cardDescription}
              text="Profitez de toutes les fonctionnalités : encaissement illimité, rapports financiers détaillés, et meilleure crédibilité auprès de vos clients."
            />
          </View>
        </View>
      </TouchableOpacity>

      <TouchableOpacity
        style={[styles.choiceCard, isBusinessRegistered === false && styles.choiceCardSelected]}
        onPress={() => setIsBusinessRegistered(false)}
      >
        <View style={styles.cardContentVertical}>
          <View style={styles.iconContainer}>
            <Icon icon="notcertified" size={39} color={colors.palette.neutral700} />
            <Text style={styles.cardTitle} text="Entreprise non enregistrée" />
          </View>
          <View style={{}}>
            <Text
              preset="subheading"
              style={styles.cardDescriptionAccent}
              text="Parfait pour les vendeurs individuels, freelances ou petits commerces en démarrage."
            />
            <Text
              style={styles.cardDescription}
              text="Commencez à accepter des paiements et gérer votre activité même sans documents officiels."
            />
          </View>
        </View>
      </TouchableOpacity>

      {/* <TouchableOpacity
        style={[styles.choiceCard, isBusinessRegistered === false && styles.choiceCardSelected]}
        onPress={() => setIsBusinessRegistered(false)}
      >
        <Text style={styles.choiceTitle}>Unregistered Business</Text>
        {isBusinessRegistered === false && (
          <Icon icon="check" size={20} color={colors.palette.accent500} />
        )}
      </TouchableOpacity> */}
    </View>
  )

  const renderStepTwo = () => (
    <View style={styles.stepContent}>
      <FencyTextInput
        value={businessDetails.name}
        onChange={(text: string) => handleInputChange("name", text)}
        style={styles.input}
        inputname={"Nom de l'entreprise"}
        placeholder={"Nom de l'entreprise"}
      />
      {isBusinessRegistered && (
        <FencyTextInput
          value={businessDetails.registration_number}
          onChange={(text: string) => handleInputChange("registration_number", text)}
          style={styles.input}
          inputname={"Numéro national d'identification"}
          placeholder={"Numéro d'enregistrement"}
        />
      )}
      <CustomSelector
        value={businessDetails.city}
        inputname={"Ville d'activité"}
        onValueChange={(text: string) => handleInputChange("city", text)}
        isitems={DRC_cities}
        placeholder={"Ville"}
      />
      <FencyTextInput
        value={businessDetails.adresse}
        onChange={(text: string) => handleInputChange("adresse", text)}
        style={styles.input}
        inputname={"Address"}
        placeholder={"Address"}
      />
      <FencyTextInput
        value={businessDetails.website}
        onChange={(text: string) => handleInputChange("website", text)}
        style={styles.input}
        inputname={"Site web (Optionnel)"}
        placeholder={"www.example.com"}
      />
      <FencyTextInput
        value={businessDetails.description}
        onChange={(text: string) => handleInputChange("description", text)}
        style={styles.input}
        inputname={"Description de l'entreprise "}
        placeholder={"Description"}
      />
      <CategoryDropdown
        value={businessDetails.categories}
        onValueChange={(text: string) => handleInputChange("categories", text)}
        inputname={"Catégories d'entreprises"}
        placeholder={"Sélectionner une catégorie"}
      />
    </View>
  )

  // Pick image from gallery
  const pickImage = async () => {
    try {
      const response = await new Promise<ImagePickerResponse>((resolve) => {
        launchImageLibrary(
          {
            mediaType: "photo",
            includeBase64: false,
            maxHeight: 500,
            maxWidth: 500,
            selectionLimit: 1,
          },
          resolve,
        )
      })

      if (response.didCancel) {
        // console.log("User cancelled image picker")
        return
      }

      if (response.errorCode) {
        console.error("ImagePicker Error:", response.errorMessage)
        Alert.alert("Error", "An error occurred while selecting the image")
        return
      }

      if (response.assets && response.assets[0]?.uri) {
        setSelectedImageUri(response.assets[0].uri)
        setShowImagePreview(true)
      }
    } catch (error) {
      console.error("Error picking image:", error)
      Alert.alert("Error", "An error occurred")
    }
  }

  // Take a picture with camera
  const takePicture = async () => {
    try {
      const response = await new Promise<ImagePickerResponse>((resolve) => {
        launchCamera(
          {
            mediaType: "photo",
            includeBase64: false,
            maxHeight: 500,
            maxWidth: 500,
            saveToPhotos: true,
          },
          resolve,
        )
      })

      if (response.didCancel) {
        console.log("User cancelled camera")
        return
      }

      if (response.errorCode) {
        console.error("Camera Error:", response.errorMessage)
        Alert.alert("Error", "An error occurred while taking the picture")
        return
      }

      if (response.assets && response.assets[0]?.uri) {
        setSelectedImageUri(response.assets[0].uri)
        setShowImagePreview(true)
      }
    } catch (error) {
      console.error("Error taking picture:", error)
      Alert.alert("Error", "An error occurred")
    }
  }

  // Save the selected image
  const handleSaveImage = async () => {
    if (!selectedImageUri) return

    setShowImagePreview(false)
    setIsUploading(true)

    try {
      // Compress the image
      const compressedUri = await compressImage(selectedImageUri, {
        maxWidth: 800,
        quality: 0.7,
      })

      // Update the documents state with the compressed image URI
      setDocuments({
        ...documents,
        businesslogo: compressedUri,
      })

      // Reset the selected image URI
      setSelectedImageUri(null)
    } catch (error) {
      console.error("Error processing image:", error)
      Alert.alert("Error", "An error occurred while processing the image")
    } finally {
      setIsUploading(false)
    }
  }

  const renderStepThree = () => (
    <View style={styles.stepContent}>
      <Text style={styles.stepTitle}>Logo de l&lsquo;entreprise</Text>
      <Text style={styles.stepDescription}>
        Ajoutez le logo de votre entreprise pour rendre votre profil plus professionnel et
        reconnaissable par les clients.
      </Text>

      {documents.businesslogo ? (
        <View style={styles.logoPreviewContainer}>
          <Image
            source={{ uri: documents.businesslogo }}
            style={styles.logoPreview}
            resizeMode="contain"
          />
          <TouchableOpacity
            style={styles.changeLogoButton}
            onPress={() => setShowImageOptions(true)}
          >
            <Text style={styles.changeLogoText}>Changer le logo</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <TouchableOpacity style={styles.uploadLogoBox} onPress={() => setShowImageOptions(true)}>
          <View style={styles.plusIconContainer}>
            <Text style={styles.plusIcon}>+</Text>
          </View>
          <View>
            <Text style={styles.uploadLabel}>Logo de l&lsquo;entreprise</Text>
            <Text style={styles.uploadHint}>Cliquez pour télécharger</Text>
          </View>
        </TouchableOpacity>
      )}

      <Text style={styles.logoTips}>
        Conseils : utilisez une image de haute qualité avec le logo de votre entreprise. Taille
        recommandée : 400x400 pixels.
      </Text>
    </View>
  )

  const renderStepFour = () => (
    <View style={styles.stepContent}>
      <Text style={styles.stepTitle}>Vérifier et soumettre</Text>

      {/* Business Logo */}
      {documents.businesslogo ? (
        <View style={styles.summaryLogoContainer}>
          <Image
            source={{ uri: documents.businesslogo }}
            style={styles.summaryLogo}
            resizeMode="contain"
          />
        </View>
      ) : (
        <View style={styles.noLogoContainer}>
          <Icon icon="store" size={60} color={colors.palette.neutral400} />
          <Text style={styles.noLogoText}>Aucun logo téléchargé</Text>
        </View>
      )}

      {/* Business Details */}
      <View style={styles.summarySection}>
        <Text style={styles.summarySectionTitle}>Vérification et soumission</Text>

        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>Nom de l&lsquo;entreprise :</Text>
          <Text style={styles.summaryValue}>{businessDetails.name}</Text>
        </View>

        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>Type d&rsquo;entreprise :</Text>
          <Text style={styles.summaryValue}>
            {isBusinessRegistered ? "Entreprise enregistrée" : "Entreprise non enregistrée"}
          </Text>
        </View>

        {isBusinessRegistered && (
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>N° d&lsquo;enregistrement :</Text>
            <Text style={styles.summaryValue}>{businessDetails.registration_number}</Text>
          </View>
        )}
      </View>

      {/* Location Details */}
      <View style={styles.summarySection}>
        <Text style={styles.summarySectionTitle}>Localisation</Text>

        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>Pays :</Text>
          <Text style={styles.summaryValue}>{businessDetails.country}</Text>
        </View>

        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>Ville:</Text>
          <Text style={styles.summaryValue}>{businessDetails.city}</Text>
        </View>

        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>Adresse:</Text>
          <Text style={styles.summaryValue}>{businessDetails.adresse}</Text>
        </View>
      </View>

      {/* Additional Information */}
      <View style={styles.summarySection}>
        <Text style={styles.summarySectionTitle}>Informations supplémentaires</Text>

        {businessDetails.website && (
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Site web:</Text>
            <Text style={styles.summaryValue}>{businessDetails.website}</Text>
          </View>
        )}

        {businessDetails.categories && (
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Catégorie:</Text>
            <Text style={styles.summaryValue}>{businessDetails.categories}</Text>
          </View>
        )}

        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>Description:</Text>
          <Text style={styles.summaryValue}>{businessDetails.description}</Text>
        </View>
      </View>
    </View>
  )

  const renderStepContent = (currentStep: number) => {
    switch (currentStep) {
      case 1:
        return renderStepOne()
      case 2:
        return renderStepTwo()
      case 3:
        return renderStepThree()
      case 4:
        return renderStepFour()
      default:
        return null
    }
  }

  const isNextButtonDisabled = () => {
    if (step === 1) {
      return isBusinessRegistered === null ? true : false
    }
    if (step === 2) {
      return (
        !businessDetails.name ||
        !businessDetails.city ||
        !businessDetails.adresse ||
        (isBusinessRegistered && !businessDetails.registration_number)
      )
    }
    if (step === 3) {
      // Only require the business logo
      return !documents.businesslogo
    }
    return false
  }

  // Image Options Modal
  const ImageOptionsModal = () => (
    <Modal
      visible={showImageOptions}
      transparent={true}
      animationType="slide"
      onRequestClose={() => setShowImageOptions(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Choose Image Source</Text>
            <TouchableOpacity onPress={() => setShowImageOptions(false)} style={styles.closeButton}>
              <Icon icon="x" size={24} color={colors.palette.neutral800} />
            </TouchableOpacity>
          </View>

          <View style={styles.imageOptionsContainer}>
            <TouchableOpacity
              style={styles.imageOptionButton}
              onPress={() => {
                setShowImageOptions(false)
                setTimeout(() => takePicture(), 500)
              }}
            >
              <Icon icon="photocamera" size={40} color={colors.palette.primary500} />
              <Text style={styles.imageOptionText}>Prendre une photo</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.imageOptionButton}
              onPress={() => {
                setShowImageOptions(false)
                setTimeout(() => pickImage(), 500)
              }}
            >
              <Icon icon="upload_image" size={40} color={colors.palette.primary500} />
              <Text style={styles.imageOptionText}>Choisir depuis la galerie</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  )

  // Image Preview Modal
  const ImagePreviewModal = () => (
    <Modal
      visible={showImagePreview}
      transparent={true}
      animationType="slide"
      onRequestClose={() => setShowImagePreview(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Aperçu du logo</Text>
            <TouchableOpacity onPress={() => setShowImagePreview(false)} style={styles.closeButton}>
              <Icon icon="x" size={24} color={colors.palette.neutral800} />
            </TouchableOpacity>
          </View>

          <View style={styles.imagePreviewContainer}>
            {selectedImageUri && (
              <Image
                source={{ uri: selectedImageUri }}
                style={styles.previewImage}
                resizeMode="contain"
              />
            )}
          </View>

          <View style={styles.modalActions}>
            <Button
              text="Cancel"
              preset="default"
              onPress={() => {
                setShowImagePreview(false)
                setSelectedImageUri(null)
              }}
              style={styles.modalButton}
            />
            <Button
              text="Save"
              preset="default"
              onPress={handleSaveImage}
              style={[styles.modalButton, styles.saveButton]}
            />
          </View>
        </View>
      </View>
    </Modal>
  )

  return (
    <>
      <Header
        title="Creation d' Entreprise"
        onRightPress={() => setShowMenuPopup(true)}
        rightIcon="menugrid"
        // titleStyle={$headerTitle}
      />
      <View style={styles.root}>
        {/* Step Indicator */}
        {renderStepIndicator()}

        {/* Main Content - Horizontal Scrolling for Steps */}
        <View style={styles.contentContainer}>
          <ScrollView
            ref={scrollViewRef}
            horizontal
            pagingEnabled
            showsHorizontalScrollIndicator={false}
            scrollEnabled={false}
            style={styles.horizontalScrollView}
            contentContainerStyle={{ width: width * 4 }}
          >
            {/* Each step is wrapped in a scrollable container for vertical scrolling */}
            <ScrollView
              style={[styles.stepWrapper, { width }]}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={styles.stepScrollContent}
            >
              {renderStepContent(1)}
            </ScrollView>

            <ScrollView
              style={[styles.stepWrapper, { width }]}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={styles.stepScrollContent}
            >
              {renderStepContent(2)}
            </ScrollView>

            <ScrollView
              style={[styles.stepWrapper, { width }]}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={styles.stepScrollContent}
            >
              {renderStepContent(3)}
            </ScrollView>

            <ScrollView
              style={[styles.stepWrapper, { width }]}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={styles.stepScrollContent}
            >
              {renderStepContent(4)}
            </ScrollView>
          </ScrollView>
        </View>

        {/* Bottom Navigation Buttons */}
        <View style={styles.bottomButtonContainer}>
          {step > 1 && (
            <TouchableOpacity
              style={styles.prevButton}
              onPress={prevStep}
              disabled={isSubmitting}
              activeOpacity={isSubmitting ? 0.5 : 0.2}
            >
              <Icon
                icon="caretLeft"
                size={20}
                color={isSubmitting ? colors.palette.neutral400 : colors.palette.neutral700}
              />
              <Text style={[styles.prevButtonText, isSubmitting && styles.disabledText]}>Back</Text>
            </TouchableOpacity>
          )}
          {step < 4 && (
            <Button
              text="Next"
              onPress={nextStep}
              preset="reversed"
              // style={styles.nextButton}
              style={$buttonStyle}
              disabled={isNextButtonDisabled() === true || isSubmitting}
            />
          )}
          {step === 4 && (
            <Button
              preset="reversed"
              onPress={handleSubmit}
              style={$buttonStyle}
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <View style={styles.loadingContainer}>
                  <ActivityIndicator size="small" color={colors.palette.neutral100} />
                  <Text style={styles.loadingText}>Loading...</Text>
                </View>
              ) : (
                <Text style={{ color: colors.palette.neutral100 }}>Submit</Text>
              )}
            </Button>
          )}
        </View>
      </View>

      {/* Modals */}
      <ImageOptionsModal />
      <ImagePreviewModal />
      <BusinessMenuPopup visible={showMenuPopup} onClose={() => setShowMenuPopup(false)} />
    </>
  )
}

const $buttonStyle: ViewStyle = {
  backgroundColor: colors.palette.neutral900,
  borderRadius: 16,
  flex: 1,
  marginLeft: spacing.md,
  // paddingVertical: 16, // Button preset might handle padding
  width: "100%",
}

const styles = StyleSheet.create({
  root: {
    flex: 1,
    // backgroundColor: colors.background,
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
    padding: spacing.lg,
  },
  modalContent: {
    backgroundColor: colors.background,
    borderRadius: 12,
    width: "100%",
    maxWidth: 500,
    padding: spacing.md,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: spacing.md,
    paddingBottom: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: colors.palette.neutral200,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: colors.palette.neutral900,
  },
  closeButton: {
    padding: spacing.xs,
  },
  imageOptionsContainer: {
    flexDirection: "row",
    justifyContent: "space-around",
    padding: spacing.md,
  },
  imageOptionButton: {
    alignItems: "center",
    padding: spacing.md,
    borderWidth: 1,
    borderColor: colors.palette.neutral300,
    borderRadius: 8,
    width: "45%",
  },
  imageOptionText: {
    marginTop: spacing.sm,
    fontSize: 14,
    color: colors.palette.neutral800,
  },
  imagePreviewContainer: {
    alignItems: "center",
    marginVertical: spacing.lg,
  },
  previewImage: {
    width: 200,
    height: 200,
    borderRadius: 8,
    marginBottom: spacing.md,
  },
  modalActions: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: spacing.md,
  },
  modalButton: {
    flex: 1,
    marginHorizontal: spacing.xs,
  },
  saveButton: {
    backgroundColor: colors.palette.primary500,
  },
  // Header styles
  header: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: spacing.md,
    paddingTop: spacing.xl,
    paddingBottom: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.palette.neutral200,
  },
  headerTextContainer: {
    flex: 1,
    marginLeft: spacing.md,
  },
  backButton: {
    padding: spacing.sm,
  },
  cardContentVertical: {
    flex: 1,
    flexDirection: "column",
    alignItems: "flex-start",
    paddingRight: spacing.sm,
    justifyContent: "center",
    paddingVertical: spacing.md,
  },
  title: {
    fontSize: 20,
    fontWeight: "bold",
    color: colors.palette.neutral900,
  },
  subtitle: {
    fontSize: 14,
    color: colors.palette.neutral700,
    marginTop: spacing.xs,
  },
  // Step indicator styles
  stepIndicatorContainer: {
    flexDirection: "row",
    justifyContent: "space-around",
    alignItems: "center",
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
    // backgroundColor: colors.background,
    borderBottomWidth: 1,
    borderBottomColor: colors.palette.neutral200,
  },
  stepCircle: {
    width: 30,
    height: 30,
    borderRadius: 15,
    borderWidth: 1,
    borderColor: colors.palette.neutral400,
    justifyContent: "center",
    alignItems: "center",
  },
  iconContainer: {
    marginBottom: spacing.md,
    flexDirection: "row",
  },

  cardTitle: {
    fontSize: 16,
    fontWeight: "600",
    paddingHorizontal: spacing.sm,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  cardDescriptionAccent: {
    fontSize: 13,
    fontWeight: "bold",
    color: colors.palette.neutral900,
    lineHeight: 18,
    marginBottom: spacing.xs,
  },
  cardDescription: {
    fontSize: 13,
    color: colors.palette.accent400,
    lineHeight: 18,
  },
  stepActiveCircle: {
    borderColor: colors.palette.neutral900,
    backgroundColor: colors.palette.neutral900,
  },
  stepNumber: {
    fontSize: 14,
    fontWeight: "500",
    color: colors.palette.neutral500,
  },
  stepActiveNumber: {
    color: colors.palette.neutral100,
  },
  // Content container styles
  contentContainer: {
    flex: 1,
  },
  horizontalScrollView: {
    flex: 1,
  },
  stepWrapper: {
    flex: 1,
  },
  stepScrollContent: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    paddingBottom: spacing.xl * 2, // Extra padding at bottom for scrolling
  },
  stepContent: {
    width: "100%",
  },
  stepTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: colors.palette.neutral900,
    marginBottom: spacing.lg,
  },
  // Form element styles
  choiceCard: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    backgroundColor: colors.palette.neutral100,
    borderWidth: 1,
    borderColor: colors.palette.neutral300,
    borderRadius: 8,
    padding: spacing.md,
    marginBottom: spacing.md,
  },
  choiceCardSelected: {
    borderColor: colors.palette.primary500,
    borderWidth: 2,
  },
  choiceTitle: {
    fontSize: 16,
    fontWeight: "500",
    color: colors.palette.neutral900,
  },
  input: {
    // backgroundColor: colors.palette.neutral100,
    // borderWidth: 1,
    // borderColor: colors.palette.neutral300,
    // borderRadius: 8,
    // padding: spacing.md,
    // marginBottom: spacing.md,
    // fontSize: 16,
    // color: colors.palette.neutral800,
  },
  label: {
    fontSize: 14,
    fontWeight: "500",
    color: colors.palette.neutral700,
    marginBottom: spacing.sm,
  },
  uploadBox: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: colors.palette.neutral100,
    borderWidth: 1,
    borderColor: colors.palette.neutral300,
    borderRadius: 8,
    padding: spacing.md,
    marginBottom: spacing.md,
  },
  plusIconContainer: {
    backgroundColor: colors.palette.neutral300,
    borderRadius: 8,
    width: 30,
    height: 30,
    justifyContent: "center",
    alignItems: "center",
    marginRight: spacing.md,
  },
  plusIcon: {
    color: colors.palette.neutral500,
    fontSize: 20,
    fontWeight: "bold",
  },
  uploadLabel: {
    fontSize: 16,
    fontWeight: "500",
    color: colors.palette.neutral900,
  },
  uploadHint: {
    fontSize: 12,
    color: colors.palette.neutral500,
  },
  // Bottom navigation styles
  bottomButtonContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
    borderTopWidth: 1,
    borderTopColor: colors.palette.neutral200,
    // backgroundColor: colors.background,
  },
  prevButton: {
    flexDirection: "row",
    alignItems: "center",
    padding: spacing.sm,
  },
  prevButtonText: {
    fontSize: 16,
    fontWeight: "500",
    color: colors.palette.neutral700,
    marginLeft: spacing.sm,
  },
  disabledText: {
    color: colors.palette.neutral400,
  },
  loadingContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
  },
  loadingText: {
    color: colors.palette.neutral100,
    marginLeft: spacing.xs,
    fontSize: 16,
    fontWeight: "500",
  },
  submitButton: {
    width: "100%",
  },
  confirmationText: {
    fontSize: 16,
    color: colors.palette.neutral800,
    marginBottom: spacing.sm,
  },
  // Logo upload styles
  stepDescription: {
    fontSize: 14,
    color: colors.palette.neutral700,
    marginBottom: spacing.lg,
    lineHeight: 20,
  },
  uploadLogoBox: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: colors.palette.neutral100,
    borderWidth: 1,
    borderColor: colors.palette.neutral300,
    borderRadius: 8,
    padding: spacing.lg,
    marginBottom: spacing.md,
    justifyContent: "center",
  },
  logoPreviewContainer: {
    alignItems: "center",
    marginVertical: spacing.lg,
  },
  logoPreview: {
    width: 150,
    height: 150,
    borderRadius: 75,
    marginBottom: spacing.md,
    borderWidth: 1,
    borderColor: colors.palette.neutral300,
  },
  changeLogoButton: {
    backgroundColor: colors.palette.primary500,
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.md,
    borderRadius: 4,
  },
  changeLogoText: {
    color: colors.palette.neutral100,
    fontSize: 14,
    fontWeight: "500",
  },
  logoTips: {
    fontSize: 12,
    color: colors.palette.neutral600,
    fontStyle: "italic",
    textAlign: "center",
    marginTop: spacing.lg,
  },
  // Summary styles
  summaryLogoContainer: {
    alignItems: "center",
    marginVertical: spacing.lg,
  },
  summaryLogo: {
    width: 100,
    height: 100,
    borderRadius: 50,
    borderWidth: 1,
    borderColor: colors.palette.neutral300,
  },
  noLogoContainer: {
    alignItems: "center",
    marginVertical: spacing.lg,
    backgroundColor: colors.palette.neutral200,
    padding: spacing.lg,
    borderRadius: 8,
  },
  noLogoText: {
    fontSize: 14,
    color: colors.palette.neutral600,
    marginTop: spacing.sm,
  },
  summarySection: {
    marginBottom: spacing.lg,
    backgroundColor: colors.palette.neutral100,
    borderRadius: 8,
    padding: spacing.md,
  },
  summarySectionTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: colors.palette.neutral900,
    marginBottom: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.palette.neutral300,
    paddingBottom: spacing.xs,
  },
  summaryRow: {
    flexDirection: "row",
    marginBottom: spacing.sm,
  },
  summaryLabel: {
    fontSize: 14,
    fontWeight: "500",
    color: colors.palette.neutral700,
    width: "40%",
  },
  summaryValue: {
    fontSize: 14,
    color: colors.palette.neutral900,
    flex: 1,
  },
})

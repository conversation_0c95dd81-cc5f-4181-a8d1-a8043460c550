/* eslint-disable no-restricted-imports */
/* eslint-disable react-native/sort-styles */
/* eslint-disable react-native/no-color-literals */
import React, { FC, useState } from "react"
import { View, StyleSheet, ScrollView, TouchableOpacity, Modal } from "react-native"
import { AppStackScreenProps } from "@/navigators"
import { Screen, Text, Icon, Header } from "@/components"
import { colors, spacing } from "@/theme"

interface BusinessReportsScreenProps extends AppStackScreenProps<"BusinessReports"> {}

// Empty chart component for sales report
const EmptySalesChart = () => {
  const days = ["L", "M", "M", "J", "V", "S", "D"]

  return (
    <View style={styles.chartContainer}>
      <View style={styles.chartContent}>
        {days.map((day, index) => (
          <View key={index} style={styles.chartColumn}>
            <View style={styles.chartBar} />
            <Text style={styles.chartLabel}>{day}</Text>
          </View>
        ))}
      </View>
      <View style={styles.chartFooter}>
        <Text style={styles.chartFooterText}>FC 0.00</Text>
        <Text style={styles.chartFooterText}>Semaine dernière</Text>
      </View>
    </View>
  )
}

// Empty product performance component
const EmptyProductPerformance = () => {
  return (
    <View style={styles.emptyContainer}>
      <Icon icon="reports" size={48} color={colors.palette.neutral300} />
      <Text style={styles.emptyText}>Aucune donnée disponible</Text>
      <Text style={styles.emptySubtext}>
        Les performances de vos produits s&lsquo;afficheront ici
      </Text>
    </View>
  )
}

// Empty profitability report component
const EmptyProfitabilityReport = () => {
  return (
    <View style={styles.emptyContainer}>
      <View style={styles.profitabilityIcons}>
        <Icon
          icon="increase"
          size={32}
          color={colors.palette.neutral300}
          style={styles.profitIcon}
        />
        <Icon icon="decrease" size={32} color={colors.palette.neutral300} />
      </View>
      <Text style={styles.emptyText}>Rapport non disponible</Text>
      <Text style={styles.emptySubtext}>Suivez la rentabilité de votre entreprise ici</Text>
    </View>
  )
}

// Empty sales forecast component
const EmptySalesForecast = () => {
  return (
    <View style={styles.emptyContainer}>
      <Icon icon="report" size={48} color={colors.palette.neutral300} />
      <Text style={styles.emptyText}>Prévisions à venir</Text>
      <Text style={styles.emptySubtext}>
        Les prévisions basées sur vos données de vente s&lsquo;afficheront ici
      </Text>
    </View>
  )
}

// Report card component
interface ReportCardProps {
  title: string
  subtitle?: string
  children: React.ReactNode
  onPress?: () => void
}

const ReportCard: FC<ReportCardProps> = ({ title, subtitle, children, onPress }) => {
  return (
    <TouchableOpacity
      style={styles.card}
      activeOpacity={onPress ? 0.7 : 1}
      onPress={onPress}
      disabled={!onPress}
    >
      <View style={styles.cardHeader}>
        <View>
          <Text style={styles.cardTitle}>{title}</Text>
          {subtitle && <Text style={styles.cardSubtitle}>{subtitle}</Text>}
        </View>
        <Icon icon="caretRight" size={16} color={colors.palette.neutral500} />
      </View>
      <View style={styles.cardContent}>{children}</View>
    </TouchableOpacity>
  )
}

// Fancy Modal Component
interface FancyModalProps {
  visible: boolean
  onClose: () => void
  title: string
}

const FancyModal: FC<FancyModalProps> = ({ visible, onClose, title }) => {
  return (
    <Modal transparent visible={visible} animationType="slide" onRequestClose={onClose}>
      <View style={styles.modalContainer}>
        <TouchableOpacity style={styles.backdrop} activeOpacity={1} onPress={onClose} />
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>{title}</Text>
            <TouchableOpacity onPress={onClose}>
              <Icon icon="x" size={24} color={colors.palette.neutral900} />
            </TouchableOpacity>
          </View>
          <View style={styles.modalBody}>
            <Icon
              icon="info"
              size={48}
              color={colors.palette.neutral900}
              style={styles.modalIcon}
            />
            <Text style={styles.modalMessage}>
              Vos premiers rapports seront disponibles après quelques semaines d’utilisation de
              Fedha. Certains seront générés chaque semaine, d’autres après un mois. Nous serons
              avec vous à chaque étape pour vous aider à tirer le meilleur de vos données. Chaque
              rapport inclura des ressources utiles pour mieux comprendre et optimiser votre
              activité.
            </Text>
          </View>
          <TouchableOpacity style={styles.modalButton} onPress={onClose}>
            <Text style={styles.modalButtonText}>Compris</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  )
}

export const BusinessReportsScreen: FC<BusinessReportsScreenProps> = ({ navigation }) => {
  const [modalVisible, setModalVisible] = useState(false)
  const [modalTitle, setModalTitle] = useState("")

  const handleReportPress = (title: string) => {
    setModalTitle(title)
    setModalVisible(true)
  }

  return (
    <>
      <Header title="Rapports" leftIcon="backicon" onLeftPress={() => navigation.goBack()} />
      <Screen style={styles.root} preset="fixed" statusBarStyle="dark">
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          <ReportCard
            title="Rapports de vente"
            subtitle="Hebdomadaire"
            onPress={() => handleReportPress("Rapports de vente")}
          >
            <EmptySalesChart />
          </ReportCard>

          <ReportCard
            title="Performances des produits"
            onPress={() => handleReportPress("Performances des produits")}
          >
            <EmptyProductPerformance />
          </ReportCard>

          <ReportCard
            title="Rapport de rentabilité"
            onPress={() => handleReportPress("Rapport de rentabilité")}
          >
            <EmptyProfitabilityReport />
          </ReportCard>

          <ReportCard
            title="Prévisions de ventes"
            onPress={() => handleReportPress("Prévisions de ventes")}
          >
            <EmptySalesForecast />
          </ReportCard>
        </ScrollView>

        <FancyModal
          visible={modalVisible}
          onClose={() => setModalVisible(false)}
          title={modalTitle}
        />
      </Screen>
    </>
  )
}

const styles = StyleSheet.create({
  // Modal styles
  modalContainer: {
    alignItems: "center",
    flex: 1,
    justifyContent: "flex-end",
  },
  backdrop: {
    backgroundColor: "rgba(0,0,0,0.5)",
    bottom: 0,
    left: 0,
    position: "absolute",
    right: 0,
    top: 0,
  },
  modalContent: {
    backgroundColor: colors.palette.neutral100,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    elevation: 10,
    maxHeight: "80%",
    paddingBottom: spacing.xl + spacing.lg,
    paddingHorizontal: spacing.lg,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    width: "100%",
  },
  modalHeader: {
    alignItems: "center",
    borderBottomColor: colors.palette.neutral200,
    borderBottomWidth: 1,
    flexDirection: "row",
    justifyContent: "space-between",
    paddingVertical: spacing.md,
  },
  modalTitle: {
    color: colors.palette.neutral900,
    fontSize: 18,
    fontWeight: "600",
  },
  modalBody: {
    alignItems: "center",
    paddingVertical: spacing.xl,
  },
  modalIcon: {
    marginBottom: spacing.md,
  },
  modalMessage: {
    color: colors.palette.neutral800,
    fontSize: 16,
    lineHeight: 24,
    textAlign: "center",
  },
  modalButton: {
    alignItems: "center",
    backgroundColor: colors.palette.neutral900,
    borderRadius: 12,
    marginTop: spacing.lg,
    paddingVertical: spacing.md,
  },
  modalButtonText: {
    color: colors.palette.neutral100,
    fontSize: 16,
    fontWeight: "600",
  },
  root: {
    // backgroundColor: colors.background,
    flex: 1,
  },
  scrollView: {
    // flex: 1,
  },
  scrollContent: {
    paddingBottom: spacing.xl * 2,
    paddingHorizontal: spacing.md,
  },
  card: {
    backgroundColor: colors.palette.neutral100,
    borderRadius: 16,
    elevation: 2,
    marginTop: spacing.md,
    overflow: "hidden",
    shadowColor: colors.palette.neutral900,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
  },
  cardHeader: {
    alignItems: "center",
    borderBottomColor: colors.palette.neutral200,
    borderBottomWidth: 1,
    flexDirection: "row",
    justifyContent: "space-between",
    padding: spacing.md,
  },
  cardTitle: {
    color: colors.palette.neutral900,
    fontSize: 16,
    fontWeight: "600",
  },
  cardSubtitle: {
    color: colors.palette.neutral600,
    fontSize: 12,
    marginTop: spacing.xs / 2,
  },
  cardContent: {
    minHeight: 150,
    padding: spacing.md,
  },
  emptyContainer: {
    alignItems: "center",
    flex: 1,
    justifyContent: "center",
    paddingVertical: spacing.lg,
  },
  emptyText: {
    color: colors.palette.neutral700,
    fontSize: 16,
    fontWeight: "500",
    marginTop: spacing.md,
  },
  emptySubtext: {
    color: colors.palette.neutral600,
    fontSize: 14,
    marginTop: spacing.xs,
    textAlign: "center",
  },
  chartContainer: {
    flex: 1,
    paddingVertical: spacing.sm,
  },
  chartContent: {
    alignItems: "flex-end",
    flexDirection: "row",
    height: 100,
    justifyContent: "space-between",
    marginBottom: spacing.sm,
  },
  chartColumn: {
    alignItems: "center",
    width: 24,
  },
  chartBar: {
    backgroundColor: colors.palette.neutral300,
    borderRadius: 4,
    height: 40,
    marginBottom: spacing.xs,
    width: 8,
  },
  chartLabel: {
    color: colors.palette.neutral600,
    fontSize: 12,
  },
  chartFooter: {
    borderTopColor: colors.palette.neutral200,
    borderTopWidth: 1,
    flexDirection: "row",
    justifyContent: "space-between",
    paddingTop: spacing.xs,
  },
  chartFooterText: {
    color: colors.palette.neutral600,
    fontSize: 12,
  },
  profitabilityIcons: {
    alignItems: "center",
    flexDirection: "row",
  },
  profitIcon: {
    marginRight: spacing.sm,
  },
})

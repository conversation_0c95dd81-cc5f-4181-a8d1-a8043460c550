/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable react-native/no-inline-styles */
import { FC, useEffect, useRef, useState } from "react"
import {
  View,
  Animated,
  Dimensions,
  ViewStyle,
  TouchableOpacity,
  Alert,
  Linking,
} from "react-native"
import { AppStackScreenProps } from "@/navigators"
import {
  Button,
  Icon,
  OtpEntryInput,
  OtpEntryInputRef,
  OtpExpirationTimer,
  Screen,
  Text,
} from "@/components"
import { $Gstyles, colors, OTPScreenstyles, spacing } from "@/theme"
import { useAuthStore } from "@/store/AuthenticationStore"
import { useStores } from "@/store/rootStore"

interface OtpVerficationScreenProps extends AppStackScreenProps<"OtpVerfication"> {}

const screenWidth = Dimensions.get("window").width

export const OtpVerficationScreen: FC<OtpVerficationScreenProps> = ({ navigation }) => {
  const {
    auth: { user, logout, fetchUserData },
  } = useStores()
  const { verifyOtp, resendOtp, otpExpiration } = useAuthStore()
  const [isVerified, setIsVerified] = useState<boolean | null>(null)
  const [otp, setOtp] = useState<number>(0)
  const [errorMessage, setErrorMessage] = useState<string>("")
  const [isResendDisabled, setIsResendDisabled] = useState(false)
  const slideAnim = useRef(new Animated.Value(0)).current
  const otpInputRef = useRef<OtpEntryInputRef>(null)

  // Check if resend is disabled based on otpExpiration
  useEffect(() => {
    if (otpExpiration) {
      const checkExpiration = () => {
        const now = Date.now()
        setIsResendDisabled(now < otpExpiration)
      }

      checkExpiration()
      const interval = setInterval(checkExpiration, 1000)
      return () => clearInterval(interval)
    } else {
      setIsResendDisabled(false)
    }
  }, [otpExpiration])

  useEffect(() => {
    if (!user) {
      fetchUserData()
    }
  }, [fetchUserData, user])

  const handleOtpSubmit = async (pin: any) => {
    console.log(pin)
    if (pin.length === 6) {
      try {
        setErrorMessage("")
        const response = await verifyOtp(pin)

        if (response.success) {
          await fetchUserData()
          setIsVerified(true)
          navigation.navigate("FedhaLoader", { nextScreen: "UserVerification" })
        } else {
          setIsVerified(false)
          setErrorMessage(response.message)
          slideToNextView()
          console.log(response)
        }
      } catch (error: any) {
        setIsVerified(false)
        console.log(error)
        setErrorMessage(error.message || "Une erreur s'est produite lors de la vérification")
        slideToNextView()
      }
    }
  }

  // Slide animation to the next view
  const slideToNextView = () => {
    Animated.timing(slideAnim, {
      toValue: -screenWidth,
      duration: 500,
      useNativeDriver: true,
    }).start()
  }

  // Slide animation back to the OTP input view and clear OTP
  const slideToPreviousView = () => {
    Animated.timing(slideAnim, {
      toValue: 0,
      duration: 500,
      useNativeDriver: true,
    }).start(() => {
      setOtp(0) // Clear OTP state
      otpInputRef.current?.clear() // Clear the OTP input field
    })
  }

  const handleResendOTP = async () => {
    if (isResendDisabled) {
      const remainingTime = otpExpiration ? Math.ceil((otpExpiration - Date.now()) / 1000) : 0
      Alert.alert(
        "Patientez",
        `Veuillez attendre ${remainingTime} secondes avant de demander un nouveau code.`,
      )
      return
    }

    try {
      navigation.navigate("FedhaLoader", { nextScreen: "OtpVerfication" })
      const response = await resendOtp()

      if (response.success) {
        Alert.alert("Succès", "Un nouveau code OTP a été envoyé.")
        otpInputRef.current?.clear()
        setOtp(0)
        setErrorMessage("")
        setIsVerified(null)

        setTimeout(() => {
          navigation.reset({
            index: 0,
            routes: [{ name: "OtpVerfication" }],
          })
        }, 2000)
      } else {
        Alert.alert("Erreur", response.message)
      }
    } catch (error: any) {
      Alert.alert("Erreur", "Impossible d'envoyer un nouveau code OTP.")
    }
  }

  const openWhatsApp = () => {
    // Get current verification step message
    const stepMessage = (() => {
      //  return "Étape: Informations de base"
      return "Étape: Vérification de mon numero de telephone"
    })()

    // Format the message
    const message = `Bonjour, j'ai besoin d'aide avec la vérification de mon compte Fedha.\n${stepMessage}`

    // Replace with your actual WhatsApp number
    const SupportphoneNumber = "+243836803506"

    // Create WhatsApp URL with encoded message
    const url = `https://wa.me/${SupportphoneNumber.replace("+", "")}?text=${encodeURIComponent(message)}`

    Linking.openURL(url)
      .then((supported) => {
        if (supported) {
          return Linking.openURL(url)
        } else {
          alert("WhatsApp n'est pas installé sur votre appareil")
          return Promise.resolve() // Explicitly return a resolved promise
        }
      })
      .catch((err) => console.error("An error occurred", err))
  }

  const GotoFedhaLoader = () => {
    //  this will navigate
    console.log("phoneNumber", user)
  }

  return (
    <Screen
      style={$root}
      preset="scroll"
      safeAreaEdges={["top", "bottom"]}
      StatusBarProps={{
        backgroundColor: colors.palette.neutral900,
      }}
    >
      <View
        style={{
          flexDirection: "row",
          justifyContent: "space-between",
          // alignItems: "center",
          top: spacing.xl,
          // backgroundColor: "#F3F4F6",
          flex: 1,
          // backgroundColor: colors.palette.neutral900,
          // marginBottom: spacing.md,
        }}
      >
        <View style={{ flexDirection: "row", gap: spacing.sm }}>
          <Icon
            icon="whatsapp"
            color={"#128c7e"}
            size={29}
            onPress={openWhatsApp}
            containerStyle={[$Gstyles.ActionIcon]}
          />
        </View>
        <Icon
          icon="logout"
          color={colors.palette.primary200}
          size={25}
          onPress={() => logout()}
          containerStyle={[$Gstyles.ActionIcon, { backgroundColor: colors.palette.primary600 }]}
        />
      </View>
      <View style={OTPScreenstyles.container}>
        <Animated.View
          style={[OTPScreenstyles.animatedContainer, { transform: [{ translateX: slideAnim }] }]}
        >
          <View style={OTPScreenstyles.section}>
            <Icon
              icon={"otp"} // Adjust based on your icon names
              color={colors.palette.primary600}
              size={90}
              containerStyle={{ marginBottom: spacing.xl }}
            />
            {/* OTP Input View */}

            <Text style={OTPScreenstyles.title}>Entrer le code de vérification</Text>
            <Text style={OTPScreenstyles.subtitle}>
              Un code PIN à usage unique a été envoyé au numéro de téléphone
              {` ${user?.phone_number}`} Veuillez saisir les 6 chiffres envoyés.
            </Text>
            <OtpEntryInput
              ref={otpInputRef}
              maxLength={6}
              onPinChange={handleOtpSubmit}
              containerStyle={{ marginBottom: spacing.xl }}
            />
            {/* <Text>{otpExpiration}</Text> */}
            {otpExpiration && <OtpExpirationTimer otpExpiration={otpExpiration} />}

            <TouchableOpacity onPress={handleResendOTP} disabled={isResendDisabled}>
              <Text style={[OTPScreenstyles.resendText, isResendDisabled && { opacity: 0.5 }]}>
                Vous n&#39;avez pas reçu l&#39;OTP ?{" "}
                <Text
                  style={[
                    OTPScreenstyles.resendLink,
                    isResendDisabled && { color: colors.palette.neutral500 },
                  ]}
                >
                  Renvoyer un nouveau code
                </Text>
              </Text>
            </TouchableOpacity>
          </View>
          {/* Success / Failure Message View */}
          {/* Success / Failure Message View */}
          <View style={OTPScreenstyles.section}>
            {isVerified ? (
              <View style={OTPScreenstyles.resultContainer}>
                <Icon
                  icon={"check"}
                  color={colors.palette.primary600}
                  size={90}
                  containerStyle={OTPScreenstyles.successIcon}
                />
                <Text style={OTPScreenstyles.successTitle}>Vérification réussie</Text>
                <Text style={OTPScreenstyles.successSubtitle}>
                  Votre numéro a été vérifié avec succès.
                </Text>
                <Button text="Continuer" style={OTPScreenstyles.button} onPress={GotoFedhaLoader} />
              </View>
            ) : (
              <View style={OTPScreenstyles.resultContainer}>
                <Icon
                  icon={"x"}
                  color={colors.palette.angry500}
                  size={90}
                  containerStyle={OTPScreenstyles.failureIcon}
                />
                <Text style={OTPScreenstyles.failureTitle}>Vérification échouée</Text>
                <Text style={OTPScreenstyles.failureSubtitle}>
                  {errorMessage || "Code OTP invalide"}
                </Text>
                <Button
                  text="Réessayer"
                  style={OTPScreenstyles.button}
                  onPress={slideToPreviousView}
                />
              </View>
            )}
          </View>
        </Animated.View>
      </View>
    </Screen>
  )
}

const $root: ViewStyle = {
  flex: 1,
}

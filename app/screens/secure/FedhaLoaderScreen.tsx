import { FC, useCallback, useEffect, useState } from "react"
import { ViewStyle, Image, ImageStyle, View, Text, TouchableOpacity, TextStyle } from "react-native"
import { AppStackScreenProps } from "@/navigators"
import { FedhaLoader, Screen } from "@/components"
import { useStores } from "@/store/rootStore"
import { colors } from "@/theme"
import NetInfo from "@react-native-community/netinfo"
import { MaterialCommunityIcons } from "@expo/vector-icons"
import { useAuthStore } from "@/store/AuthenticationStore"
import { isFirstTimeLaunch } from "@/utils/onboardingUtils"

interface FedhaLoaderScreenProps extends AppStackScreenProps<"FedhaLoader"> {}

export const FedhaLoaderScreen: FC<FedhaLoaderScreenProps> = ({ navigation, route }) => {
  const { nextScreen } = route.params || {}
  const {
    auth: { isAuthenticated, user_type, has_business, isPhoneVerified, isInfoVerified, user },
    // business: { hasVerifiedBusiness },
  } = useStores()
  const [isImageLoading, setIsImageLoading] = useState(true)
  const [isOnline, setIsOnline] = useState(true)
  const [hasError, setHasError] = useState(false)
  const authStore = useAuthStore()

  const checkNetwork = useCallback(async () => {
    const netInfo = await NetInfo.fetch()
    setIsOnline(netInfo.isConnected ?? false)
    if (netInfo.isConnected) {
      try {
        await authStore.fetchUserData()
        handleNavigation()
      } catch (error) {
        console.error("Failed to fetch user data:", error)
        setHasError(true)
      }
    }
  }, [])

  const handleNavigation = useCallback(() => {
    if (nextScreen) {
      navigation.reset({ index: 0, routes: [{ name: nextScreen as any }] })
    } else if (!isAuthenticated) {
      // Check if this is the first time launching the app
      if (isFirstTimeLaunch()) {
        navigation.reset({ index: 0, routes: [{ name: "Onboarding" }] })
      } else {
        navigation.reset({ index: 0, routes: [{ name: "Welcome" }] })
      }
    } else if (!isPhoneVerified) {
      navigation.reset({ index: 0, routes: [{ name: "OtpVerfication" }] })
    } else if (!isInfoVerified || !user?.is_documents_verified) {
      navigation.reset({ index: 0, routes: [{ name: "UserVerification" }] })
    } else {
      // Handle navigation based on user type and business status
      console.log("Navigation Decision:", {
        user_type,
        user_type_typeof: typeof user_type,
        user_type_exact: JSON.stringify(user_type),
        has_business,
        has_business_typeof: typeof has_business,
        isAuthenticated,
        isPhoneVerified,
        isInfoVerified,
        is_documents_verified: user?.is_documents_verified,
      })

      if (user_type === "standard") {
        // Standard users go to regular TabNav
        console.log("Navigating to TabNav (standard user)")
        navigation.reset({ index: 0, routes: [{ name: "TabNav" }] })
      } else if (user_type?.toLowerCase() === "business") {
        // Business users need additional verification (case-insensitive)
        console.log(`Business user detected (user_type: "${user_type}")`)
        if (has_business) {
          // Business user with verified business -> BusinessTabNav
          console.log("Navigating to BusinessTabNav (business user with business)")
          navigation.reset({ index: 0, routes: [{ name: "BusinessTabNav" }] })
        } else {
          // Business user without business -> BusinessOnboarding
          console.log("Navigating to BusinessOnboarding (business user without business)")
          navigation.reset({ index: 0, routes: [{ name: "BusinessOnboarding" }] })
        }
      } else {
        // Fallback for any other user types - default to standard user flow
        console.log(`Unknown user_type: "${user_type}", defaulting to standard user flow`)
        navigation.reset({ index: 0, routes: [{ name: "TabNav" }] })
      }
    }
  }, [
    navigation,
    nextScreen,
    isAuthenticated,
    isPhoneVerified,
    isInfoVerified,
    user,
    user_type,
    has_business,
  ])

  // useEffect(() => {
  //   // Only show modal if not on BusinessOnboarding screen and has no business
  //   if (!has_business && route.name !== "BusinessOnboarding") {
  //     setIsBusinessModalVisible(true)
  //   } else {
  //     setIsBusinessModalVisible(false)
  //     fetchProducts()
  //   }
  // }, [fetchProducts, has_business, route.name])

  useEffect(() => {
    checkNetwork()
    const unsubscribe = NetInfo.addEventListener((state) => {
      setIsOnline(state.isConnected ?? false)
      if (state.isConnected) {
        checkNetwork()
      }
    })

    return () => {
      unsubscribe()
    }
  }, [checkNetwork])

  const handleRetry = () => {
    setHasError(false)
    checkNetwork()
  }

  if (hasError) {
    return (
      <Screen
        style={$root}
        preset="fixed"
        StatusBarProps={{
          backgroundColor: colors.palette.neutral900,
        }}
      >
        <View style={$errorContainer}>
          <MaterialCommunityIcons name="alert-circle" size={50} color={colors.palette.angry500} />
          <Text style={$errorText}>Failed to Load User Data</Text>
          <Text style={$errorSubText}>
            We couldn&lsquo;t load your profile information. Please try again.
          </Text>
          <TouchableOpacity style={$retryButton} onPress={handleRetry}>
            <MaterialCommunityIcons name="refresh" size={24} color={colors.palette.neutral100} />
            <Text style={$retryText}>Retry</Text>
          </TouchableOpacity>
        </View>
      </Screen>
    )
  }

  if (!isOnline) {
    return (
      <Screen
        style={$root}
        preset="fixed"
        StatusBarProps={{
          backgroundColor: colors.palette.neutral900,
        }}
      >
        <View style={$offlineContainer}>
          <MaterialCommunityIcons name="wifi-off" size={50} color={colors.palette.neutral400} />
          <Text style={$offlineText}>No Internet Connection</Text>
          <Text style={$offlineSubText}>Please check your connection and try again</Text>
          <TouchableOpacity style={$retryButton} onPress={handleRetry}>
            <MaterialCommunityIcons name="refresh" size={24} color={colors.palette.neutral100} />
            <Text style={$retryText}>Retry</Text>
          </TouchableOpacity>
        </View>
      </Screen>
    )
  }

  return (
    <Screen
      style={$root}
      preset="fixed"
      StatusBarProps={{
        backgroundColor: colors.palette.neutral900,
      }}
    >
      {isImageLoading ? (
        <FedhaLoader size={280} />
      ) : (
        <Image
          source={require("../../../assets/annimations/fedhalogo.gif")}
          style={$animationStyle}
          onLoadStart={() => setIsImageLoading(true)}
          onLoadEnd={() => setIsImageLoading(false)}
        />
      )}
    </Screen>
  )
}

const $root: ViewStyle = {
  flex: 1,
  justifyContent: "center",
  alignItems: "center",
}

const $animationStyle: ImageStyle = {
  width: 400,
  height: 400,
  resizeMode: "contain",
}

const $offlineContainer: ViewStyle = {
  flex: 1,
  justifyContent: "center",
  alignItems: "center",
  padding: 20,
}

const $errorContainer: ViewStyle = {
  flex: 1,
  justifyContent: "center",
  alignItems: "center",
  padding: 20,
}

const $offlineText: TextStyle = {
  fontSize: 24,
  fontWeight: "bold",
  color: colors.palette.neutral100,
  marginTop: 16,
  textAlign: "center",
}

const $errorText: TextStyle = {
  fontSize: 24,
  fontWeight: "bold",
  color: colors.palette.angry500,
  marginTop: 16,
  textAlign: "center",
}

const $offlineSubText: TextStyle = {
  fontSize: 16,
  color: colors.palette.neutral400,
  marginTop: 8,
  textAlign: "center",
}

const $errorSubText: TextStyle = {
  fontSize: 16,
  color: colors.palette.neutral400,
  marginTop: 8,
  textAlign: "center",
}

const $retryButton: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  backgroundColor: colors.palette.primary500,
  paddingHorizontal: 24,
  paddingVertical: 12,
  borderRadius: 8,
  marginTop: 24,
}

const $retryText: TextStyle = {
  color: colors.palette.neutral100,
  fontSize: 16,
  fontWeight: "600",
  marginLeft: 8,
}

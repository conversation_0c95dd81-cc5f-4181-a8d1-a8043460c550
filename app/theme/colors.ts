const palette = {
  neutral100: "#FFFFFF",
  neutral200: "#F8FAFA",
  neutral300: "#DDEAEA",
  neutralBg: "#f0f8f8",
  neutral400: "#A9C9C9",
  neutral500: "#8CBABA",
  neutral600: "#6CA5A5",
  neutral700: "#498D8D",
  neutral800: "#082c2c",
  neutral900: "#0D5152",

  primary100: "#E5F5F5",
  primary200: "#D0EAEA",
  primary300: "#A0CDCD",
  primary400: "#70B1B1",
  primary500: "#03A6A6",
  primary600: "#028989",

  secondary100: "#FFEDEB",
  secondary200: "#FFD5CF",
  secondary300: "#F2A79B",
  secondsMain: "#F27457",
  secondary500: "#D35D43",
  secondary400: "#d9d9a9",

  terciary100: "#929898",
  cardBg: "#f5f4f0",
  terciary200: "#f0f0f0",
  accent100: "#F6F8F8",
  accent200: "#EFF0F0",
  accent300: "#D7DBDB",
  accent400: "#9EAFAF",
  accent500: "#697979",

  angry100: "#F2D6CD",
  angry500: "#C03403",

  Black: "#000a0a",

  overlay20: "rgba(25, 16, 21, 0.2)",
  overlay50: "rgba(25, 16, 21, 0.5)",
} as const

export const colors = {
  /**
   * The palette is available to use, but prefer using the name.
   * This is only included for rare, one-off cases. Try to use
   * semantic names as much as possible.
   */
  palette,
  /**
   * A helper for making something see-thru.
   */
  transparent: "rgba(0, 0, 0, 0)",
  /**
   * The default text color in many components.
   */
  text: palette.neutral800,
  /**
   * Secondary text information.
   */
  textDim: palette.neutral600,
  /**
   * The default color of the screen background.
   */
  background: palette.accent200,
  /**
   * The default border color.
   */
  border: palette.accent300,
  /**
   * The main tinting color.
   */
  tint: palette.neutral900,
  /**
   * The inactive tinting color.
   */
  tintInactive: palette.terciary100,
  /**
   * A subtle color used for lines.
   */
  separator: palette.neutral300,
  /**
   * Error messages.
   */
  error: palette.angry500,
  /**
   * Error Background.
   */
  errorBackground: palette.angry100,
} as const

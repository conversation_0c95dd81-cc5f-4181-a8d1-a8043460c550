/* eslint-disable react-native/sort-styles */
/* eslint-disable react-native/no-color-literals */
import { ViewStyle, StyleSheet, Dimensions } from "react-native"
import { colors } from "./colors"
import { spacing } from "./spacing"
import { typography } from "./typography"

/* Use this file to define styles that are used in multiple places in your app. */
export const $Gstyles = {
  row: { flexDirection: "row" } as ViewStyle,
  flex1: { flex: 1 } as ViewStyle,
  flexWrap: { flexWrap: "wrap" } as ViewStyle,

  buttonStyle: {
    backgroundColor: colors.palette.neutral900, // Example: Green
    borderRadius: 14,
    marginVertical: 15,
    paddingVertical: 15,
    flex: 1,
    width: "100%", // Full width
  } as ViewStyle,
  serviceName: { fontSize: 18 },
  boxcomp: {
    flexDirection: "row",
    marginVertical: 10,
    borderWidth: 1,
    borderColor: colors.palette.accent300,
    alignContent: "center",
    borderRadius: 15,
    padding: 20,
  } as ViewStyle,

  ActionIcon: {
    padding: 5,
    marginRight: 20,
    alignItems: "center",
    justifyContent: "center",
    alignContent: "center",
    // backgroundColor: colors.palette.neutral300,
    borderRadius: 50,
  } as ViewStyle,

  toggleInner: {
    width: "100%",
    height: "100%",
    alignItems: "center",
    justifyContent: "center",
    overflow: "hidden",
  } as ViewStyle,
}

export const OTPScreenstyles = StyleSheet.create({
  animatedContainer: {
    flexDirection: "row",
    width: Dimensions.get("window").width * 2,
  },
  button: {
    backgroundColor: colors.palette.neutral100,
    borderRadius: 15,
    paddingHorizontal: 40,
    paddingVertical: 12,
  },
  container: {
    top: spacing.xxl + spacing.xxl,
    // backgroundColor: "#F3F4F6",
    flex: 1,
    // justifyContent: "center",
  },
  failureIcon: {
    color: "#FF5252",
    fontSize: 50,
    marginBottom: 20,
  },
  failureSubtitle: {
    color: "#555",
    fontSize: 16,
    marginBottom: 30,
    textAlign: "center",
  },
  failureTitle: {
    color: "#FF5252",
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 10,
  },
  otpInput: {
    marginBottom: 20,
  },
  otpTextInput: {
    color: "#333",
    fontSize: 24,
    fontWeight: "bold",
  },
  resendLink: {
    color: colors.palette.primary600,
    fontWeight: "bold",
  },
  resendText: {
    color: "#555",
    fontSize: 14,
    marginTop: 10,
  },
  resultContainer: {
    alignItems: "center",
    justifyContent: "center",
    padding: 20,
  },
  section: {
    alignItems: "center",
    justifyContent: "center",
    padding: 20,
    width: Dimensions.get("window").width,
  },
  subtitle: {
    color: "#555",
    fontSize: 14,
    marginBottom: 30,
    textAlign: "center",
  },
  successIcon: {
    fontSize: 50,
    marginBottom: 20,
  },
  successSubtitle: {
    color: "#555",
    fontSize: 16,
    marginBottom: 30,
    textAlign: "center",
  },
  successTitle: {
    color: "#4CAF50",
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 10,
  },
  title: {
    color: "#333",
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 10,
  },
})

export const UserVerificationStyles = StyleSheet.create({
  button: {
    borderRadius: 50,
    borderWidth: 1,
    color: colors.palette.neutral800,
    marginTop: 10,
  },

  title: {
    marginBottom: spacing.sm,
    fontFamily: typography.primary.bold,
  },

  header: {
    marginBottom: 16,
  },
  heading: {
    fontWeight: "bold",
    marginBottom: 8,
  },
  progressBar: {
    borderRadius: 3,
    height: 3,
  },
  sectionHeader: {
    color: "#666666",
    fontWeight: "bold",
    marginBottom: 8,
    marginTop: 16,
  },
  serviceName: { fontSize: 18, fontWeight: "bold" },
  subHeading: {
    color: "#666666",
    marginBottom: 8,
  },
  select: {
    backgroundColor: "#fff",
    padding: 10,
    borderRadius: 5,
    marginBottom: spacing.xs,
  },
  input: {
    marginBottom: spacing.xs,
  },
  activeStepIndicator: {
    backgroundColor: "#000",
  },
  stepIndicator: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: "#ccc",
    marginHorizontal: 5,
  },

  ItemIconButton: {
    padding: 15,
    marginVertical: 20,
    alignItems: "center",
    justifyContent: "center",
    alignContent: "center",
    // backgroundColor: colors.palette.neutral300,
    borderRadius: 10,
  } as ViewStyle,
})

export const VerifUserBasicInfoStyle = StyleSheet.create({
  backButton: {
    backgroundColor: "#ddd",
    borderRadius: 8,
    flex: 1,
    marginRight: 10,
    padding: 10,
  },
  buttonText: {
    color: "#fff",
    textAlign: "center",
  },
  cameraButton: {
    alignItems: "center",
    backgroundColor: "#007BFF",
    borderRadius: 8,
    // marginBottom: 15,
    padding: 10,
  },
  container: {
    alignItems: "center",
    backgroundColor: "#fff",
    // flex: 1,
    justifyContent: "center",
    padding: 20,
  },
  datePickerButton: {
    alignItems: "center",
    backgroundColor: "#4CAF50",
    borderRadius: 8,
    padding: 10,
  },
  formContainer: {
    alignItems: "center",
    justifyContent: "center",
    // maxWidth: 400,
    // flex: 1,
    width: "100%",
  },
  input: {
    borderColor: "#ddd",
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 15,
    padding: 10,
    width: "100%",
  },
  label: {
    fontSize: 16,
    marginBottom: 5,
    textAlign: "left",
  },
  navigation: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 20,
    // maxWidth: 400,
    width: "100%",
  },
  nextButton: {
    alignItems: "center",
    backgroundColor: colors.palette.primary600,
    borderRadius: 15,
    flex: 1,
    marginBottom: spacing.xs,
    paddingVertical: spacing.lg,
  },
  profileImage: {
    borderRadius: 80,
    height: 140,
    marginBottom: 15,
    padding: 10,
    width: 140,
    justifyContent: "center",
    overflow: "hidden",
    alignContent: "center",
    alignItems: "center",
    borderWidth: 2,
    borderColor: colors.palette.neutral900,
  },
  stepContainer: {
    alignItems: "center",
    justifyContent: "center",
    width: "100%",
  },
  uploadButton: {
    alignItems: "center",
    backgroundColor: "#4CAF50",
    borderRadius: 8,
    marginBottom: 10,
    padding: 10,
  },
  uploadText: {
    color: "#fff",
    fontSize: 14,
  },
})

export const Recevoirstyles = StyleSheet.create({
  screen: {
    flex: 1,
    backgroundColor: colors.palette.neutral100,
  },
  tabBar: {
    backgroundColor: colors.palette.neutral200,
    borderBottomWidth: 1,
    borderBottomColor: colors.palette.neutral300,
  },
  messageContainer: {
    marginTop: 16,
    padding: 10,
    backgroundColor: "#e0ffe0",
    borderRadius: 5,
  },
  button: {
    alignItems: "center",
    borderRadius: 8,
    color: colors.palette.neutral800,
    paddingVertical: spacing.lg,
  },
  ProofTImage: {
    // borderRadius: 50,
    top: 10,
    height: 60,
    // marginBottom: 15,
    width: 80,
  },
  tabContent: {
    // paddingHorizontal: spacing.lg,
    // flex: 1,
    paddingVertical: spacing.md,
  },
  walletRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: spacing.xs,
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: colors.palette.neutral300,
  },
  walletLeft: {
    flexDirection: "row",
    alignItems: "center",
  },
  walletName: {
    marginLeft: spacing.sm,
    fontSize: 16,
    color: colors.palette.neutral900,
  },
  walletRight: {
    flexDirection: "row",
    alignItems: "center",
  },
  walletNumber: {
    marginRight: spacing.sm,
    fontSize: 14,
    color: colors.palette.neutral700,
  },
  additionalBox: {
    borderWidth: 1,
    borderColor: colors.palette.accent300,
    borderRadius: 15,
    padding: spacing.md,
    alignItems: "center",
    marginVertical: spacing.lg,
  },
  sectionTitle: {
    fontSize: 16,
    color: colors.palette.neutral900,
    marginBottom: spacing.sm,
    textAlign: "center",
  },
  qrImage: {
    width: 150,
    height: 150,
    marginVertical: spacing.md,
  },
  IcoImage: {
    width: 35,
    height: 35,
    // marginVertical: spacing.md,
  },
  walletId: {
    fontSize: 14,
    color: colors.palette.neutral700,
  },
})

# First-Time App Launch Detection & Onboarding Flow

## Overview

The app now includes a robust first-time launch detection system that automatically shows the onboarding screen only once when users first install and open the Fedha app. The state is saved in local storage to prevent showing onboarding on subsequent app launches.

## 🎯 How It Works

### Flow Diagram
```
App Launch → FedhaLoaderScreen → Check Storage → First Time? 
                                                      ↓
                                               Yes → Onboarding → Welcome
                                                      ↓
                                               No → Welcome/Login
```

### Key Components

1. **FedhaLoaderScreen** - Entry point that checks first-time status
2. **OnboardingScreen** - Full-screen image carousel shown once
3. **OnboardingUtils** - Utility functions for state management
4. **MMKV Storage** - Persistent storage for onboarding state

## 📱 Implementation Details

### Storage Key
```typescript
const FIRST_TIME_LAUNCH_KEY = "hasSeenOnboarding"
```

### Utility Functions

#### `isFirstTimeLaunch(): boolean`
- Checks if this is the first time the app is launched
- Returns `true` if onboarding should be shown
- Returns `false` if user has already seen onboarding

#### `markOnboardingCompleted(): boolean`
- Marks onboarding as completed in storage
- Called when user finishes onboarding flow
- Returns `true` if successfully saved

#### `resetOnboardingState(): void`
- Resets onboarding state (useful for testing)
- Removes the storage key
- Next app launch will show onboarding again

#### `getOnboardingState(): "completed" | "pending"`
- Returns current onboarding state
- Useful for debugging and analytics

## 🔄 Navigation Flow

### First-Time Users
1. **App Launch** → FedhaLoaderScreen
2. **Check Storage** → No "hasSeenOnboarding" key found
3. **Navigate** → OnboardingScreen
4. **User Experience** → Full-screen image carousel
5. **Completion** → Save "hasSeenOnboarding" = "true"
6. **Navigate** → WelcomeScreen

### Returning Users
1. **App Launch** → FedhaLoaderScreen
2. **Check Storage** → "hasSeenOnboarding" = "true" found
3. **Navigate** → WelcomeScreen (skip onboarding)

## 🛠️ Technical Implementation

### FedhaLoaderScreen Changes
```typescript
import { isFirstTimeLaunch } from "@/utils/onboardingUtils"

const handleNavigation = useCallback(() => {
  if (nextScreen) {
    navigation.reset({ index: 0, routes: [{ name: nextScreen as any }] })
  } else if (!isAuthenticated) {
    // Check if this is the first time launching the app
    if (isFirstTimeLaunch()) {
      navigation.reset({ index: 0, routes: [{ name: "Onboarding" }] })
    } else {
      navigation.reset({ index: 0, routes: [{ name: "Welcome" }] })
    }
  }
  // ... rest of navigation logic
}, [navigation, nextScreen, isAuthenticated, /* other deps */])
```

### OnboardingScreen Changes
```typescript
import { markOnboardingCompleted } from "@/utils/onboardingUtils"

const completeOnboarding = useCallback(() => {
  markOnboardingCompleted()
  navigation.replace("Welcome")
}, [navigation])
```

## 📊 Storage Details

### Technology Used
- **MMKV** - Fast, efficient key-value storage
- **Synchronous** - No async/await needed
- **Persistent** - Survives app restarts and updates
- **Secure** - Data stored locally on device

### Storage Location
- **iOS**: App's Documents directory
- **Android**: App's internal storage
- **Key**: `"hasSeenOnboarding"`
- **Value**: `"true"` (string)

## 🧪 Testing & Debugging

### Manual Testing
1. **Fresh Install**: Delete app, reinstall → Should show onboarding
2. **Subsequent Launches**: Close and reopen app → Should skip onboarding
3. **App Updates**: Update app → Should skip onboarding (state preserved)

### Reset for Testing
```typescript
import { resetOnboardingState } from "@/utils/onboardingUtils"

// Call this to reset onboarding state
resetOnboardingState()
// Next app launch will show onboarding again
```

### Debug Information
```typescript
import { getOnboardingState, isFirstTimeLaunch } from "@/utils/onboardingUtils"

console.log("Onboarding State:", getOnboardingState())
console.log("Is First Time:", isFirstTimeLaunch())
```

## 🔧 Configuration

### Customizing Behavior

#### Change Storage Key
```typescript
// In onboardingUtils.ts
const FIRST_TIME_LAUNCH_KEY = "your_custom_key"
```

#### Add Version-Based Onboarding
```typescript
// Show onboarding for major app updates
const APP_VERSION_KEY = "lastSeenOnboardingVersion"
const CURRENT_VERSION = "2.0.0"

export function shouldShowOnboarding(): boolean {
  const lastVersion = loadString(APP_VERSION_KEY)
  return lastVersion !== CURRENT_VERSION
}
```

## 📈 Analytics Integration

### Track Onboarding Events
```typescript
// In OnboardingScreen
const completeOnboarding = useCallback(() => {
  // Analytics tracking
  analytics.track('onboarding_completed', {
    slides_viewed: currentIndex + 1,
    completion_time: Date.now() - startTime,
  })
  
  markOnboardingCompleted()
  navigation.replace("Welcome")
}, [navigation])
```

### Track Skip Events
```typescript
const handleSkip = useCallback(() => {
  analytics.track('onboarding_skipped', {
    slide_index: currentIndex,
    skip_time: Date.now() - startTime,
  })
  
  markOnboardingCompleted()
  navigation.replace("Welcome")
}, [navigation, currentIndex])
```

## 🚨 Error Handling

### Storage Failures
- Functions return `false` on storage write failures
- App continues to function even if storage fails
- Onboarding may show again if storage write fails

### Network Independence
- Onboarding state is stored locally
- No network required for first-time detection
- Works offline

## 🔄 Migration & Updates

### App Updates
- Onboarding state persists through app updates
- Users won't see onboarding again after updates
- Consider version-based onboarding for major changes

### Data Migration
- Current implementation is backward compatible
- No migration needed for existing users
- New users automatically get the new flow

## 📝 Best Practices

1. **Keep It Simple**: Onboarding should be quick and valuable
2. **Test Thoroughly**: Test fresh installs and updates
3. **Monitor Analytics**: Track completion and skip rates
4. **Respect User Choice**: Don't force onboarding if skipped
5. **Version Considerations**: Plan for future onboarding updates

## 🐛 Troubleshooting

### Common Issues

#### Onboarding Shows Every Time
- Check if `markOnboardingCompleted()` is being called
- Verify storage permissions
- Check for storage write failures

#### Onboarding Never Shows
- Verify `isFirstTimeLaunch()` logic
- Check navigation conditions in FedhaLoaderScreen
- Ensure OnboardingScreen is in navigation stack

#### Storage Issues
- Check MMKV installation and setup
- Verify storage permissions on device
- Test on different devices and OS versions

### Debug Commands
```typescript
// Check current state
console.log("First time:", isFirstTimeLaunch())
console.log("State:", getOnboardingState())

// Reset for testing
resetOnboardingState()

// Force onboarding (for testing)
remove("hasSeenOnboarding")
```

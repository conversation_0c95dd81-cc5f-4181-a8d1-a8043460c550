import React, { useState } from "react"
import { View, StyleSheet } from "react-native"
import { Text, Button, CustomAlert } from "./app/components"
import { createNetworkAlert } from "./app/utils/alertUtils"

// Example of how to use CustomAlert with network icon
export const NetworkAlertExample = () => {
  const [alertConfig, setAlertConfig] = useState<{
    visible: boolean
    type: "error" | "success" | "warning" | "info" | "network"
    title: string
    message: string
    buttons: any[]
  }>({
    visible: false,
    type: "info",
    title: "",
    message: "",
    buttons: [],
  })

  // Example 1: Simple network alert with just OK button
  const showSimpleNetworkAlert = () => {
    const networkConfig = createNetworkAlert()
    setAlertConfig({
      visible: true,
      ...networkConfig,
      buttons: [
        {
          text: "OK",
          style: "default",
          onPress: () => setAlertConfig(prev => ({ ...prev, visible: false })),
        },
      ],
    })
  }

  // Example 2: Network alert with retry functionality
  const showNetworkAlertWithRetry = () => {
    const networkConfig = createNetworkAlert(
      "Impossible de charger les données. Vérifiez votre connexion internet.",
      () => {
        // Retry logic here
        console.log("Retrying network request...")
        setAlertConfig(prev => ({ ...prev, visible: false }))
        // Add your retry logic here (e.g., refetch data)
      },
      () => setAlertConfig(prev => ({ ...prev, visible: false }))
    )
    
    setAlertConfig({
      visible: true,
      ...networkConfig,
    })
  }

  // Example 3: Custom network alert with specific message
  const showCustomNetworkAlert = () => {
    setAlertConfig({
      visible: true,
      type: "network",
      title: "Connexion lente",
      message: "La connexion semble lente. Voulez-vous continuer à attendre ou réessayer ?",
      buttons: [
        {
          text: "Attendre",
          style: "cancel",
          onPress: () => setAlertConfig(prev => ({ ...prev, visible: false })),
        },
        {
          text: "Réessayer",
          style: "default",
          onPress: () => {
            console.log("Retrying with custom logic...")
            setAlertConfig(prev => ({ ...prev, visible: false }))
          },
        },
      ],
    })
  }

  // Example 4: Network alert for offline mode
  const showOfflineAlert = () => {
    setAlertConfig({
      visible: true,
      type: "network",
      title: "Mode hors ligne",
      message: "Vous êtes actuellement hors ligne. Certaines fonctionnalités peuvent ne pas être disponibles.",
      buttons: [
        {
          text: "Compris",
          style: "default",
          onPress: () => setAlertConfig(prev => ({ ...prev, visible: false })),
        },
      ],
    })
  }

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Network Alert Examples</Text>
      
      <Text style={styles.description}>
        Examples of using CustomAlert with the "faildwifi" (network) icon for different network-related scenarios.
      </Text>

      <View style={styles.buttonContainer}>
        <Button
          text="Simple Network Alert"
          onPress={showSimpleNetworkAlert}
          style={[styles.button, styles.networkButton]}
        />

        <Button
          text="Network Alert with Retry"
          onPress={showNetworkAlertWithRetry}
          style={[styles.button, styles.networkButton]}
        />

        <Button
          text="Custom Network Message"
          onPress={showCustomNetworkAlert}
          style={[styles.button, styles.networkButton]}
        />

        <Button
          text="Offline Mode Alert"
          onPress={showOfflineAlert}
          style={[styles.button, styles.networkButton]}
        />
      </View>

      <View style={styles.codeContainer}>
        <Text style={styles.codeTitle}>Usage Examples:</Text>
        
        <Text style={styles.codeLabel}>1. Simple Network Alert:</Text>
        <Text style={styles.code}>
{`const networkConfig = createNetworkAlert()
setAlertConfig({
  visible: true,
  ...networkConfig,
})`}
        </Text>

        <Text style={styles.codeLabel}>2. With Retry Button:</Text>
        <Text style={styles.code}>
{`const networkConfig = createNetworkAlert(
  "Custom message",
  onRetry,
  onCancel
)`}
        </Text>

        <Text style={styles.codeLabel}>3. Manual Configuration:</Text>
        <Text style={styles.code}>
{`setAlertConfig({
  visible: true,
  type: "network",
  title: "Custom Title",
  message: "Custom message",
  buttons: [...]
})`}
        </Text>
      </View>

      {/* Custom Alert Component */}
      <CustomAlert
        visible={alertConfig.visible}
        type={alertConfig.type}
        title={alertConfig.title}
        message={alertConfig.message}
        buttons={alertConfig.buttons}
        onClose={() => setAlertConfig(prev => ({ ...prev, visible: false }))}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: "#f5f5f5",
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    textAlign: "center",
    marginBottom: 16,
    color: "#333",
  },
  description: {
    fontSize: 16,
    textAlign: "center",
    marginBottom: 24,
    color: "#666",
    lineHeight: 24,
  },
  buttonContainer: {
    marginBottom: 24,
  },
  button: {
    marginBottom: 12,
  },
  networkButton: {
    backgroundColor: "#9b59b6",
  },
  codeContainer: {
    backgroundColor: "white",
    padding: 16,
    borderRadius: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  codeTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 12,
    color: "#333",
  },
  codeLabel: {
    fontSize: 14,
    fontWeight: "600",
    marginTop: 12,
    marginBottom: 4,
    color: "#555",
  },
  code: {
    fontSize: 12,
    fontFamily: "monospace",
    backgroundColor: "#f8f8f8",
    padding: 8,
    borderRadius: 4,
    color: "#333",
  },
})
